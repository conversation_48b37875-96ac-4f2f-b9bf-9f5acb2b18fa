const { MongoClient } = require('mongodb');

async function clearDatabase() {
  // Use the MongoDB URI from your .env.local file
  const MONGODB_URI = 'mongodb+srv://saimmanchester12121212:<EMAIL>/streamzen?retryWrites=true&w=majority&appName=Cluster0';
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db();
    
    // Clear all collections
    const collections = ['movies', 'series', 'episodes'];
    
    for (const collectionName of collections) {
      const result = await db.collection(collectionName).deleteMany({});
      console.log(`🗑️ Cleared ${collectionName}: ${result.deletedCount} documents deleted`);
    }
    
    console.log('✅ Database cleared successfully!');
    
  } catch (error) {
    console.error('❌ Error clearing database:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

clearDatabase();
