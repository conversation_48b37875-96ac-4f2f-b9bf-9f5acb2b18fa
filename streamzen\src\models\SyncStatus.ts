import mongoose, { Schema, Document } from 'mongoose';

export interface ISyncStatus extends Document {
  syncType: string;
  lastSyncTime?: Date;
  nextSyncTime: Date;
  isRunning: boolean;
  lastSyncResults?: {
    movies?: number;
    series?: number;
    episodes?: number;
  };
  lastResult?: any; // For storing detailed sync results
  createdAt: Date;
  updatedAt: Date;
}

const SyncStatusSchema: Schema = new Schema({
  syncType: {
    type: String,
    required: true
  },
  lastSyncTime: {
    type: Date,
    default: null
  },
  nextSyncTime: {
    type: Date,
    required: true
  },
  isRunning: {
    type: Boolean,
    default: false
  },
  lastSyncResults: {
    movies: {
      type: Number,
      default: 0
    },
    series: {
      type: Number,
      default: 0
    },
    episodes: {
      type: Number,
      default: 0
    }
  },
  lastResult: {
    type: Schema.Types.Mixed,
    default: null
  }
}, {
  timestamps: true
});

// Index for querying sync status
SyncStatusSchema.index({ createdAt: -1 });
SyncStatusSchema.index({ nextSyncTime: 1, isRunning: 1 });
SyncStatusSchema.index({ syncType: 1 });
SyncStatusSchema.index({ syncType: 1, nextSyncTime: 1 });

export default mongoose.models.SyncStatus || mongoose.model<ISyncStatus>('SyncStatus', SyncStatusSchema);
