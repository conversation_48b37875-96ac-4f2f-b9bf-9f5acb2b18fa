import axios from 'axios';
import * as cheerio from 'cheerio';
import ContentService from './contentService';
import connectDB from './mongodb';
import Movie from '@/models/Movie';
import Series from '@/models/Series';
import Episode from '@/models/Episode';
import IMDbScraper from './scraper';

interface VidSrcMovie {
  imdb_id: string;
  tmdb_id?: string;
  title: string;
  embed_url: string;
  embed_url_tmdb?: string;
  quality?: string;
}

interface VidSrcSeries {
  imdb_id: string;
  tmdb_id?: string;
  title: string;
  embed_url: string;
  embed_url_tmdb?: string;
}

interface VidSrcEpisode {
  imdb_id: string;
  tmdb_id?: string;
  show_title: string;
  season: string;
  episode: string;
  embed_url: string;
  embed_url_tmdb?: string;
  quality?: string;
}

interface VidSrcResponse<T> {
  result: T[];
}

interface SyncStatus {
  _id?: string;
  lastSyncTime: Date;
  nextSyncTime: Date;
  isRunning: boolean;
  lastSyncResults: {
    movies: number;
    series: number;
    episodes: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

class VidSrcSyncService {
  private static instance: VidSrcSyncService;
  private syncInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private scraper: IMDbScraper;
  private readonly PARALLEL_LIMIT = 10; // Process 10 items at a time
  private readonly BATCH_DELAY = 2000; // 2 second delay between batches
  private readonly ITEM_DELAY = 200; // 200ms delay between individual items

  private constructor() {
    this.scraper = IMDbScraper.getInstance();
  }

  static getInstance(): VidSrcSyncService {
    if (!VidSrcSyncService.instance) {
      VidSrcSyncService.instance = new VidSrcSyncService();
    }
    return VidSrcSyncService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    await connectDB();
    await this.checkAndRunSync();
    this.startSyncScheduler();
    this.isInitialized = true;
    console.log('🔄 VidSrc Sync Service initialized');
  }

  private async processInParallel<T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    batchSize: number = this.PARALLEL_LIMIT
  ): Promise<R[]> {
    const results: R[] = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(items.length / batchSize)} (${batch.length} items)`);

      // Process batch in parallel with individual delays
      const batchPromises = batch.map(async (item, batchIndex) => {
        // Add staggered delay to avoid hitting rate limits
        await new Promise(resolve => setTimeout(resolve, batchIndex * this.ITEM_DELAY));
        return processor(item, i + batchIndex);
      });

      const batchResults = await Promise.allSettled(batchPromises);

      // Collect successful results
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`❌ Error in batch item ${i + index}:`, result.reason);
        }
      });

      // Delay between batches to avoid detection
      if (i + batchSize < items.length) {
        console.log(`⏳ Waiting ${this.BATCH_DELAY}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, this.BATCH_DELAY));
      }
    }

    return results;
  }

  private async checkAndRunSync(): Promise<void> {
    const lastSync = await this.getLastSyncStatus();
    const now = new Date();

    if (!lastSync || now >= lastSync.nextSyncTime) {
      console.log('⏰ Sync time reached, starting automatic sync...');
      await this.runFullSync();
    } else {
      const timeUntilNext = lastSync.nextSyncTime.getTime() - now.getTime();
      console.log(`⏳ Next sync in ${Math.round(timeUntilNext / (1000 * 60 * 60))} hours`);
    }
  }

  private startSyncScheduler(): void {
    // Check every hour if it's time to sync
    this.syncInterval = setInterval(async () => {
      await this.checkAndRunSync();
    }, 60 * 60 * 1000); // 1 hour
  }

  public async runFullSync(): Promise<{ movies: number; series: number; episodes: number }> {
    console.log('🚀 Starting VidSrc full sync...');
    
    const syncStatus = await this.updateSyncStatus(true);
    const results = { movies: 0, series: 0, episodes: 0 };

    try {
      // Sync movies (pages 1-15)
      console.log('📽️ Syncing movies...');
      results.movies = await this.syncMovies();

      // Sync series (pages 1-15)
      console.log('📺 Syncing series...');
      results.series = await this.syncSeries();

      // Sync episodes (pages 1-15)
      console.log('🎬 Syncing episodes...');
      results.episodes = await this.syncEpisodes();

      // Update sync status
      await this.updateSyncStatus(false, results);

      console.log('✅ VidSrc sync completed:', results);
      return results;

    } catch (error) {
      console.error('❌ VidSrc sync failed:', error);
      await this.updateSyncStatus(false, results);
      throw error;
    }
  }

  private async syncMovies(): Promise<number> {
    let totalMovies = 0;

    for (let page = 1; page <= 15; page++) {
      try {
        const url = `https://vidsrc.xyz/movies/latest/page-${page}.json`;
        console.log(`📥 Fetching movies page ${page}...`);

        const response = await axios.get<VidSrcResponse<VidSrcMovie>>(url, {
          timeout: 30000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });

        const movies = response.data.result;

        // Filter out duplicates before processing
        const existingMovieIds = await Movie.find(
          { imdbId: { $in: movies.map(m => m.imdb_id) } },
          { imdbId: 1 }
        ).lean();
        const existingIds = new Set(existingMovieIds.map(m => m.imdbId));
        const newMovies = movies.filter(movie => !existingIds.has(movie.imdb_id));

        console.log(`🎬 Found ${movies.length} movies, ${newMovies.length} new movies to process`);

        if (newMovies.length === 0) {
          console.log(`⏭️ No new movies on page ${page}, skipping...`);
          continue;
        }

        // Process movies in parallel batches
        const processedMovies = await this.processInParallel(
          newMovies,
          async (movieData: VidSrcMovie) => this.processMovie(movieData)
        );

        const successfulMovies = processedMovies.filter(result => result !== null);
        totalMovies += successfulMovies.length;

        console.log(`✅ Page ${page}: Added ${successfulMovies.length}/${newMovies.length} movies`);

        // Add delay between pages to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`❌ Error fetching movies page ${page}:`, error);
      }
    }

    return totalMovies;
  }

  private async processMovie(movieData: VidSrcMovie): Promise<any> {
    try {
      // Double-check if movie already exists (race condition protection)
      const existingMovie = await Movie.findOne({ imdbId: movieData.imdb_id });
      if (existingMovie) {
        console.log(`⏭️ Movie already exists: ${movieData.title} (${movieData.imdb_id})`);
        return existingMovie;
      }

      console.log(`🔍 Scraping metadata for movie: ${movieData.title} (${movieData.imdb_id})`);

      // Scrape complete metadata from IMDb
      const scrapedData = await this.scraper.scrapeMovie(movieData.imdb_id);

      // Prepare movie data
      const movieDoc = {
        imdbId: movieData.imdb_id,
        tmdbId: movieData.tmdb_id,
        title: scrapedData.title || movieData.title,
        year: scrapedData.year || new Date().getFullYear(),
        rating: scrapedData.rating,
        runtime: scrapedData.runtime,
        imdbRating: scrapedData.imdbRating,
        imdbVotes: scrapedData.imdbVotes,
        popularity: scrapedData.popularity,
        popularityDelta: scrapedData.popularityDelta,
        posterUrl: scrapedData.posterUrl,
        trailerUrl: scrapedData.trailerUrl,
        trailerRuntime: scrapedData.trailerRuntime,
        trailerLikes: scrapedData.trailerLikes,
        description: scrapedData.description,
        genres: scrapedData.genres || [],
        director: scrapedData.director,
        cast: scrapedData.cast || [],
        language: scrapedData.language,
        country: scrapedData.country,
        embedUrl: movieData.embed_url,
        embedUrlTmdb: movieData.embed_url_tmdb,
        vidsrcUrl: movieData.embed_url,
        vidsrcTmdbUrl: movieData.embed_url_tmdb,
        quality: movieData.quality
      };

      // Use findOneAndUpdate with upsert to handle duplicates gracefully
      const movie = await Movie.findOneAndUpdate(
        { imdbId: movieData.imdb_id },
        movieDoc,
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      );

      console.log(`✅ Added/Updated movie with metadata: ${scrapedData.title || movieData.title} (${movieData.imdb_id})`);
      return movie;
    } catch (error) {
      console.error(`❌ Error processing movie ${movieData.imdb_id}:`, error);

      // Check if it's a duplicate key error
      if (error.code === 11000) {
        console.log(`⏭️ Movie already exists (duplicate key): ${movieData.title} (${movieData.imdb_id})`);
        // Try to find and return the existing movie
        try {
          const existingMovie = await Movie.findOne({ imdbId: movieData.imdb_id });
          return existingMovie;
        } catch (findError) {
          console.error(`❌ Error finding existing movie: ${movieData.imdb_id}`, findError);
          return null;
        }
      }

      // Fallback: Create movie with basic data if scraping fails
      try {
        const fallbackDoc = {
          imdbId: movieData.imdb_id,
          tmdbId: movieData.tmdb_id,
          title: movieData.title,
          year: new Date().getFullYear(),
          description: `Movie: ${movieData.title}`,
          genres: ['Drama'],
          imdbRating: 0,
          runtime: '120 min',
          language: 'English',
          country: 'US',
          director: 'Unknown',
          cast: [],
          embedUrl: movieData.embed_url,
          embedUrlTmdb: movieData.embed_url_tmdb || '',
          vidsrcUrl: movieData.embed_url,
          vidsrcTmdbUrl: movieData.embed_url_tmdb,
          quality: movieData.quality || 'HD'
        };

        // Use upsert for fallback as well
        const movie = await Movie.findOneAndUpdate(
          { imdbId: movieData.imdb_id },
          fallbackDoc,
          {
            upsert: true,
            new: true,
            setDefaultsOnInsert: true
          }
        );

        console.log(`⚠️ Added/Updated movie with fallback data: ${movieData.title} (${movieData.imdb_id})`);
        return movie;
      } catch (fallbackError) {
        if (fallbackError.code === 11000) {
          console.log(`⏭️ Movie already exists (fallback duplicate): ${movieData.title} (${movieData.imdb_id})`);
          try {
            const existingMovie = await Movie.findOne({ imdbId: movieData.imdb_id });
            return existingMovie;
          } catch (findError) {
            console.error(`❌ Error finding existing movie in fallback: ${movieData.imdb_id}`, findError);
            return null;
          }
        }
        console.error(`❌ Failed to save movie even with fallback: ${movieData.imdb_id}`, fallbackError);
        return null;
      }
    }
  }

  private async syncSeries(): Promise<number> {
    let totalSeries = 0;

    for (let page = 1; page <= 15; page++) {
      try {
        const url = `https://vidsrc.xyz/tvshows/latest/page-${page}.json`;
        console.log(`📥 Fetching series page ${page}...`);

        const response = await axios.get<VidSrcResponse<VidSrcSeries>>(url, {
          timeout: 30000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });

        const seriesList = response.data.result;

        // Filter out duplicates before processing
        const existingSeriesIds = await Series.find(
          { imdbId: { $in: seriesList.map(s => s.imdb_id) } },
          { imdbId: 1 }
        ).lean();
        const existingIds = new Set(existingSeriesIds.map(s => s.imdbId));
        const newSeries = seriesList.filter(series => !existingIds.has(series.imdb_id));

        console.log(`📺 Found ${seriesList.length} series, ${newSeries.length} new series to process`);

        if (newSeries.length === 0) {
          console.log(`⏭️ No new series on page ${page}, skipping...`);
          continue;
        }

        // Process series in parallel batches
        const processedSeries = await this.processInParallel(
          newSeries,
          async (seriesData: VidSrcSeries) => this.processSeries(seriesData)
        );

        const successfulSeries = processedSeries.filter(result => result !== null);
        totalSeries += successfulSeries.length;

        console.log(`✅ Page ${page}: Added ${successfulSeries.length}/${newSeries.length} series`);

      } catch (error) {
        console.error(`❌ Error fetching series page ${page}:`, error);
      }
    }

    return totalSeries;
  }

  private async processSeries(seriesData: VidSrcSeries): Promise<any> {
    try {
      // Double-check if series already exists (race condition protection)
      const existingSeries = await Series.findOne({ imdbId: seriesData.imdb_id });
      if (existingSeries) {
        console.log(`⏭️ Series already exists: ${seriesData.title} (${seriesData.imdb_id})`);
        return existingSeries;
      }

      console.log(`🔍 Scraping metadata for series: ${seriesData.title} (${seriesData.imdb_id})`);

      // Scrape complete metadata from IMDb
      const scrapedData = await this.scraper.scrapeSeries(seriesData.imdb_id);

      // Prepare series data
      const seriesDoc = {
        imdbId: seriesData.imdb_id,
        tmdbId: seriesData.tmdb_id,
        title: scrapedData.title || seriesData.title,
        startYear: scrapedData.startYear || new Date().getFullYear(),
        endYear: scrapedData.endYear,
        rating: scrapedData.rating,
        imdbRating: scrapedData.imdbRating,
        imdbVotes: scrapedData.imdbVotes,
        popularity: scrapedData.popularity,
        popularityDelta: scrapedData.popularityDelta,
        posterUrl: scrapedData.posterUrl,
        trailerUrl: scrapedData.trailerUrl,
        description: scrapedData.description,
        genres: scrapedData.genres || [],
        creator: scrapedData.creator,
        cast: scrapedData.cast || [],
        language: scrapedData.language,
        country: scrapedData.country,
        totalSeasons: scrapedData.totalSeasons,
        status: scrapedData.status,
        embedUrl: seriesData.embed_url,
        embedUrlTmdb: seriesData.embed_url_tmdb,
        vidsrcUrl: seriesData.embed_url,
        vidsrcTmdbUrl: seriesData.embed_url_tmdb
      };

      // Use findOneAndUpdate with upsert to handle duplicates gracefully
      const series = await Series.findOneAndUpdate(
        { imdbId: seriesData.imdb_id },
        seriesDoc,
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      );

      console.log(`✅ Added/Updated series with metadata: ${scrapedData.title || seriesData.title} (${seriesData.imdb_id})`);
      return series;
    } catch (error) {
      console.error(`❌ Error processing series ${seriesData.imdb_id}:`, error);

      // Check if it's a duplicate key error
      if (error.code === 11000) {
        console.log(`⏭️ Series already exists (duplicate key): ${seriesData.title} (${seriesData.imdb_id})`);
        // Try to find and return the existing series
        try {
          const existingSeries = await Series.findOne({ imdbId: seriesData.imdb_id });
          return existingSeries;
        } catch (findError) {
          console.error(`❌ Error finding existing series: ${seriesData.imdb_id}`, findError);
          return null;
        }
      }

      // Fallback: Create series with basic data if scraping fails
      try {
        const fallbackDoc = {
          imdbId: seriesData.imdb_id,
          tmdbId: seriesData.tmdb_id,
          title: seriesData.title,
          startYear: new Date().getFullYear(),
          description: `TV Series: ${seriesData.title}`,
          genres: ['Drama'],
          imdbRating: 0,
          status: 'ongoing',
          totalSeasons: 1,
          language: 'English',
          country: 'US',
          cast: [],
          embedUrl: seriesData.embed_url,
          embedUrlTmdb: seriesData.embed_url_tmdb || '',
          vidsrcUrl: seriesData.embed_url,
          vidsrcTmdbUrl: seriesData.embed_url_tmdb
        };

        // Use upsert for fallback as well
        const series = await Series.findOneAndUpdate(
          { imdbId: seriesData.imdb_id },
          fallbackDoc,
          {
            upsert: true,
            new: true,
            setDefaultsOnInsert: true
          }
        );

        console.log(`⚠️ Added/Updated series with fallback data: ${seriesData.title} (${seriesData.imdb_id})`);
        return series;
      } catch (fallbackError) {
        if (fallbackError.code === 11000) {
          console.log(`⏭️ Series already exists (fallback duplicate): ${seriesData.title} (${seriesData.imdb_id})`);
          try {
            const existingSeries = await Series.findOne({ imdbId: seriesData.imdb_id });
            return existingSeries;
          } catch (findError) {
            console.error(`❌ Error finding existing series in fallback: ${seriesData.imdb_id}`, findError);
            return null;
          }
        }
        console.error(`❌ Failed to save series even with fallback: ${seriesData.imdb_id}`, fallbackError);
        return null;
      }
    }
  }

  private async syncEpisodes(): Promise<number> {
    let totalEpisodes = 0;

    for (let page = 1; page <= 15; page++) {
      try {
        const url = `https://vidsrc.xyz/episodes/latest/page-${page}.json`;
        console.log(`📥 Fetching episodes page ${page}...`);
        
        const response = await axios.get<VidSrcResponse<VidSrcEpisode>>(url, {
          timeout: 30000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });

        const episodes = response.data.result;
        
        for (const episodeData of episodes) {
          try {
            // Check if episode already exists
            const existingEpisode = await Episode.findOne({ 
              imdbId: episodeData.imdb_id,
              season: parseInt(episodeData.season),
              episode: parseInt(episodeData.episode)
            });
            if (existingEpisode) continue;

            // Ensure series exists (auto-create if needed)
            await this.ensureSeriesExistsForSync(episodeData.imdb_id, episodeData.show_title);

            // Get series for poster URL and genres
            const series = await Series.findOne({ imdbId: episodeData.imdb_id });

            // Generate VidSrc embed URL using the pattern
            const embedUrl = `https://vidsrc.me/embed/tv?imdb=${episodeData.imdb_id}&season=${episodeData.season}&episode=${episodeData.episode}`;

            const episode = new Episode({
              imdbId: episodeData.imdb_id,
              tmdbId: episodeData.tmdb_id,
              seriesTitle: episodeData.show_title,
              season: parseInt(episodeData.season),
              episode: parseInt(episodeData.episode),
              episodeTitle: `Episode ${episodeData.episode}`,
              description: `Episode ${episodeData.episode} of ${episodeData.show_title}`,
              posterUrl: series?.posterUrl || '',
              seriesPosterUrl: series?.posterUrl || '',
              genres: series?.genres || [], // Populate genres from series
              quality: episodeData.quality || 'HD',
              embedUrl: embedUrl, // Required field
              embedUrlTmdb: episodeData.embed_url_tmdb || '',
              vidsrcUrl: episodeData.embed_url,
              vidsrcTmdbUrl: episodeData.embed_url_tmdb,
              runtime: '45 min',
              airDate: new Date().toISOString(),
              createdAt: new Date(),
              updatedAt: new Date()
            });

            await episode.save();
            totalEpisodes++;
            console.log(`✅ Added episode: ${episodeData.show_title} S${episodeData.season}E${episodeData.episode}`);
          } catch (error) {
            console.error(`❌ Error processing episode ${episodeData.imdb_id} S${episodeData.season}E${episodeData.episode}:`, error);
          }
        }

        // Add delay between pages
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`❌ Error fetching episodes page ${page}:`, error);
      }
    }

    return totalEpisodes;
  }

  private async ensureSeriesExistsForSync(imdbId: string, title: string): Promise<void> {
    const existingSeries = await Series.findOne({ imdbId });

    if (!existingSeries) {
      try {
        console.log(`🔍 Auto-creating series with metadata: ${title} (${imdbId})`);

        // Scrape complete metadata from IMDb
        const scrapedData = await this.scraper.scrapeSeries(imdbId);

        const series = new Series({
          imdbId,
          title: scrapedData.title || title,
          startYear: scrapedData.startYear || new Date().getFullYear(),
          endYear: scrapedData.endYear,
          rating: scrapedData.rating,
          imdbRating: scrapedData.imdbRating,
          imdbVotes: scrapedData.imdbVotes,
          popularity: scrapedData.popularity,
          popularityDelta: scrapedData.popularityDelta,
          posterUrl: scrapedData.posterUrl,
          trailerUrl: scrapedData.trailerUrl,
          description: scrapedData.description || `TV Series: ${title}`,
          genres: scrapedData.genres || [],
          creator: scrapedData.creator,
          cast: scrapedData.cast || [],
          language: scrapedData.language,
          country: scrapedData.country,
          totalSeasons: scrapedData.totalSeasons,
          status: scrapedData.status,
          embedUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
          embedUrlTmdb: '',
          vidsrcUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
          vidsrcTmdbUrl: ''
        });

        await series.save();
        console.log(`✅ Auto-created series with metadata: ${scrapedData.title || title} (${imdbId})`);
      } catch (error) {
        console.error(`❌ Error scraping series metadata for ${imdbId}:`, error);

        // Fallback: Create series with basic data
        const series = new Series({
          imdbId,
          title,
          startYear: new Date().getFullYear(),
          description: `TV Series: ${title}`,
          genres: ['Drama'],
          imdbRating: 0,
          status: 'ongoing',
          totalSeasons: 1,
          language: 'English',
          country: 'US',
          cast: [],
          embedUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
          embedUrlTmdb: '',
          vidsrcUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
          vidsrcTmdbUrl: ''
        });

        await series.save();
        console.log(`⚠️ Auto-created series with fallback data: ${title} (${imdbId})`);
      }
    }
  }

  private async getLastSyncStatus(): Promise<SyncStatus | null> {
    await connectDB();
    const { default: SyncStatusModel } = await import('@/models/SyncStatus');
    return SyncStatusModel.findOne({ syncType: 'VIDSRC_FULL_SYNC' }).sort({ createdAt: -1 }).lean();
  }

  private async updateSyncStatus(isRunning: boolean, results?: { movies: number; series: number; episodes: number }): Promise<SyncStatus> {
    await connectDB();
    const { default: SyncStatusModel } = await import('@/models/SyncStatus');

    const now = new Date();
    const nextSync = new Date(now.getTime() + 12 * 60 * 60 * 1000); // 12 hours from now
    const syncType = 'VIDSRC_FULL_SYNC';

    const syncStatus = {
      syncType,
      lastSyncTime: isRunning ? null : now,
      nextSyncTime: nextSync,
      isRunning,
      lastSyncResults: results || { movies: 0, series: 0, episodes: 0 },
      updatedAt: now
    };

    const existing = await SyncStatusModel.findOne({ syncType });

    if (existing) {
      Object.assign(existing, syncStatus);
      return existing.save();
    } else {
      return SyncStatusModel.create({
        ...syncStatus,
        createdAt: now
      });
    }
  }

  public async getSyncStatus(): Promise<SyncStatus | null> {
    return this.getLastSyncStatus();
  }

  public async forcSync(): Promise<{ movies: number; series: number; episodes: number }> {
    console.log('🔄 Force sync requested...');
    return this.runFullSync();
  }

  destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.isInitialized = false;
    console.log('🛑 VidSrc Sync Service destroyed');
  }
}

export default VidSrcSyncService;
