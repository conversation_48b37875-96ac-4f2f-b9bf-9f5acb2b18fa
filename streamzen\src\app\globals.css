@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;
  --surface: #0a0a0a;
  --surface-elevated: #141414;
  --surface-glass: rgba(20, 20, 20, 0.8);
  --border: rgba(255, 255, 255, 0.08);
  --border-hover: rgba(255, 255, 255, 0.16);
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #737373;
  --accent: #2a2a2a;
  --accent-hover: #3a3a3a;
  --accent-light: #4a4a4a;
  --accent-dark: #1a1a1a;
  --success: #30d158;
  --warning: #ff9f0a;
  --error: #ff453a;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Premium scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Custom scrollbar for request history */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.6);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8);
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Advanced glassmorphism */
.glass {
  background: var(--surface-glass);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--border);
}

.glass-elevated {
  background: rgba(30, 30, 30, 0.9);
  backdrop-filter: blur(40px) saturate(200%);
  -webkit-backdrop-filter: blur(40px) saturate(200%);
  border: 1px solid var(--border-hover);
}

/* Premium animations */
.animate-fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slide-up {
  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Premium hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(0, 113, 227, 0.3);
}

/* Hide scrollbar for horizontal scrolling */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Text utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Focus styles */
.focus-ring {
  outline: none;
  transition: box-shadow 0.2s ease;
}

.focus-ring:focus-visible {
  box-shadow: 0 0 0 3px rgba(74, 74, 74, 0.5);
}

/* Loading shimmer */
.shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.05) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Custom gradient utilities */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

.bg-gradient-conic {
  background: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
}

/* Premium card hover effects */
.card-hover {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.card-hover:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.8),
    0 0 40px rgba(74, 74, 74, 0.25),
    0 0 80px rgba(42, 42, 42, 0.15);
}

/* Smooth page transitions */
.page-transition {
  animation: pageEnter 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
