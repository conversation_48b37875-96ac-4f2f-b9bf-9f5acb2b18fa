import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * GET Series Episodes - Now using embedded episodes from Series collection
 * This replaces the old separate Episode collection approach
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const season = searchParams.get('season') ? parseInt(searchParams.get('season')!) : undefined;

    await connectDB();

    // Get series with embedded episodes
    const series = await Series.findOne({ imdbId: id }).lean();

    if (!series) {
      return NextResponse.json(
        { error: 'Series not found' },
        { status: 404 }
      );
    }

    let episodes = series.episodes || [];

    // Filter by season if specified
    if (season !== undefined) {
      episodes = episodes.filter(ep => ep.season === season);
    }

    // Sort episodes by season and episode number
    episodes.sort((a, b) => {
      if (a.season !== b.season) return a.season - b.season;
      return a.episode - b.episode;
    });

    console.log(`📺 Returning ${episodes.length} episodes for series ${series.title}${season ? ` season ${season}` : ''}`);

    return NextResponse.json(episodes);
  } catch (error) {
    console.error('Error fetching embedded episodes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch episodes' },
      { status: 500 }
    );
  }
}
