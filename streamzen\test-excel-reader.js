const XLSX = require('xlsx');
const path = require('path');

async function testExcelReader() {
  try {
    console.log('📊 Testing Excel file reader...');
    
    const excelPath = path.join(__dirname, 'imdb_movie_ids_final.xlsx');
    console.log('📁 Excel file path:', excelPath);
    
    // Check if file exists
    const fs = require('fs');
    if (!fs.existsSync(excelPath)) {
      console.error('❌ Excel file not found at:', excelPath);
      return;
    }
    
    console.log('✅ Excel file found, reading...');
    
    // Read the Excel file
    const workbook = XLSX.readFile(excelPath);
    console.log('📋 Sheet names:', workbook.SheetNames);
    
    // Get the first sheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON
    const data = XLSX.utils.sheet_to_json(worksheet);
    console.log(`📊 Total rows in Excel: ${data.length}`);
    
    // Show first 10 entries
    console.log('\n📝 First 10 entries:');
    data.slice(0, 10).forEach((row, index) => {
      console.log(`${index + 1}. ${row.TitleID}`);
    });
    
    // Extract IMDb IDs
    const imdbIds = data.map(row => row.TitleID).filter(id => id && id.startsWith('tt'));
    console.log(`\n✅ Valid IMDb IDs found: ${imdbIds.length}`);
    
    // Show some statistics
    const invalidIds = data.filter(row => !row.TitleID || !row.TitleID.startsWith('tt'));
    if (invalidIds.length > 0) {
      console.log(`⚠️  Invalid entries found: ${invalidIds.length}`);
      console.log('First few invalid entries:', invalidIds.slice(0, 5));
    }
    
    console.log('\n🎯 Excel file analysis complete!');
    console.log(`📊 Ready to process ${imdbIds.length} movies with parallel scraper`);
    
  } catch (error) {
    console.error('❌ Error reading Excel file:', error);
  }
}

testExcelReader();
