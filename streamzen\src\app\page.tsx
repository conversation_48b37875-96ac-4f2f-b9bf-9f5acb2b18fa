import { Suspense } from 'react';
import { Metadata } from 'next';
import PremiumHeroSection from '@/components/PremiumHeroSection';
import TrendingSection from '@/components/TrendingSection';
import GenreSpotlight from '@/components/GenreSpotlight';
import DecadeCollection from '@/components/DecadeCollection';
import DirectorSpotlight from '@/components/DirectorSpotlight';
import GlobalCinema from '@/components/GlobalCinema';
import ContentSection from '@/components/ContentSection';
import BingeWorthy from '@/components/BingeWorthy';
import HiddenGems from '@/components/HiddenGems';
import QuickPicks from '@/components/QuickPicks';
import CastSpotlight from '@/components/CastSpotlight';
import ModernLoader from '@/components/ModernLoader';
import { apiClient } from '@/lib/api';
import InitializeButton from '@/components/ClientInitializeButton';
import connectDB from '@/lib/mongodb';
import Episode from '@/models/Episode';
import Series from '@/models/Series';
// import { SchemaGenerator } from '@/lib/schema';

// Function to get filtered episodes - ONLY VidSrc latest episodes
async function getFilteredEpisodes(filters: { genre?: string; language?: string; country?: string }, limit: number = 20) {
  try {
    await connectDB();

    console.log(`🔍 Fetching VidSrc filtered episodes:`, filters, `limit: ${limit}`);

    // First, let's check what VidSrc latest episodes we have in Series collection
    const seriesWithLatestEpisodes = await Series.countDocuments({ 'episodes.isLatestRelease': true });
    const sampleSeries = await Series.findOne({ 'episodes.isLatestRelease': true }).lean();
    console.log(`📊 Total series with VidSrc latest episodes: ${seriesWithLatestEpisodes}`);
    console.log(`📝 Sample series with VidSrc episodes:`, sampleSeries ? {
      title: sampleSeries.title,
      language: sampleSeries.language,
      country: sampleSeries.country,
      genres: sampleSeries.genres,
      episodeCount: sampleSeries.episodes?.length || 0
    } : 'None found');

    // Build series match stage for filtering
    const seriesMatchStage: any = {
      'episodes.isLatestRelease': true // ✅ CRITICAL: Only series with VidSrc latest episodes
    };

    if (filters.genre) {
      seriesMatchStage.genres = { $in: [filters.genre] };
      console.log(`🎨 Genre filter applied: genres contains "${filters.genre}"`);
    }

    if (filters.language) {
      seriesMatchStage.language = filters.language;
      console.log(`🌍 Language filter applied: language = "${filters.language}"`);

      // Check how many series match this language
      const languageCount = await Series.countDocuments({ language: filters.language, 'episodes.isLatestRelease': true });
      console.log(`📊 Series with language "${filters.language}" and latest episodes: ${languageCount}`);
    }

    if (filters.country) {
      seriesMatchStage.country = filters.country;
      console.log(`🏳️ Country filter applied: country = "${filters.country}"`);
    }

    console.log(`🔍 Final series match stage:`, seriesMatchStage);

    // Same aggregation pipeline as episodes page with Series lookup
    const aggregationPipeline = [
      { $match: seriesMatchStage },
      // Unwind episodes array to work with individual episodes
      { $unwind: '$episodes' },
      // Filter to only VidSrc latest episodes
      {
        $match: {
          'episodes.isLatestRelease': true
        }
      },
      // Sort episodes by series, then by latest episode
      {
        $sort: {
          imdbId: 1,
          season: -1,
          episode: -1,
          createdAt: -1
        }
      },
      // Group by series (imdbId) and get the latest episode
      {
        $group: {
          _id: '$imdbId',
          latestEpisode: { $first: '$$ROOT' }
        }
      },
      // Replace root with the latest episode
      {
        $replaceRoot: { newRoot: '$latestEpisode' }
      },
      // Join with Series collection to get series poster - SAME AS EPISODES PAGE
      {
        $lookup: {
          from: 'series',
          localField: 'imdbId',
          foreignField: 'imdbId',
          as: 'seriesInfo'
        }
      },
      // Add series poster information - SAME AS EPISODES PAGE
      {
        $addFields: {
          seriesPosterUrl: { $arrayElemAt: ['$seriesInfo.posterUrl', 0] },
          seriesTitle: {
            $cond: {
              if: { $ne: [{ $arrayElemAt: ['$seriesInfo.title', 0] }, null] },
              then: { $arrayElemAt: ['$seriesInfo.title', 0] },
              else: '$seriesTitle'
            }
          },
          // Always use series poster for episodes
          posterUrl: { $arrayElemAt: ['$seriesInfo.posterUrl', 0] }
        }
      },
      // Clean up the response
      {
        $project: {
          seriesInfo: 0
        }
      },
      // Sort by creation date (newest first)
      { $sort: { createdAt: -1 } },
      { $limit: limit }
    ];

    const rawEpisodes = await Series.aggregate(aggregationPipeline);

    console.log(`✅ Found ${rawEpisodes.length} filtered episodes for`, filters);

    // Convert to plain objects - SAME AS EPISODES PAGE
    const episodes = rawEpisodes.map(episode => ({
      _id: episode._id.toString(),
      imdbId: episode.imdbId,
      seriesTitle: episode.seriesTitle,
      season: episode.season,
      episode: episode.episode,
      episodeTitle: episode.episodeTitle,
      description: episode.description,
      posterUrl: episode.posterUrl || episode.seriesPosterUrl, // Use series poster as fallback
      seriesPosterUrl: episode.seriesPosterUrl,
      runtime: episode.runtime,
      imdbRating: episode.imdbRating,
      airDate: episode.airDate,
      embedUrl: episode.embedUrl,
      genres: episode.genres || [],
      language: episode.language,
      country: episode.country,
      createdAt: episode.createdAt,
      updatedAt: episode.updatedAt
    }));

    // Debug: Show sample with poster info
    if (episodes.length > 0) {
      console.log('📝 Sample filtered episodes:', episodes.slice(0, 2).map(ep => ({
        seriesTitle: ep.seriesTitle,
        hasPoster: !!ep.posterUrl,
        hasSeriesPoster: !!ep.seriesPosterUrl,
        genres: ep.genres
      })));
    }

    return episodes;
  } catch (error) {
    console.error('Error fetching filtered episodes:', error);
    return [];
  }
}

// Enhanced metadata for 2025
export const metadata: Metadata = {
  title: 'freeMoviesWatchNow - Stream Premium Movies & TV Series Free in HD',
  description: 'Watch the latest movies and TV series online free in stunning HD quality. Discover trending content, hidden gems, and binge-worthy series updated daily.',
  keywords: 'free movies, watch online, HD streaming, TV series, latest movies, trending shows, cinema, entertainment',
  openGraph: {
    title: 'freeMoviesWatchNow - Premium Free Streaming',
    description: 'Stream the latest movies and TV series in HD quality. Your ultimate destination for premium entertainment.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'freeMoviesWatchNow - Premium Free Streaming',
    description: 'Stream the latest movies and TV series in HD quality.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

async function getEnhancedHomePageData() {
  try {
    const currentYear = new Date().getFullYear();

    // Fetch diverse content for different sections
    const [
      // Hero content - top rated recent content
      heroMoviesResponse,
      heroSeriesResponse,

      // Trending content - high popularity with positive delta
      trendingMoviesResponse,
      trendingSeriesResponse,

      // Recently added content
      recentMoviesResponse,
      recentSeriesResponse,
      recentEpisodesResponse,

      // Top rated content by different criteria
      topRatedMoviesResponse,
      topRatedSeriesResponse,

      // Genre-specific content (we'll fetch popular genres)
      actionMoviesResponse,
      dramaSeriesResponse,
      comedyMoviesResponse,

      // Decade collections
      modernMoviesResponse, // 2020s
      classicMoviesResponse, // 2000s-2010s

      // Global content
      internationalMoviesResponse,
      internationalSeriesResponse,

      // Binge-worthy series (ongoing with high ratings)
      ongoingSeriesResponse,

      // Quick picks (shorter content)
      shortMoviesResponse
    ] = await Promise.all([
      // Hero content
      apiClient.getMovies({ limit: 8, sortBy: 'imdbRating', sortOrder: 'desc', year: currentYear - 1 }),
      apiClient.getSeries({ limit: 8, sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Trending (we'll simulate trending by using popularity)
      apiClient.getMovies({ limit: 12, sortBy: 'popularity', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 12, sortBy: 'popularity', sortOrder: 'desc' }),

      // Recently added - Increased limits
      apiClient.getMovies({ limit: 24, sortBy: 'createdAt', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 24, sortBy: 'createdAt', sortOrder: 'desc' }),
      apiClient.getEpisodes({ limit: 24, sortBy: 'createdAt', sortOrder: 'desc', isLatestRelease: true }), // ✅ ONLY VidSrc latest episodes

      // Top rated - Increased limits
      apiClient.getMovies({ limit: 30, sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 30, sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Genre content - Increased limits
      apiClient.getMovies({ limit: 20, genre: 'Action', sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 20, genre: 'Drama', sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getMovies({ limit: 20, genre: 'Comedy', sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Decade collections
      apiClient.getMovies({ limit: 15, year: currentYear - 4, sortBy: 'imdbRating', sortOrder: 'desc' }), // Recent years
      apiClient.getMovies({ limit: 15, year: 2010, sortBy: 'imdbRating', sortOrder: 'desc' }), // 2010s

      // International content
      apiClient.getMovies({ limit: 12, language: 'English', sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 12, language: 'English', sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Ongoing series
      apiClient.getSeries({ limit: 12, sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Short content (we'll filter by runtime later)
      apiClient.getMovies({ limit: 20, sortBy: 'imdbRating', sortOrder: 'desc' })
    ]);

    // Fetch new episode sections with increased counts
    console.log('🎬 Fetching episode sections...');
    const [
      animationEpisodes,
      englishEpisodes,
      koreanEpisodes
    ] = await Promise.all([
      getFilteredEpisodes({ genre: 'Animation' }, 24),
      getFilteredEpisodes({ language: 'English' }, 24),
      getFilteredEpisodes({ language: 'Korean' }, 24)
    ]);

    console.log('📊 Episode sections results:', {
      animation: animationEpisodes.length,
      english: englishEpisodes.length,
      korean: koreanEpisodes.length
    });

    return {
      hero: {
        movies: heroMoviesResponse.data,
        series: heroSeriesResponse.data
      },
      trending: {
        movies: trendingMoviesResponse.data,
        series: trendingSeriesResponse.data
      },
      recent: {
        movies: recentMoviesResponse.data,
        series: recentSeriesResponse.data,
        episodes: recentEpisodesResponse.data
      },
      episodeSections: {
        animation: animationEpisodes,
        english: englishEpisodes,
        korean: koreanEpisodes
      },
      topRated: {
        movies: topRatedMoviesResponse.data,
        series: topRatedSeriesResponse.data
      },
      genres: {
        action: actionMoviesResponse.data,
        drama: dramaSeriesResponse.data,
        comedy: comedyMoviesResponse.data
      },
      decades: {
        modern: modernMoviesResponse.data,
        classic: classicMoviesResponse.data
      },
      international: {
        movies: internationalMoviesResponse.data,
        series: internationalSeriesResponse.data
      },
      bingeWorthy: ongoingSeriesResponse.data,
      quickPicks: shortMoviesResponse.data.filter(movie =>
        movie.runtime && parseInt(movie.runtime.replace(/\D/g, '')) < 120
      )
    };
  } catch (error) {
    console.error('Error fetching enhanced homepage data:', error);
    return {
      hero: { movies: [], series: [] },
      trending: { movies: [], series: [] },
      recent: { movies: [], series: [], episodes: [] },
      episodeSections: { animation: [], english: [], korean: [] },
      topRated: { movies: [], series: [] },
      genres: { action: [], drama: [], comedy: [] },
      decades: { modern: [], classic: [] },
      international: { movies: [], series: [] },
      bingeWorthy: [],
      quickPicks: []
    };
  }
}

function transformMovieToHeroItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    description: movie.description,
    type: 'movie' as const
  };
}

function transformSeriesToHeroItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    description: series.description,
    type: 'series' as const
  };
}

function transformMovieToContentItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    description: movie.description,
    type: 'movie' as const
  };
}

function transformSeriesToContentItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    description: series.description,
    type: 'series' as const
  };
}

function transformEpisodeToContentItem(episode: any) {
  return {
    id: episode._id,
    imdbId: episode.imdbId,
    title: episode.episodeTitle || `Episode ${episode.episode}`,
    season: episode.season,
    episode: episode.episode,
    seriesTitle: episode.seriesTitle,
    posterUrl: episode.posterUrl,
    seriesPosterUrl: episode.seriesPosterUrl, // Use series poster for episodes
    imdbRating: episode.imdbRating,
    description: episode.description,
    type: 'episode' as const
  };
}

export default async function Home() {
  const data = await getEnhancedHomePageData();

  // Check if we have any content at all
  const hasContent = data.hero.movies.length > 0 || data.hero.series.length > 0 ||
                    data.recent.movies.length > 0 || data.recent.series.length > 0;

  // If no data, show initialization prompt
  if (!hasContent) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Welcome to freeMoviesWatchNow</h1>
          <p className="text-gray-400 mb-8">Initialize the database to start streaming</p>
          <InitializeButton />
        </div>
      </div>
    );
  }

  // Create hero items from top-rated movies and series
  const heroItems = [
    ...data.hero.movies.slice(0, 4).map(transformMovieToHeroItem),
    ...data.hero.series.slice(0, 4).map(transformSeriesToHeroItem)
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
      {/* Enhanced Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "freeMoviesWatchNow",
            "description": "Stream premium movies and TV series free in HD quality",
            "url": "https://freemovieswatchnow.com",
            "potentialAction": {
              "@type": "SearchAction",
              "target": "https://freemovieswatchnow.com/search?q={search_term_string}",
              "query-input": "required name=search_term_string"
            },
            "mainEntity": {
              "@type": "ItemList",
              "name": "Featured Content",
              "itemListElement": [
                ...data.hero.movies.slice(0, 5).map((movie, index) => ({
                  "@type": "Movie",
                  "position": index + 1,
                  "name": movie.title,
                  "datePublished": movie.year,
                  "image": movie.posterUrl,
                  "url": `/watch/movie/${movie.imdbId}`,
                  "aggregateRating": movie.imdbRating ? {
                    "@type": "AggregateRating",
                    "ratingValue": movie.imdbRating,
                    "ratingCount": "1000"
                  } : undefined
                })),
                ...data.hero.series.slice(0, 5).map((series, index) => ({
                  "@type": "TVSeries",
                  "position": index + 6,
                  "name": series.title,
                  "startDate": series.startYear,
                  "image": series.posterUrl,
                  "url": `/watch/series/${series.imdbId}`,
                  "aggregateRating": series.imdbRating ? {
                    "@type": "AggregateRating",
                    "ratingValue": series.imdbRating,
                    "ratingCount": "1000"
                  } : undefined
                }))
              ]
            }
          })
        }}
      />

      {/* Premium Hero Section */}
      <Suspense fallback={<ModernLoader type="hero" message="Preparing your cinematic experience..." />}>
        <PremiumHeroSection items={heroItems} />
      </Suspense>

      {/* Enhanced Dynamic Content Sections with Modern Design */}
      <div className="relative">
        {/* Advanced Animated Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black via-gray-900/30 to-black" />

          {/* Floating Orbs with Enhanced Animation */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-600/8 to-cyan-600/8 rounded-full blur-3xl animate-pulse opacity-60" />
          <div className="absolute top-1/2 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-600/8 to-pink-600/8 rounded-full blur-3xl animate-pulse opacity-60" style={{ animationDelay: '2s' }} />
          <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-gradient-to-r from-red-600/8 to-orange-600/8 rounded-full blur-3xl animate-pulse opacity-60" style={{ animationDelay: '4s' }} />
          <div className="absolute top-3/4 right-1/3 w-64 h-64 bg-gradient-to-r from-green-600/8 to-emerald-600/8 rounded-full blur-3xl animate-pulse opacity-60" style={{ animationDelay: '6s' }} />

          {/* Subtle Grid Pattern */}
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.02)_1px,transparent_0)] bg-[length:50px_50px]" />
        </div>

        <div className="relative space-y-32 py-24 pb-40">
          {/* Enhanced Trending Now Section */}
          <Suspense fallback={<ModernLoader type="content" message="Loading trending content..." />}>
            <TrendingSection
              movies={data.trending.movies}
              series={data.trending.series}
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
            />
          </Suspense>

          {/* New Episode Sections - Animation */}
          <Suspense fallback={<ModernLoader type="content" message="Loading animation episodes..." />}>
            <ContentSection
              title="🎨 Latest Animation Episodes"
              items={data.episodeSections.animation.map(transformEpisodeToContentItem)}
              viewAllHref="/episodes?genre=Animation"
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '0.2s' }}
            />
          </Suspense>

          {/* New Episode Sections - English */}
          <Suspense fallback={<ModernLoader type="content" message="Loading English episodes..." />}>
            <ContentSection
              title="🇺🇸 Latest English Episodes"
              items={data.episodeSections.english.map(transformEpisodeToContentItem)}
              viewAllHref="/episodes?language=English"
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '0.4s' }}
            />
          </Suspense>

          {/* New Episode Sections - Korean */}
          <Suspense fallback={<ModernLoader type="content" message="Loading Korean episodes..." />}>
            <ContentSection
              title="🇰🇷 Latest Korean Episodes"
              items={data.episodeSections.korean.map(transformEpisodeToContentItem)}
              viewAllHref="/episodes?language=Korean"
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '0.6s' }}
            />
          </Suspense>

          {/* Enhanced Genre Spotlights */}
          <Suspense fallback={<ModernLoader type="content" message="Curating genre collections..." />}>
            <GenreSpotlight
              actionMovies={data.genres.action}
              dramaSeries={data.genres.drama}
              comedyMovies={data.genres.comedy}
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '0.8s' }}
            />
          </Suspense>

          {/* Enhanced Recently Added - Increased count */}
          <Suspense fallback={<ModernLoader type="content" message="Loading fresh content..." />}>
            <ContentSection
              title="🆕 Recently Added"
              items={[
                ...data.recent.movies.slice(0, 12).map(transformMovieToContentItem),
                ...data.recent.series.slice(0, 12).map(transformSeriesToContentItem)
              ]}
              viewAllHref="/movies"
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '0.4s' }}
            />
          </Suspense>



          {/* Enhanced Binge-Worthy Series */}
          <Suspense fallback={<ModernLoader type="content" message="Finding binge-worthy series..." />}>
            <BingeWorthy
              series={data.bingeWorthy}
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '0.8s' }}
            />
          </Suspense>

          {/* Enhanced Hidden Gems */}
          <Suspense fallback={<ModernLoader type="content" message="Discovering hidden gems..." />}>
            <HiddenGems
              movies={data.topRated.movies.filter(m => m.imdbRating && m.imdbRating >= 8.0)}
              series={data.topRated.series.filter(s => s.imdbRating && s.imdbRating >= 8.0)}
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '1.0s' }}
            />
          </Suspense>

          {/* Enhanced Decade Collections */}
          <Suspense fallback={<ModernLoader type="content" message="Exploring cinema through time..." />}>
            <DecadeCollection
              modernContent={data.decades.modern}
              classicContent={data.decades.classic}
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '1.2s' }}
            />
          </Suspense>

          {/* Enhanced Global Cinema */}
          <Suspense fallback={<ModernLoader type="content" message="Loading international content..." />}>
            <GlobalCinema
              movies={data.international.movies}
              series={data.international.series}
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '1.4s' }}
            />
          </Suspense>

          {/* Enhanced Quick Picks */}
          <Suspense fallback={<ModernLoader type="content" message="Preparing quick picks..." />}>
            <QuickPicks
              movies={data.quickPicks}
              className="animate-fade-in transform transition-all duration-1000 hover:scale-[1.01]"
              style={{ animationDelay: '1.6s' }}
            />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
