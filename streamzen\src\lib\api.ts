const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');

export interface Movie {
  _id: string;
  imdbId: string;
  tmdbId?: string;
  title: string;
  year: number;
  rating?: string;
  runtime?: string;
  imdbRating?: number;
  imdbVotes?: string;
  popularity?: number;
  popularityDelta?: number;
  posterUrl?: string;
  trailerUrl?: string;
  trailerRuntime?: string;
  trailerLikes?: string;
  description?: string;
  genres?: string[];
  director?: string;
  cast?: string[];
  language?: string;
  country?: string;
  embedUrl: string;
  embedUrlTmdb?: string;
  quality?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Series {
  _id: string;
  imdbId: string;
  tmdbId?: string;
  title: string;
  startYear: number;
  endYear?: number;
  rating?: string;
  imdbRating?: number;
  imdbVotes?: string;
  popularity?: number;
  popularityDelta?: number;
  posterUrl?: string;
  trailerUrl?: string;
  description?: string;
  genres?: string[];
  creator?: string;
  cast?: string[];
  language?: string;
  country?: string;
  totalSeasons?: number;
  status?: string;
  embedUrl: string;
  embedUrlTmdb?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Episode {
  _id: string;
  imdbId: string;
  tmdbId?: string;
  seriesTitle: string;
  season: number;
  episode: number;
  episodeTitle?: string;
  airDate?: string;
  runtime?: string;
  imdbRating?: number;
  description?: string;
  embedUrl: string;
  embedUrlTmdb?: string;
  quality?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ContentFilters {
  genre?: string;
  year?: number;
  language?: string;
  country?: string;
  rating?: string;
  quality?: string;
  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  search?: string;
  isLatestRelease?: boolean; // ✅ NEW: Filter for VidSrc latest episodes only
}

export interface GenreCount {
  genre: string;
  count: number;
}

export interface LanguageCount {
  language: string;
  count: number;
}

export interface CountryCount {
  country: string;
  count: number;
}

export interface FilterOptions {
  genres: GenreCount[];
  languages: LanguageCount[];
  countries: CountryCount[];
  years: number[];
  ratings: string[];
  qualities: string[];
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    // For server-side rendering, use absolute URL
    const baseUrl = this.baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');
    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${url}`, error);
      throw error;
    }
  }

  // Movies
  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<Movie>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    
    return this.request<PaginatedResponse<Movie>>(`/api/movies?${params.toString()}`);
  }

  async getMovie(id: string): Promise<Movie> {
    return this.request<Movie>(`/api/movies/${id}`);
  }

  // Series
  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<Series>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    
    return this.request<PaginatedResponse<Series>>(`/api/series?${params.toString()}`);
  }

  async getSeriesById(id: string): Promise<Series> {
    return this.request<Series>(`/api/series/${id}`);
  }

  async getSeriesEpisodes(id: string, season?: number): Promise<Episode[]> {
    const params = season ? `?season=${season}` : '';
    return this.request<Episode[]>(`/api/series/${id}/episodes${params}`);
  }

  // Episodes
  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<Episode>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    
    return this.request<PaginatedResponse<Episode>>(`/api/episodes?${params.toString()}`);
  }

  // Requests
  async createBulkRequest(imdbIds: string[], contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<{ requestId: string; status: string; totalCount: number; message: string }> {
    return this.request('/api/requests', {
      method: 'POST',
      body: JSON.stringify({ imdbIds, contentType }),
    });
  }

  async getRequestStatus(requestId: string): Promise<any> {
    return this.request(`/api/requests/${requestId}`);
  }

  // Filter Options
  async getMovieFilterOptions(): Promise<FilterOptions> {
    return this.request<FilterOptions>('/api/movies/filters');
  }

  async getSeriesFilterOptions(): Promise<FilterOptions> {
    return this.request<FilterOptions>('/api/series/filters');
  }

  async getEpisodeFilterOptions(): Promise<FilterOptions> {
    return this.request<FilterOptions>('/api/episodes/filters');
  }

  // Search
  async search(query: string, type: 'all' | 'movies' | 'series' | 'episodes' = 'all', page: number = 1, limit: number = 20): Promise<any> {
    const params = new URLSearchParams({
      q: query,
      type,
      page: page.toString(),
      limit: limit.toString()
    });
    return this.request(`/api/search?${params.toString()}`);
  }

  async getSearchSuggestions(query: string, limit: number = 8): Promise<any> {
    const params = new URLSearchParams({
      q: query,
      limit: limit.toString()
    });
    return this.request(`/api/search/suggestions?${params.toString()}`);
  }

  // Sync
  async syncContent(): Promise<{ success: boolean; message: string; counts: { movies: number; series: number; episodes: number } }> {
    return this.request('/api/sync', {
      method: 'POST',
    });
  }
}

export const apiClient = new ApiClient();
export default ApiClient;
