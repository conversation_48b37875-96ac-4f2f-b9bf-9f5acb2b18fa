import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { MongoClient } from 'mongodb';

/**
 * Force set isLatestRelease flags using direct MongoDB operations
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();
    
    console.log('🔧 Force setting isLatestRelease flags using direct MongoDB...');

    // Get MongoDB connection
    const mongoose = require('mongoose');
    const db = mongoose.connection.db;
    const seriesCollection = db.collection('series');

    // First, set all episodes isLatestRelease to false
    console.log('🧹 Setting all episodes isLatestRelease to false...');
    const resetResult = await seriesCollection.updateMany(
      { 'episodes.0': { $exists: true } },
      { $set: { 'episodes.$[].isLatestRelease': false } }
    );
    console.log(`✅ Reset ${resetResult.modifiedCount} series`);

    // Now set the first episode of each series to true for testing
    console.log('🎯 Setting first episode of each series to isLatestRelease: true...');
    const setResult = await seriesCollection.updateMany(
      { 'episodes.0': { $exists: true } },
      { $set: { 'episodes.0.isLatestRelease': true } }
    );
    console.log(`✅ Updated ${setResult.modifiedCount} series`);

    // Verify the changes
    const verifyCount = await seriesCollection.countDocuments({
      'episodes.isLatestRelease': true
    });
    console.log(`✅ Verification: ${verifyCount} series have episodes with isLatestRelease: true`);

    // Get a sample to verify
    const sampleSeries = await seriesCollection.findOne({
      'episodes.isLatestRelease': true
    });

    const sampleEpisode = sampleSeries?.episodes?.find(ep => ep.isLatestRelease === true);

    return NextResponse.json({
      success: true,
      message: 'Force set isLatestRelease flags using direct MongoDB',
      stats: {
        resetCount: resetResult.modifiedCount,
        setCount: setResult.modifiedCount,
        verificationCount: verifyCount
      },
      sampleEpisode: sampleEpisode ? {
        season: sampleEpisode.season,
        episode: sampleEpisode.episode,
        title: sampleEpisode.episodeTitle,
        isLatestRelease: sampleEpisode.isLatestRelease,
        hasField: sampleEpisode.hasOwnProperty('isLatestRelease')
      } : null
    });

  } catch (error) {
    console.error('❌ Force latest flags error:', error);
    return NextResponse.json(
      { error: 'Failed to force flags', message: error.message },
      { status: 500 }
    );
  }
}
