import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Movie from '@/models/Movie';
import Series from '@/models/Series';
import Episode from '@/models/Episode';
import VidSrcAPI from '@/lib/vidsrc';
import IMDbScraper from '@/lib/scraper';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting database initialization...');
    
    await connectDB();
    console.log('✅ Connected to MongoDB');

    const vidsrc = VidSrcAPI.getInstance();
    const scraper = IMDbScraper.getInstance();

    // Check if data already exists
    const movieCount = await Movie.countDocuments();
    const seriesCount = await Series.countDocuments();
    const episodeCount = await Episode.countDocuments();

    if (movieCount > 0 || seriesCount > 0 || episodeCount > 0) {
      return NextResponse.json({
        success: true,
        message: 'Database already initialized',
        counts: { movies: movieCount, series: seriesCount, episodes: episodeCount }
      });
    }

    // Fetch latest content from VidSrc
    console.log('📡 Fetching latest content from VidSrc...');
    const { movies, series, episodes } = await vidsrc.syncLatestContent(2); // Get 2 pages

    console.log(`Found ${movies.length} movies, ${series.length} series, ${episodes.length} episodes`);

    let processedMovies = 0;
    let processedSeries = 0;
    let processedEpisodes = 0;

    // Process a limited set of movies for initial setup
    console.log('🎬 Processing movies...');
    for (const movie of movies.slice(0, 10)) {
      try {
        console.log(`Processing movie: ${movie.title} (${movie.imdb_id})`);
        
        const movieData = await scraper.scrapeMovie(movie.imdb_id);
        
        await Movie.create({
          imdbId: movie.imdb_id,
          tmdbId: movie.tmdb_id,
          title: movieData.title,
          year: movieData.year,
          rating: movieData.rating,
          runtime: movieData.runtime,
          imdbRating: movieData.imdbRating,
          imdbVotes: movieData.imdbVotes,
          popularity: movieData.popularity,
          popularityDelta: movieData.popularityDelta,
          posterUrl: movieData.posterUrl,
          trailerUrl: movieData.trailerUrl,
          trailerRuntime: movieData.trailerRuntime,
          trailerLikes: movieData.trailerLikes,
          description: movieData.description,
          genres: movieData.genres,
          director: movieData.director,
          cast: movieData.cast,
          language: movieData.language,
          country: movieData.country,
          embedUrl: movie.embed_url,
          embedUrlTmdb: movie.embed_url_tmdb,
          quality: movie.quality
        });
        
        processedMovies++;
        console.log(`✅ Processed movie: ${movieData.title}`);
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`❌ Error processing movie ${movie.imdb_id}:`, error.message);
      }
    }

    // Process a limited set of series
    console.log('📺 Processing series...');
    const uniqueSeries = Array.from(
      new Map(series.map(s => [s.imdb_id, s])).values()
    ).slice(0, 5);

    for (const show of uniqueSeries) {
      try {
        console.log(`Processing series: ${show.show_title} (${show.imdb_id})`);
        
        const seriesData = await scraper.scrapeSeries(show.imdb_id);
        
        await Series.create({
          imdbId: show.imdb_id,
          tmdbId: show.tmdb_id,
          title: seriesData.title,
          startYear: seriesData.startYear,
          endYear: seriesData.endYear,
          rating: seriesData.rating,
          imdbRating: seriesData.imdbRating,
          imdbVotes: seriesData.imdbVotes,
          popularity: seriesData.popularity,
          popularityDelta: seriesData.popularityDelta,
          posterUrl: seriesData.posterUrl,
          trailerUrl: seriesData.trailerUrl,
          description: seriesData.description,
          genres: seriesData.genres,
          creator: seriesData.creator,
          cast: seriesData.cast,
          language: seriesData.language,
          country: seriesData.country,
          totalSeasons: seriesData.totalSeasons,
          status: seriesData.status,
          embedUrl: vidsrc.generateSeriesEmbedUrl(show.imdb_id),
          embedUrlTmdb: show.tmdb_id ? vidsrc.generateSeriesEmbedUrl(show.imdb_id, { tmdbId: show.tmdb_id }) : undefined
        });
        
        processedSeries++;
        console.log(`✅ Processed series: ${seriesData.title}`);
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`❌ Error processing series ${show.imdb_id}:`, error.message);
      }
    }

    // Process episodes
    console.log('📺 Processing episodes...');
    for (const episode of episodes.slice(0, 30)) {
      try {
        await Episode.create({
          imdbId: episode.imdb_id,
          tmdbId: episode.tmdb_id,
          seriesTitle: episode.show_title,
          season: parseInt(episode.season),
          episode: parseInt(episode.episode),
          embedUrl: episode.embed_url,
          embedUrlTmdb: episode.embed_url_tmdb,
          quality: episode.quality
        });
        
        processedEpisodes++;
      } catch (error) {
        console.error(`❌ Error processing episode ${episode.imdb_id} S${episode.season}E${episode.episode}:`, error.message);
      }
    }

    console.log('🎉 Database initialization completed!');
    
    return NextResponse.json({
      success: true,
      message: 'Database initialized successfully',
      counts: { 
        movies: processedMovies, 
        series: processedSeries, 
        episodes: processedEpisodes 
      }
    });

  } catch (error) {
    console.error('💥 Database initialization failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database initialization failed',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
