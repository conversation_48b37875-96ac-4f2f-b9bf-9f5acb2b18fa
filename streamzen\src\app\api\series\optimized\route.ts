import { NextRequest, NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

// Cache for series page data (3 minutes)
let seriesPageCache: {
  [key: string]: {
    data: any;
    timestamp: number;
  }
} = {};

const CACHE_DURATION = 3 * 60 * 1000; // 3 minutes

// Function to invalidate cache
export function invalidateSeriesPageCache() {
  console.log('🗑️ Series page cache invalidated');
  seriesPageCache = {};
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Create cache key from search params
    const cacheKey = searchParams.toString();
    
    // Check cache first
    if (seriesPageCache[cacheKey] && (Date.now() - seriesPageCache[cacheKey].timestamp) < CACHE_DURATION) {
      console.log('🚀 Serving series page data from cache');
      return NextResponse.json(seriesPageCache[cacheKey].data);
    }

    console.log('🔄 Fetching fresh series page data...');
    const startTime = Date.now();

    const filters = {
      genre: searchParams.get('genre') || undefined,
      year: searchParams.get('year') ? parseInt(searchParams.get('year')!) : undefined,
      language: searchParams.get('language') || undefined,
      country: searchParams.get('country') || undefined,
      rating: searchParams.get('rating') || undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1,
      limit: Math.min(searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 24, 48),
      search: searchParams.get('search') || undefined,
    };

    console.log(`📺 Optimized Series API - Page: ${filters.page}, Limit: ${filters.limit}, Sort: ${filters.sortBy} ${filters.sortOrder}`);

    // Fetch both content and filter options in parallel
    const [seriesResult, filterOptions] = await Promise.all([
      contentService.getSeries(filters),
      contentService.getSeriesFilterOptions()
    ]);

    const responseData = {
      content: seriesResult,
      filters: filterOptions,
      meta: {
        currentFilters: filters,
        hasActiveFilters: Object.entries(filters).some(
          ([key, value]) => value && key !== 'page' && key !== 'sortBy' && key !== 'sortOrder' && key !== 'limit'
        )
      }
    };

    // Cache the result
    seriesPageCache[cacheKey] = {
      data: responseData,
      timestamp: Date.now()
    };

    const endTime = Date.now();
    console.log(`✅ Optimized Series API completed in ${endTime - startTime}ms - ${seriesResult.data.length} items, total: ${seriesResult.pagination.total}`);

    // Add cache headers
    const response = NextResponse.json(responseData);
    response.headers.set('Cache-Control', 'public, max-age=180'); // 3 minute cache
    
    return response;

  } catch (error) {
    console.error('❌ Error in optimized series API:', error);
    
    // Return empty data structure on error
    const emptyData = {
      content: {
        data: [],
        pagination: { page: 1, limit: 24, total: 0, pages: 0 }
      },
      filters: {
        genres: [],
        languages: [],
        countries: [],
        years: [],
        ratings: []
      },
      meta: {
        currentFilters: {},
        hasActiveFilters: false
      }
    };

    return NextResponse.json(emptyData);
  }
}
