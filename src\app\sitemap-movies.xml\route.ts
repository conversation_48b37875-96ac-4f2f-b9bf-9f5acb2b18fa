import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Movie from '@/models/Movie';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlsXML = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlsXML}
</urlset>`;
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = 50000; // Max URLs per sitemap
    const skip = (page - 1) * limit;

    // Get movies with pagination, prioritizing popular and recent content
    const movies = await Movie.find({})
      .select('imdbId title year imdbRating popularity updatedAt createdAt')
      .sort({ 
        popularity: -1,  // Popular content first
        imdbRating: -1,  // High-rated content
        year: -1,        // Recent content
        createdAt: -1    // Newly added content
      })
      .skip(skip)
      .limit(limit)
      .lean();

    const urls: SitemapUrl[] = movies.map(movie => {
      // Calculate priority based on popularity and rating
      let priority = 0.6; // Base priority
      
      if (movie.popularity && movie.popularity > 80) priority = 0.9;
      else if (movie.popularity && movie.popularity > 60) priority = 0.8;
      else if (movie.imdbRating && movie.imdbRating > 8.0) priority = 0.8;
      else if (movie.imdbRating && movie.imdbRating > 7.0) priority = 0.7;
      else if (movie.year && movie.year >= new Date().getFullYear() - 2) priority = 0.7;

      // Determine change frequency
      let changefreq: SitemapUrl['changefreq'] = 'monthly';
      if (movie.year && movie.year >= new Date().getFullYear()) changefreq = 'weekly';
      else if (movie.year && movie.year >= new Date().getFullYear() - 1) changefreq = 'monthly';

      return {
        loc: `${BASE_URL}/watch/movie/${movie.imdbId}`,
        lastmod: movie.updatedAt?.toISOString() || movie.createdAt?.toISOString() || new Date().toISOString(),
        changefreq,
        priority: Math.round(priority * 10) / 10, // Round to 1 decimal
      };
    });

    const sitemapXML = generateSitemapXML(urls);

    return new NextResponse(sitemapXML, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating movies sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
