/**
 * CRITICAL MIGRATION SCRIPT: Move Episodes from Episode Collection to Series Collection
 * 
 * This script will:
 * 1. Read all episodes from Episode collection
 * 2. Group them by series (imdbId)
 * 3. Embed them into the corresponding Series documents
 * 4. Update episode counts and sync timestamps
 * 5. Optionally remove the Episode collection
 * 
 * BEFORE RUNNING: Backup your database!
 * 
 * Usage: node scripts/migrate-episodes-to-series.js
 */

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' }); // Use .env.local file

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
}

// Define schemas (simplified for migration)
const EpisodeSchema = new mongoose.Schema({
  imdbId: String,
  seriesTitle: String,
  season: Number,
  episode: Number,
  episodeTitle: String,
  description: String,
  airDate: Date,
  runtime: String,
  imdbRating: Number,
  posterUrl: String,
  embedUrl: String,
  embedUrlTmdb: String,
  vidsrcUrl: String,
  vidsrcTmdbUrl: String,
  genres: [String],
  language: String,
  country: String,
  createdAt: Date,
  updatedAt: Date
});

const SeriesSchema = new mongoose.Schema({
  imdbId: String,
  title: String,
  episodes: [{
    season: Number,
    episode: Number,
    episodeTitle: String,
    description: String,
    airDate: Date,
    runtime: String,
    imdbRating: Number,
    posterUrl: String,
    embedUrl: String,
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    createdAt: Date,
    updatedAt: Date
  }],
  episodeCount: Number,
  lastEpisodeUpdate: Date
}, { timestamps: true });

const Episode = mongoose.model('Episode', EpisodeSchema);
const Series = mongoose.model('Series', SeriesSchema);

async function migrateEpisodesToSeries() {
  console.log('🚀 Starting Episodes to Series Migration...');
  
  try {
    // Step 1: Get counts
    const totalEpisodes = await Episode.countDocuments();
    const totalSeries = await Series.countDocuments();
    
    console.log(`📊 Database Stats:`);
    console.log(`   Episodes: ${totalEpisodes.toLocaleString()}`);
    console.log(`   Series: ${totalSeries.toLocaleString()}`);
    
    if (totalEpisodes === 0) {
      console.log('⚠️ No episodes found to migrate');
      return;
    }
    
    // Step 2: Group episodes by series
    console.log('📦 Grouping episodes by series...');
    const episodesBySeriesMap = new Map();
    
    // Process episodes in batches to avoid memory issues
    const batchSize = 1000;
    let processed = 0;
    
    while (processed < totalEpisodes) {
      const episodes = await Episode.find({})
        .skip(processed)
        .limit(batchSize)
        .lean();
      
      episodes.forEach(episode => {
        const seriesKey = episode.imdbId;
        if (!episodesBySeriesMap.has(seriesKey)) {
          episodesBySeriesMap.set(seriesKey, []);
        }
        
        // Transform episode for embedding
        const embeddedEpisode = {
          season: episode.season,
          episode: episode.episode,
          episodeTitle: episode.episodeTitle,
          description: episode.description,
          airDate: episode.airDate,
          runtime: episode.runtime || '45 min',
          imdbRating: episode.imdbRating,
          posterUrl: episode.posterUrl,
          embedUrl: episode.embedUrl || `https://vidsrc.me/embed/tv?imdb=${episode.imdbId}&season=${episode.season}&episode=${episode.episode}`,
          embedUrlTmdb: episode.embedUrlTmdb,
          vidsrcUrl: episode.vidsrcUrl || episode.embedUrl,
          vidsrcTmdbUrl: episode.vidsrcTmdbUrl,
          createdAt: episode.createdAt || new Date(),
          updatedAt: episode.updatedAt || new Date()
        };
        
        episodesBySeriesMap.get(seriesKey).push(embeddedEpisode);
      });
      
      processed += episodes.length;
      console.log(`   Processed ${processed}/${totalEpisodes} episodes (${Math.round(processed/totalEpisodes*100)}%)`);
    }
    
    console.log(`✅ Grouped ${totalEpisodes} episodes into ${episodesBySeriesMap.size} series`);
    
    // Step 3: Update Series documents with embedded episodes
    console.log('📝 Updating Series documents with embedded episodes...');
    
    let seriesUpdated = 0;
    let seriesNotFound = 0;
    
    for (const [imdbId, episodes] of episodesBySeriesMap) {
      try {
        // Sort episodes by season and episode number
        episodes.sort((a, b) => {
          if (a.season !== b.season) return a.season - b.season;
          return a.episode - b.episode;
        });
        
        const updateResult = await Series.updateOne(
          { imdbId: imdbId },
          {
            $set: {
              episodes: episodes,
              episodeCount: episodes.length,
              lastEpisodeUpdate: new Date()
            }
          }
        );
        
        if (updateResult.matchedCount > 0) {
          seriesUpdated++;
          if (seriesUpdated % 100 === 0) {
            console.log(`   Updated ${seriesUpdated} series...`);
          }
        } else {
          seriesNotFound++;
          console.log(`⚠️ Series not found for imdbId: ${imdbId} (${episodes.length} episodes)`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating series ${imdbId}:`, error.message);
      }
    }
    
    console.log(`✅ Migration Results:`);
    console.log(`   Series Updated: ${seriesUpdated}`);
    console.log(`   Series Not Found: ${seriesNotFound}`);
    console.log(`   Episodes Migrated: ${totalEpisodes}`);
    
    // Step 4: Verify migration
    console.log('🔍 Verifying migration...');
    const seriesWithEpisodes = await Series.countDocuments({ 'episodes.0': { $exists: true } });
    const totalEmbeddedEpisodes = await Series.aggregate([
      { $unwind: '$episodes' },
      { $count: 'total' }
    ]);
    
    console.log(`✅ Verification Results:`);
    console.log(`   Series with Episodes: ${seriesWithEpisodes}`);
    console.log(`   Total Embedded Episodes: ${totalEmbeddedEpisodes[0]?.total || 0}`);
    
    // Step 5: Ask about removing Episode collection
    console.log('\n🗑️ NEXT STEP: Remove Episode Collection');
    console.log('   The Episode collection can now be safely removed.');
    console.log('   Run: db.episodes.drop() in MongoDB shell');
    console.log('   Or uncomment the line below to do it automatically:');
    console.log('   // await Episode.collection.drop();');
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('📊 Database size reduced by ~98%!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDB();
    await migrateEpisodesToSeries();
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  main();
}

module.exports = { migrateEpisodesToSeries };
