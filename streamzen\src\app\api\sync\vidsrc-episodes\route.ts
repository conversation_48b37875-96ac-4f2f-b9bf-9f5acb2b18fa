import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
// ❌ REMOVED: Episode collection - now using embedded episodes in Series
import VidSrcAPI from '@/lib/vidsrc';
import IMDbScraper from '@/lib/scraper';

const vidsrc = VidSrcAPI.getInstance();
const imdbScraper = IMDbScraper.getInstance();

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    console.log('🚀 Starting COMPREHENSIVE VidSrc Episodes Sync with Embedded Architecture');
    console.log('📋 New Architecture: Episodes embedded in Series collection with isLatestRelease flag');
    const startTime = Date.now();

    // STEP 1: Get current VidSrc latest episodes
    console.log('📊 STEP 1: Fetching current VidSrc latest episodes...');
    
    // **RATE-LIMITED BATCH FETCH** - Fetch pages in smaller batches to avoid 503 errors
    console.log('📄 Fetching VidSrc episodes pages 1-15 in rate-limited batches...');

    const allEpisodes = [];
    const batchSize = 3; // Fetch 3 pages at a time to avoid overwhelming the API
    const delayBetweenBatches = 2000; // 2 second delay between batches

    for (let i = 1; i <= 15; i += batchSize) {
      const batchEnd = Math.min(i + batchSize - 1, 15);
      console.log(`📦 Fetching batch: pages ${i}-${batchEnd}...`);

      const batchPromises = [];
      for (let page = i; page <= batchEnd; page++) {
        batchPromises.push(
          vidsrc.getLatestEpisodes(page).catch(error => {
            console.error(`❌ Error fetching page ${page}:`, error.message || error);
            return []; // Return empty array on error
          })
        );
      }

      // Wait for current batch to complete
      const batchResults = await Promise.all(batchPromises);
      const batchEpisodes = batchResults.flat();
      allEpisodes.push(...batchEpisodes);

      console.log(`✅ Batch ${i}-${batchEnd}: Found ${batchEpisodes.length} episodes`);

      // Add delay between batches (except for the last batch)
      if (batchEnd < 15) {
        console.log(`⏳ Waiting ${delayBetweenBatches/1000}s before next batch...`);
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }
    
    console.log(`✅ Rate-limited fetch complete: ${allEpisodes.length} episodes from 15 pages`);

    if (allEpisodes.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No episodes found from VidSrc API - all pages may have failed due to rate limiting or server issues',
        recommendation: 'Try again later or check VidSrc API status'
      });
    }

    // STEP 2: Identify episodes to remove (cleanup) - Now working with embedded episodes
    console.log('🧹 STEP 2: Cleaning up outdated VidSrc episodes from Series collection...');

    // Get current VidSrc IMDb IDs and episode identifiers
    const currentVidSrcEpisodes = new Set();
    allEpisodes.forEach(ep => {
      const episodeKey = `${ep.imdb_id}-S${ep.season}E${ep.episode}`;
      currentVidSrcEpisodes.add(episodeKey);
    });

    // CLEANUP: Remove isLatestRelease flag from episodes no longer in VidSrc
    console.log('🧹 Removing isLatestRelease flag from outdated episodes...');

    // Update all series to remove isLatestRelease flag from episodes not in current VidSrc
    const seriesWithEpisodes = await Series.find({
      'episodes.isLatestRelease': true
    });

    let cleanupCount = 0;
    for (const series of seriesWithEpisodes) {
      let episodesUpdated = false;

      series.episodes.forEach(episode => {
        if (episode.isLatestRelease) {
          const episodeKey = `${series.imdbId}-S${episode.season}E${episode.episode}`;
          if (!currentVidSrcEpisodes.has(episodeKey)) {
            episode.isLatestRelease = false; // Remove latest release flag
            episodesUpdated = true;
            cleanupCount++;
          }
        }
      });

      if (episodesUpdated) {
        await series.save();
      }
    }

    console.log(`📊 Episodes cleanup analysis:`);
    console.log(`   Current VidSrc episodes: ${currentVidSrcEpisodes.size}`);
    console.log(`   Episodes cleaned up: ${cleanupCount}`);
    
    // STEP 3: Process new/updated episodes
    console.log('🔄 STEP 3: Processing new/updated episodes...');

    // Get unique IMDb IDs and log sample data
    const uniqueImdbIds = [...new Set(allEpisodes.map(ep => ep.imdb_id))];
    console.log(`🎬 Found ${uniqueImdbIds.length} unique series`);

    // Debug: Show sample VidSrc episode data
    if (allEpisodes.length > 0) {
      console.log('📝 Sample VidSrc episode data:', allEpisodes.slice(0, 2).map(ep => ({
        imdb_id: ep.imdb_id,
        show_title: ep.show_title,
        season: ep.season,
        episode: ep.episode,
        title: ep.title
      })));
    }
    
    // **PARALLEL SERIES PROCESSING** - Process series in batches
    const seriesBatchSize = 10; // Process 10 series at a time
    const processedSeries = new Map();
    
    for (let i = 0; i < uniqueImdbIds.length; i += seriesBatchSize) {
      const batch = uniqueImdbIds.slice(i, i + seriesBatchSize);
      console.log(`🔄 Processing series batch ${Math.floor(i/seriesBatchSize) + 1}/${Math.ceil(uniqueImdbIds.length/seriesBatchSize)}`);
      
      const batchPromises = batch.map(async (imdbId) => {
        try {
          // Check if series exists
          let series = await Series.findOne({ imdbId });
          
          if (!series) {
            console.log(`🆕 Creating series: ${imdbId}`);
            
            // Find episode data for this series
            const episodeData = allEpisodes.find(ep => ep.imdb_id === imdbId);
            
            try {
              // Scrape series data
              const seriesData = await imdbScraper.scrapeSeries(imdbId);
              const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;
              const currentYear = new Date().getFullYear();
              
              series = new Series({
                imdbId,
                title: seriesData.title || episodeData?.show_title || 'Unknown Series',
                description: seriesData.description,
                posterUrl: seriesData.posterUrl,
                imdbRating: seriesData.imdbRating,
                startYear: seriesData.startYear || currentYear,
                endYear: seriesData.endYear,
                genres: seriesData.genres || [],
                cast: seriesData.cast || [],
                director: seriesData.director,
                language: seriesData.language,
                country: seriesData.country,
                runtime: seriesData.runtime,
                embedUrl,
                type: 'series',
                episodes: [], // ✅ Initialize empty episodes array
                episodeCount: 0,
                lastEpisodeUpdate: new Date()
              });
              
              await series.save();
              console.log(`✅ Created: ${series.title}`);
            } catch (error) {
              // Create minimal series if scraping fails
              const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;
              const currentYear = new Date().getFullYear();
              
              series = new Series({
                imdbId,
                title: episodeData?.show_title || 'Unknown Series',
                startYear: currentYear,
                embedUrl,
                type: 'series',
                genres: [],
                episodes: [], // ✅ Initialize empty episodes array
                episodeCount: 0,
                lastEpisodeUpdate: new Date()
              });
              
              await series.save();
              console.log(`✅ Created minimal: ${series.title}`);
            }
          }
          
          // Now process ALL episodes for this series - USING EMBEDDED EPISODES
          const seriesEpisodes = allEpisodes.filter(ep => ep.imdb_id === imdbId);
          console.log(`📺 Processing ${seriesEpisodes.length} VidSrc episodes for ${series.title}`);

          let episodesUpdated = false;

          // First, clear isLatestRelease flag from all episodes in this series
          series.episodes.forEach(episode => {
            if (episode.isLatestRelease) {
              episode.isLatestRelease = false;
              episodesUpdated = true;
            }
          });

          // Process all VidSrc episodes for this series and mark them as latest releases
          for (const episodeData of seriesEpisodes) {
            // Generate episode embed URL
            const episodeEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}&season=${episodeData.season}&episode=${episodeData.episode}`;

            // Check if episode already exists in embedded episodes array
            const existingEpisodeIndex = series.episodes.findIndex(ep =>
              ep.season === parseInt(episodeData.season) && ep.episode === parseInt(episodeData.episode)
            );

            if (existingEpisodeIndex === -1) {
              // Add new embedded episode and mark as VidSrc latest release
              const newEpisode = {
                season: parseInt(episodeData.season),
                episode: parseInt(episodeData.episode),
                episodeTitle: episodeData.title || `Episode ${episodeData.episode}`,
                description: episodeData.description || `${series.title} - Season ${episodeData.season}, Episode ${episodeData.episode}`,
                airDate: episodeData.air_date ? new Date(episodeData.air_date) : new Date(),
                runtime: episodeData.runtime || '45 min',
                embedUrl: episodeEmbedUrl,
                embedUrlTmdb: episodeData.embed_url_tmdb || '',
                vidsrcUrl: episodeEmbedUrl,
                vidsrcTmdbUrl: episodeData.embed_url_tmdb || '',
                isLatestRelease: true, // ✅ Mark as VidSrc latest release
                createdAt: new Date(),
                updatedAt: new Date()
              };

              series.episodes.push(newEpisode);
              episodesUpdated = true;
              console.log(`✅ Added NEW VidSrc latest episode: S${episodeData.season}E${episodeData.episode} - ${episodeData.title || 'Episode ' + episodeData.episode}`);
            } else {
              // Update existing embedded episode and mark as VidSrc latest release
              const existingEpisode = series.episodes[existingEpisodeIndex];

              // Update episode data with VidSrc information
              existingEpisode.embedUrl = episodeEmbedUrl;
              existingEpisode.vidsrcUrl = episodeEmbedUrl;
              existingEpisode.embedUrlTmdb = episodeData.embed_url_tmdb || existingEpisode.embedUrlTmdb || '';
              existingEpisode.vidsrcTmdbUrl = episodeData.embed_url_tmdb || existingEpisode.vidsrcTmdbUrl || '';
              existingEpisode.isLatestRelease = true; // ✅ Mark as VidSrc latest release
              existingEpisode.updatedAt = new Date();

              // Update title if VidSrc has a better one
              if (episodeData.title && episodeData.title !== `Episode ${episodeData.episode}`) {
                existingEpisode.episodeTitle = episodeData.title;
              }

              episodesUpdated = true;
              console.log(`🔄 Updated existing VidSrc latest episode: S${episodeData.season}E${episodeData.episode} - ${existingEpisode.episodeTitle}`);
            }
          }

          // Log summary of VidSrc latest episodes processed
          if (seriesEpisodes.length > 0) {
            console.log(`✅ Processed ${seriesEpisodes.length} VidSrc latest episodes for ${series.title}`);
          } else {
            console.log(`⚠️ No VidSrc episodes found for ${series.title}`);
          }

          // Save series with updated embedded episodes
          if (episodesUpdated) {
            series.episodeCount = series.episodes.length;
            series.lastEpisodeUpdate = new Date();
            await series.save();
          }

          processedSeries.set(imdbId, series);
          return { imdbId, success: true };
        } catch (error) {
          console.error(`❌ Error processing series ${imdbId}:`, error);
          return { imdbId, success: false, error: error.message };
        }
      });
      
      await Promise.all(batchPromises);
    }
    
    console.log(`✅ Series processing complete: ${processedSeries.size} series processed`);
    
    // Update sync timestamp
    const syncTime = new Date();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`🎉 VidSrc Episodes Sync Complete in ${duration}s`);
    
    return NextResponse.json({
      success: true,
      message: `Sync completed successfully`,
      stats: {
        totalEpisodes: allEpisodes.length,
        uniqueSeries: uniqueImdbIds.length,
        processedSeries: processedSeries.size,
        duration: `${duration}s`,
        syncTime: syncTime.toISOString()
      }
    });
    
  } catch (error) {
    console.error('❌ VidSrc Episodes Sync Error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Sync failed',
        message: error.message
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check last sync status
export async function GET() {
  try {
    await connectDB();
    
    const totalSeries = await Series.countDocuments({ type: 'series' });
    const totalEpisodes = await Episode.countDocuments({});
    
    return NextResponse.json({
      success: true,
      stats: {
        totalSeries,
        totalEpisodes,
        lastSync: 'Check logs for last sync time'
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get sync status' },
      { status: 500 }
    );
  }
}
