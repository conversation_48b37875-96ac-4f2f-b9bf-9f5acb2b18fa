'use client';

import React, { useState } from 'react';
import { Send, Loader2, CheckCircle, AlertCircle, Info, Sparkles } from 'lucide-react';
import Button from './ui/Button';
import { apiClient } from '@/lib/api';

const RequestForm: React.FC = () => {
  const [imdbIds, setImdbIds] = useState('');
  const [contentType, setContentType] = useState<'auto' | 'movie' | 'series'>('auto');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  const [requestId, setRequestId] = useState<string | null>(null);

  const validateImdbIds = (ids: string[]): { valid: string[]; invalid: string[] } => {
    const valid: string[] = [];
    const invalid: string[] = [];
    
    ids.forEach(id => {
      const trimmedId = id.trim();
      if (trimmedId.match(/^tt\d{7,}$/)) {
        valid.push(trimmedId);
      } else if (trimmedId) {
        invalid.push(trimmedId);
      }
    });
    
    return { valid, invalid };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!imdbIds.trim()) {
      setStatus('error');
      setMessage('Please enter at least one IMDb ID');
      return;
    }

    const ids = imdbIds.split('\n').map(id => id.trim()).filter(Boolean);
    
    if (ids.length === 0) {
      setStatus('error');
      setMessage('Please enter at least one IMDb ID');
      return;
    }

    if (ids.length > 50) {
      setStatus('error');
      setMessage('Maximum 50 IMDb IDs allowed per request');
      return;
    }

    const { valid, invalid } = validateImdbIds(ids);
    
    if (invalid.length > 0) {
      setStatus('error');
      setMessage(`Invalid IMDb IDs: ${invalid.join(', ')}. IMDb IDs should start with 'tt' followed by at least 7 digits.`);
      return;
    }

    if (valid.length === 0) {
      setStatus('error');
      setMessage('No valid IMDb IDs found');
      return;
    }

    setIsSubmitting(true);
    setStatus('loading');
    setMessage('Submitting request...');

    try {
      const response = await apiClient.createBulkRequest(valid, contentType);

      setStatus('success');
      setMessage(`Request submitted successfully! Processing ${response.totalCount} items as ${contentType === 'auto' ? 'auto-detected content' : contentType === 'movie' ? 'movies' : 'series'}.`);
      setRequestId(response.requestId);
      setImdbIds('');

      // Start polling for status updates
      pollRequestStatus(response.requestId);

    } catch (error) {
      setStatus('error');
      setMessage('Failed to submit request. Please try again.');
      console.error('Request submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const pollRequestStatus = async (id: string) => {
    try {
      const status = await apiClient.getRequestStatus(id);
      
      if (status.status === 'completed') {
        setMessage(`Request completed! Successfully processed ${status.processedCount} out of ${status.totalCount} items.`);
      } else if (status.status === 'failed') {
        setMessage(`Request completed with errors. Processed ${status.processedCount} out of ${status.totalCount} items.`);
      } else if (status.status === 'processing') {
        setMessage(`Processing... ${status.processedCount} of ${status.totalCount} completed.`);
        // Continue polling
        setTimeout(() => pollRequestStatus(id), 5000);
      }
    } catch (error) {
      console.error('Error polling request status:', error);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="animate-spin" size={20} />;
      case 'success':
        return <CheckCircle className="text-green-400" size={20} />;
      case 'error':
        return <AlertCircle className="text-red-400" size={20} />;
      default:
        return <Send size={20} />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'bg-gradient-to-br from-green-900/30 to-green-800/30 border-green-500/30 text-green-300';
      case 'error':
        return 'bg-gradient-to-br from-red-900/30 to-red-800/30 border-red-500/30 text-red-300';
      case 'loading':
        return 'bg-gradient-to-br from-blue-900/30 to-blue-800/30 border-blue-500/30 text-blue-300';
      default:
        return '';
    }
  };

  return (
    <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl p-8 border border-gray-800/50">
      <div className="flex items-center mb-8">
        <div className="w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-2xl flex items-center justify-center mr-4">
          <Send className="w-6 h-6 text-white" />
        </div>
        <div>
          <h2 className="text-white font-bold text-2xl">Submit Request</h2>
          <p className="text-gray-400 text-sm">Add new content to StreamZen</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Content Type Selector */}
        <div>
          <label className="block text-white font-semibold text-lg mb-4">
            Content Type
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <button
              type="button"
              onClick={() => setContentType('auto')}
              className={`group relative p-6 rounded-2xl border-2 transition-all duration-300 ${
                contentType === 'auto'
                  ? 'bg-gradient-to-br from-red-600/20 to-red-700/20 border-red-500 text-white shadow-lg shadow-red-500/20'
                  : 'bg-gray-800/50 border-gray-700 text-gray-300 hover:bg-gray-800/70 hover:border-gray-600'
              }`}
            >
              <div className="text-center">
                <div className="w-8 h-8 mx-auto mb-3 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <div className="font-semibold text-base">Auto Detect</div>
                <div className="text-xs opacity-75 mt-2">Recommended</div>
              </div>
              {contentType === 'auto' && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
              )}
            </button>

            <button
              type="button"
              onClick={() => setContentType('movie')}
              className={`group relative p-6 rounded-2xl border-2 transition-all duration-300 ${
                contentType === 'movie'
                  ? 'bg-gradient-to-br from-red-600/20 to-red-700/20 border-red-500 text-white shadow-lg shadow-red-500/20'
                  : 'bg-gray-800/50 border-gray-700 text-gray-300 hover:bg-gray-800/70 hover:border-gray-600'
              }`}
            >
              <div className="text-center">
                <div className="w-8 h-8 mx-auto mb-3 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white font-bold text-sm">🎬</span>
                </div>
                <div className="font-semibold text-base">Movies Only</div>
                <div className="text-xs opacity-75 mt-2">Force movie type</div>
              </div>
              {contentType === 'movie' && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
              )}
            </button>

            <button
              type="button"
              onClick={() => setContentType('series')}
              className={`group relative p-6 rounded-2xl border-2 transition-all duration-300 ${
                contentType === 'series'
                  ? 'bg-gradient-to-br from-red-600/20 to-red-700/20 border-red-500 text-white shadow-lg shadow-red-500/20'
                  : 'bg-gray-800/50 border-gray-700 text-gray-300 hover:bg-gray-800/70 hover:border-gray-600'
              }`}
            >
              <div className="text-center">
                <div className="w-8 h-8 mx-auto mb-3 rounded-lg bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                  <span className="text-white font-bold text-sm">📺</span>
                </div>
                <div className="font-semibold text-base">Series Only</div>
                <div className="text-xs opacity-75 mt-2">Force series type</div>
              </div>
              {contentType === 'series' && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
              )}
            </button>
          </div>

          <div className="mt-4 p-4 bg-gray-800/30 rounded-xl">
            <p className="text-gray-300 text-sm leading-relaxed">
              {contentType === 'auto' && (
                <>
                  <span className="text-blue-400 font-medium">Auto Detection:</span> Our system will automatically analyze each IMDb ID and determine whether it's a movie or TV series, then process accordingly with full metadata scraping.
                </>
              )}
              {contentType === 'movie' && (
                <>
                  <span className="text-purple-400 font-medium">Movies Only:</span> All provided IMDb IDs will be processed as movies with complete metadata including cast, genres, and streaming links.
                </>
              )}
              {contentType === 'series' && (
                <>
                  <span className="text-green-400 font-medium">Series Only:</span> All provided IMDb IDs will be processed as TV series with automatic season and episode detection, complete metadata scraping.
                </>
              )}
            </p>
          </div>
        </div>

        <div>
          <label htmlFor="imdbIds" className="block text-white font-semibold text-lg mb-4">
            IMDb IDs
          </label>
          <div className="relative">
            <textarea
              id="imdbIds"
              value={imdbIds}
              onChange={(e) => setImdbIds(e.target.value)}
              placeholder="Enter IMDb IDs, one per line&#10;&#10;Examples:&#10;tt0111161 (The Shawshank Redemption)&#10;tt0068646 (The Godfather)&#10;tt0944947 (Game of Thrones)&#10;tt1316554 (Black Butler)"
              className="w-full h-40 px-4 py-4 bg-gray-800/50 border-2 border-gray-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none transition-all duration-200 text-sm leading-relaxed"
              disabled={isSubmitting}
            />
            <div className="absolute bottom-4 right-4 flex items-center space-x-2">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                imdbIds.split('\n').filter(Boolean).length > 50
                  ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                  : imdbIds.split('\n').filter(Boolean).length > 40
                  ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                  : 'bg-gray-700/50 text-gray-400 border border-gray-600/30'
              }`}>
                {imdbIds.split('\n').filter(Boolean).length} / 50
              </div>
            </div>
          </div>
          <div className="mt-3 flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2 text-gray-400">
              <Info size={16} />
              <span>One IMDb ID per line • Maximum 50 IDs per request</span>
            </div>
          </div>
        </div>

        <Button
          type="submit"
          disabled={isSubmitting || !imdbIds.trim() || imdbIds.split('\n').filter(Boolean).length > 50}
          className="w-full h-14 flex items-center justify-center space-x-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 disabled:from-gray-700 disabled:to-gray-800 text-white font-semibold text-lg rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none"
        >
          {getStatusIcon()}
          <span>{isSubmitting ? 'Processing Request...' : 'Submit Request'}</span>
        </Button>
      </form>

      {message && (
        <div className={`mt-6 p-6 rounded-2xl border-2 ${getStatusColor()}`}>
          <div className="flex items-start space-x-3">
            {getStatusIcon()}
            <div className="flex-1">
              <p className="text-sm leading-relaxed">{message}</p>
              {requestId && (
                <div className="mt-3 p-3 bg-black/20 rounded-lg">
                  <p className="text-xs opacity-75 font-mono">
                    Request ID: {requestId}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 p-6 bg-gray-800/30 rounded-2xl border border-gray-700/50">
        <div className="flex items-center mb-4">
          <div className="w-8 h-8 bg-blue-600/20 rounded-lg flex items-center justify-center mr-3">
            <Info className="w-4 h-4 text-blue-400" />
          </div>
          <h3 className="text-white font-semibold text-lg">Quick Tips</h3>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-300">
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>IMDb IDs start with "tt" + 7+ digits (e.g., tt0111161)</span>
            </div>
            <div className="flex items-start">
              <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>Find IMDb IDs in any IMDb page URL</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>Maximum 50 IDs per request</span>
            </div>
            <div className="flex items-start">
              <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>Processing: 2-5 minutes per item</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestForm;
