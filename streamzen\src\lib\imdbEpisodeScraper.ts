import axios from 'axios';
import * as cheerio from 'cheerio';

interface IMDbEpisodeData {
  season: number;
  episode: number;
  title: string;
  description?: string;
  airDate?: string;
  rating?: number;
  imdbId?: string;
  duration?: string;
}

interface IMDbSeasonData {
  seasonNumber: number;
  episodeCount: number;
  episodes: IMDbEpisodeData[];
  year?: string;
}

interface IMDbSeriesEpisodeInfo {
  totalEpisodes: number;
  totalSeasons: number;
  seasons: IMDbSeasonData[];
  seriesTitle: string;
  seriesYear?: string;
  lastUpdated: string;
}

class IMDbEpisodeScraper {
  private static instance: IMDbEpisodeScraper;
  private readonly baseUrl = 'https://www.imdb.com';
  private readonly headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
  };

  // Rate limiting properties
  private lastRequestTime = 0;
  private readonly minRequestInterval = 1000; // 1 second between requests
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds between retries

  static getInstance(): IMDbEpisodeScraper {
    if (!IMDbEpisodeScraper.instance) {
      IMDbEpisodeScraper.instance = new IMDbEpisodeScraper();
    }
    return IMDbEpisodeScraper.instance;
  }

  /**
   * Rate-limited HTTP request with retry logic
   */
  private async makeRequest(url: string, retryCount = 0): Promise<any> {
    // Rate limiting: ensure minimum interval between requests
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    this.lastRequestTime = Date.now();

    try {
      console.log(`📄 Fetching (attempt ${retryCount + 1}): ${url}`);
      const response = await axios.get(url, {
        headers: this.headers,
        timeout: 10000 // 10 second timeout
      });
      return response;
    } catch (error: any) {
      if (error.response?.status === 503 && retryCount < this.maxRetries) {
        const delay = this.retryDelay * (retryCount + 1); // Exponential backoff
        console.log(`⚠️ 503 error, retrying in ${delay}ms (attempt ${retryCount + 1}/${this.maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequest(url, retryCount + 1);
      }
      throw error;
    }
  }

  /**
   * ADVANCED: Get comprehensive episode information for a series from IMDb
   */
  async getSeriesEpisodes(imdbId: string): Promise<IMDbSeriesEpisodeInfo | null> {
    try {
      console.log(`🎬 ADVANCED: Scraping complete episode data from IMDb for ${imdbId}`);
      
      // Step 1: Get main series page to extract basic info
      const mainPageData = await this.getMainPageData(imdbId);
      if (!mainPageData) {
        console.log(`❌ Could not access main page for ${imdbId}`);
        return null;
      }

      // Step 2: Get episodes page for detailed episode information using improved season detection
      const episodesData = await this.getEpisodesPageDataWithSeasonTabs(imdbId);
      if (!episodesData) {
        console.log(`❌ Could not access episodes page for ${imdbId}`);
        return null;
      }

      // Step 3: Get detailed episode information
      let detailedSeasons: IMDbSeasonData[] = [];

      if (episodesData.seasons.length > 0) {
        // Multi-season show: scrape each season
        detailedSeasons = await this.getDetailedSeasonData(imdbId, episodesData.seasons);
      } else if (mainPageData.totalEpisodes && mainPageData.totalEpisodes > 0) {
        // Single season or no season structure: try to scrape episodes directly
        console.log(`📺 No seasons found, attempting to scrape episodes directly...`);
        const directEpisodes = await this.getEpisodesDirectly(imdbId);
        if (directEpisodes && directEpisodes.length > 0) {
          detailedSeasons = [{
            seasonNumber: 1,
            episodeCount: directEpisodes.length,
            episodes: directEpisodes,
            year: mainPageData.year
          }];
        } else {
          // Fallback: Create episodes based on total count from main page
          console.log(`📺 Direct scraping failed, creating episodes based on total count: ${mainPageData.totalEpisodes}`);
          const fallbackEpisodes: IMDbEpisodeData[] = [];
          const totalSeasons = mainPageData.totalSeasons || 1;
          const episodesPerSeason = Math.ceil(mainPageData.totalEpisodes / totalSeasons);

          for (let season = 1; season <= totalSeasons; season++) {
            const seasonEpisodes: IMDbEpisodeData[] = [];
            const startEpisode = (season - 1) * episodesPerSeason + 1;
            const endEpisode = Math.min(season * episodesPerSeason, mainPageData.totalEpisodes);

            for (let episode = startEpisode; episode <= endEpisode; episode++) {
              const actualEpisodeNumber = episode - startEpisode + 1;
              seasonEpisodes.push({
                season: season,
                episode: actualEpisodeNumber,
                title: `Episode ${actualEpisodeNumber}`,
                description: undefined,
                airDate: undefined,
                rating: undefined,
                imdbId: undefined,
                duration: undefined
              });
            }

            if (seasonEpisodes.length > 0) {
              detailedSeasons.push({
                seasonNumber: season,
                episodeCount: seasonEpisodes.length,
                episodes: seasonEpisodes,
                year: mainPageData.year
              });
            }
          }

          console.log(`✅ Fallback: Created ${detailedSeasons.length} seasons with ${mainPageData.totalEpisodes} episodes`);
        }
      }

      const result: IMDbSeriesEpisodeInfo = {
        totalEpisodes: mainPageData.totalEpisodes || episodesData.totalEpisodes,
        totalSeasons: Math.max(mainPageData.totalSeasons || 0, detailedSeasons.length),
        seasons: detailedSeasons,
        seriesTitle: mainPageData.title,
        seriesYear: mainPageData.year,
        lastUpdated: new Date().toISOString()
      };

      console.log(`✅ IMDb scraping complete: ${result.totalEpisodes} episodes across ${result.totalSeasons} seasons`);
      return result;

    } catch (error) {
      console.error(`❌ Error scraping IMDb episodes for ${imdbId}:`, error);
      return null;
    }
  }

  /**
   * Get basic series information from main IMDb page
   */
  private async getMainPageData(imdbId: string): Promise<{
    title: string;
    year?: string;
    totalEpisodes?: number;
    totalSeasons?: number;
  } | null> {
    try {
      const url = `${this.baseUrl}/title/${imdbId}/`;

      const response = await this.makeRequest(url);
      const $ = cheerio.load(response.data);

      // Extract series title
      const title = $('h1[data-testid="hero__pageTitle"] span').first().text().trim() ||
                   $('h1').first().text().trim() ||
                   'Unknown Title';

      // Extract year
      const yearText = $('h1[data-testid="hero__pageTitle"]').text();
      const yearMatch = yearText.match(/\((\d{4})\)/);
      const year = yearMatch ? yearMatch[1] : undefined;

      // Extract episode count from episodes widget
      const episodeCountText = $('section[data-testid="episodes-widget"] .ipc-title__subtext').text();
      const totalEpisodes = episodeCountText ? parseInt(episodeCountText) : undefined;

      // Extract season count from season selector - try multiple selectors
      const seasonSelectors = [
        'select[id="browse-episodes-season"] option',
        '#browse-episodes-season option',
        'select[aria-label*="season"] option',
        '.ipc-simple-select__input option'
      ];

      let totalSeasons = 0;
      let foundSeasons = false;

      for (const selector of seasonSelectors) {
        const seasonOptions = $(selector);
        if (seasonOptions.length > 0) {
          console.log(`📍 Found season options on main page using: ${selector}`);
          seasonOptions.each((_, element) => {
            const value = $(element).attr('value');
            if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {
              totalSeasons = Math.max(totalSeasons, parseInt(value));
              foundSeasons = true;
            }
          });
          if (foundSeasons) break;
        }
      }

      if (!foundSeasons) {
        console.log(`❌ No season selector found on main page`);
      }

      console.log(`📊 Main page data: ${title} (${year}) - ${totalEpisodes} episodes, ${totalSeasons} seasons`);

      return {
        title,
        year,
        totalEpisodes,
        totalSeasons
      };

    } catch (error) {
      console.error(`❌ Error fetching main page for ${imdbId}:`, error);
      return null;
    }
  }

  /**
   * Get episodes page data with season information using season tabs
   */
  private async getEpisodesPageDataWithSeasonTabs(imdbId: string): Promise<{
    totalEpisodes: number;
    seasons: { seasonNumber: number; year?: string }[];
  } | null> {
    try {
      const url = `${this.baseUrl}/title/${imdbId}/episodes/`;

      const response = await this.makeRequest(url);
      const $ = cheerio.load(response.data);

      // First, try to detect seasons from the season tabs (new IMDb structure)
      const seasonTabs = $('a[data-testid="tab-season-entry"]');
      const detectedSeasons: { seasonNumber: number; year?: string }[] = [];

      if (seasonTabs.length > 0) {
        console.log(`📍 Found ${seasonTabs.length} season tabs using new IMDb structure`);

        seasonTabs.each((_, element) => {
          const href = $(element).attr('href');
          const seasonText = $(element).text().trim();

          if (href) {
            const seasonMatch = href.match(/season=(\d+)/);
            if (seasonMatch) {
              const seasonNumber = parseInt(seasonMatch[1]);
              detectedSeasons.push({ seasonNumber, year: undefined });
              console.log(`✅ Detected Season ${seasonNumber} from tab`);
            }
          } else if (seasonText && !isNaN(parseInt(seasonText))) {
            // Fallback: use tab text as season number
            const seasonNumber = parseInt(seasonText);
            detectedSeasons.push({ seasonNumber, year: undefined });
            console.log(`✅ Detected Season ${seasonNumber} from tab text`);
          }
        });
      }

      // If season tabs found, return them
      if (detectedSeasons.length > 0) {
        return {
          totalEpisodes: 0, // Will be calculated from actual episodes
          seasons: detectedSeasons.sort((a, b) => a.seasonNumber - b.seasonNumber)
        };
      }

      // Fallback to old method if no season tabs found
      console.log(`❌ No season tabs found, falling back to dropdown detection`);
      return await this.getEpisodesPageData(imdbId);

    } catch (error) {
      console.error(`❌ Error fetching episodes page for season detection:`, error);
      return null;
    }
  }

  /**
   * Get episodes page data with season information (fallback method)
   */
  private async getEpisodesPageData(imdbId: string): Promise<{
    totalEpisodes: number;
    seasons: { seasonNumber: number; year?: string }[];
  } | null> {
    try {
      const url = `${this.baseUrl}/title/${imdbId}/episodes/`;

      const response = await this.makeRequest(url);
      const $ = cheerio.load(response.data);

      // Extract total episodes
      const episodeCountText = $('section[data-testid="episodes-widget"] .ipc-title__subtext').text() ||
                              $('.episodes-section .ipc-title__subtext').text();
      const totalEpisodes = episodeCountText ? parseInt(episodeCountText) : 0;

      // Extract seasons from dropdown - use multiple selectors to find the season dropdown
      const seasons: { seasonNumber: number; year?: string }[] = [];
      const seasonSelectors = [
        'select[id="browse-episodes-season"] option',
        '#browse-episodes-season option',
        'select[aria-label*="season"] option',
        '.ipc-simple-select__input option'
      ];

      let seasonOptions: cheerio.Cheerio<cheerio.Element> | null = null;
      for (const selector of seasonSelectors) {
        seasonOptions = $(selector);
        if (seasonOptions.length > 0) {
          console.log(`📍 Found season options using selector: ${selector}`);
          break;
        }
      }

      if (seasonOptions && seasonOptions.length > 0) {
        seasonOptions.each((_, element) => {
          const value = $(element).attr('value');
          const text = $(element).text().trim();

          console.log(`🔍 Season option: value="${value}", text="${text}"`);

          if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {
            seasons.push({
              seasonNumber: parseInt(value),
              year: undefined // Will be filled in detailed scraping
            });
            console.log(`✅ Added season ${parseInt(value)}`);
          }
        });
      } else {
        console.log(`❌ No season dropdown found with any selector`);
      }

      // Also extract years if available
      const yearOptions = $('select[id="browse-episodes-year"] option[value]');
      const years: string[] = [];
      yearOptions.each((_, element) => {
        const value = $(element).attr('value');
        if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {
          years.push(value);
        }
      });

      console.log(`📊 Episodes page data: ${totalEpisodes} episodes, ${seasons.length} seasons, years: ${years.join(', ')}`);

      return {
        totalEpisodes,
        seasons: seasons.sort((a, b) => a.seasonNumber - b.seasonNumber)
      };

    } catch (error) {
      console.error(`❌ Error fetching episodes page for ${imdbId}:`, error);
      return null;
    }
  }

  /**
   * Get detailed episode data for each season
   */
  private async getDetailedSeasonData(imdbId: string, seasons: { seasonNumber: number; year?: string }[]): Promise<IMDbSeasonData[]> {
    const detailedSeasons: IMDbSeasonData[] = [];

    for (const season of seasons) {
      try {
        console.log(`🔍 Scraping detailed data for Season ${season.seasonNumber}...`);
        
        const seasonData = await this.getSeasonEpisodes(imdbId, season.seasonNumber);
        if (seasonData) {
          detailedSeasons.push(seasonData);
        }

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`❌ Error scraping Season ${season.seasonNumber}:`, error);
        // Continue with other seasons
      }
    }

    return detailedSeasons.sort((a, b) => a.seasonNumber - b.seasonNumber);
  }

  /**
   * Get episodes directly from the episodes page (for single-season shows)
   */
  private async getEpisodesDirectly(imdbId: string): Promise<IMDbEpisodeData[]> {
    try {
      const url = `${this.baseUrl}/title/${imdbId}/episodes/`;

      const response = await this.makeRequest(url);
      const $ = cheerio.load(response.data);

      const episodes: IMDbEpisodeData[] = [];

      // Use the new IMDb structure - episode cards
      const episodeCards = $('.ipc-list-card--base');

      if (episodeCards.length > 0) {
        console.log(`📍 Found ${episodeCards.length} episode cards using new IMDb structure`);

        episodeCards.each((index, element) => {
          try {
            const $card = $(element);

            // Extract episode title and season/episode info
            // Format: "S1.E1 ∙ Sono shitsuji, yuunou"
            const titleElement = $card.find('.ipc-title__text--reduced, .ipc-title__text');
            const fullTitle = titleElement.text().trim();

            let season = 1;
            let episode = index + 1;
            let title = '';

            // Parse season and episode from title like "S1.E1 ∙ Episode Title"
            const seasonEpisodeMatch = fullTitle.match(/S(\d+)\.E(\d+)\s*∙?\s*(.+)/i);
            if (seasonEpisodeMatch) {
              season = parseInt(seasonEpisodeMatch[1]);
              episode = parseInt(seasonEpisodeMatch[2]);
              title = seasonEpisodeMatch[3].trim();
            } else {
              // Fallback: try to extract just the title
              title = fullTitle || `Episode ${episode}`;
            }

            // Extract description from the content area
            const description = $card.find('.ipc-html-content-inner-div').text().trim();

            // Extract air date (format: "Fri, Oct 3, 2008")
            const airDate = $card.find('.sc-a388aa45-10, .larLSC').text().trim();

            // Extract rating (format: "7.1")
            let rating: number | undefined;
            const ratingText = $card.find('.ipc-rating-star--rating').text().trim();
            if (ratingText && !isNaN(parseFloat(ratingText))) {
              rating = parseFloat(ratingText);
            }

            // Extract individual episode IMDb ID from the link
            let episodeImdbId: string | undefined;
            const episodeLink = $card.find('a[href*="/title/tt"]').attr('href');
            if (episodeLink) {
              const imdbMatch = episodeLink.match(/\/title\/(tt\d+)/);
              if (imdbMatch) {
                episodeImdbId = imdbMatch[1];
              }
            }

            if (title && season && episode) {
              episodes.push({
                season,
                episode,
                title,
                description: description || undefined,
                airDate: airDate || undefined,
                rating: rating,
                imdbId: episodeImdbId,
                duration: undefined
              });
            }

          } catch (error) {
            console.error(`❌ Error parsing episode card:`, error);
          }
        });
      } else {
        console.log(`❌ No episode cards found with .ipc-list-card--base selector`);
      }

      // Sort episodes by episode number
      episodes.sort((a, b) => a.episode - b.episode);

      console.log(`✅ Direct episode scraping: Found ${episodes.length} episodes`);
      return episodes;

    } catch (error) {
      console.error(`❌ Error fetching episodes directly for ${imdbId}:`, error);
      return [];
    }
  }

  /**
   * Get episodes for a specific season
   */
  private async getSeasonEpisodes(imdbId: string, seasonNumber: number): Promise<IMDbSeasonData | null> {
    try {
      const url = `${this.baseUrl}/title/${imdbId}/episodes/?season=${seasonNumber}`;

      const response = await this.makeRequest(url);
      const $ = cheerio.load(response.data);

      const episodes: IMDbEpisodeData[] = [];

      // Use the new IMDb structure - episode cards
      const episodeCards = $('.ipc-list-card--base');

      if (episodeCards.length > 0) {
        console.log(`📍 Found ${episodeCards.length} episodes for Season ${seasonNumber} using new IMDb structure`);

        episodeCards.each((index, element) => {
          try {
            const $card = $(element);

            // Extract episode title and episode info
            const titleElement = $card.find('.ipc-title__text--reduced, .ipc-title__text');
            const fullTitle = titleElement.text().trim();

            let episode = index + 1;
            let title = '';

            // Parse episode from title like "S1.E1 ∙ Episode Title"
            const seasonEpisodeMatch = fullTitle.match(/S\d+\.E(\d+)\s*∙?\s*(.+)/i);
            if (seasonEpisodeMatch) {
              episode = parseInt(seasonEpisodeMatch[1]);
              title = seasonEpisodeMatch[2].trim();
            } else {
              // Fallback: try to extract just the title
              title = fullTitle || `Episode ${episode}`;
            }

            // Extract description
            const description = $card.find('.ipc-html-content-inner-div').text().trim();

            // Extract air date
            const airDate = $card.find('.sc-a388aa45-10, .larLSC').text().trim();

            // Extract rating
            let rating: number | undefined;
            const ratingText = $card.find('.ipc-rating-star--rating').text().trim();
            if (ratingText && !isNaN(parseFloat(ratingText))) {
              rating = parseFloat(ratingText);
            }

            // Extract individual episode IMDb ID
            let episodeImdbId: string | undefined;
            const episodeLink = $card.find('a[href*="/title/tt"]').attr('href');
            if (episodeLink) {
              const imdbMatch = episodeLink.match(/\/title\/(tt\d+)/);
              if (imdbMatch) {
                episodeImdbId = imdbMatch[1];
              }
            }

            if (title) {
              episodes.push({
                season: seasonNumber,
                episode,
                title,
                description: description || undefined,
                airDate: airDate || undefined,
                rating: rating,
                imdbId: episodeImdbId,
                duration: undefined
              });
            }

          } catch (error) {
            console.error(`❌ Error parsing episode card:`, error);
          }
        });
      } else {
        console.log(`❌ No episode cards found for Season ${seasonNumber}`);
      }

      // Sort episodes by episode number
      episodes.sort((a, b) => a.episode - b.episode);

      console.log(`✅ Season ${seasonNumber}: Found ${episodes.length} episodes`);

      return {
        seasonNumber,
        episodeCount: episodes.length,
        episodes,
        year: undefined // Could be extracted from air dates if needed
      };

    } catch (error) {
      console.error(`❌ Error fetching season ${seasonNumber} for ${imdbId}:`, error);
      return null;
    }
  }
}

export default IMDbEpisodeScraper;
