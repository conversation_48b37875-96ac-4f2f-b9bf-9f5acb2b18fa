const { MongoClient } = require('mongodb');

async function checkDuplicateYears() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('streamzen');
    
    console.log('🔍 Checking for duplicate years in database...\n');
    
    // Check Movies collection
    console.log('📽️ MOVIES COLLECTION:');
    const moviesCollection = db.collection('movies');
    
    // Find movies with duplicate years (like 20252025, 20242024)
    const duplicateYearMovies = await moviesCollection.find({
      year: { $regex: /^(\d{4})\1$/ }  // Matches patterns like 20252025, 20242024
    }).limit(10).toArray();
    
    console.log(`Found ${duplicateYearMovies.length} movies with duplicate years (showing first 10):`);
    duplicateYearMovies.forEach(movie => {
      console.log(`   - ${movie.title}: Year = "${movie.year}"`);
    });
    
    // Count total movies with duplicate years
    const totalDuplicateMovies = await moviesCollection.countDocuments({
      year: { $regex: /^(\d{4})\1$/ }
    });
    console.log(`📊 Total movies with duplicate years: ${totalDuplicateMovies}\n`);
    
    // Check Series collection
    console.log('📺 SERIES COLLECTION:');
    const seriesCollection = db.collection('series');
    
    // Find series with duplicate years
    const duplicateYearSeries = await seriesCollection.find({
      year: { $regex: /^(\d{4})\1$/ }
    }).limit(10).toArray();
    
    console.log(`Found ${duplicateYearSeries.length} series with duplicate years (showing first 10):`);
    duplicateYearSeries.forEach(series => {
      console.log(`   - ${series.title}: Year = "${series.year}"`);
    });
    
    // Count total series with duplicate years
    const totalDuplicateSeries = await seriesCollection.countDocuments({
      year: { $regex: /^(\d{4})\1$/ }
    });
    console.log(`📊 Total series with duplicate years: ${totalDuplicateSeries}\n`);
    
    // Check for specific patterns
    console.log('🔍 CHECKING SPECIFIC PATTERNS:');
    
    const patterns = ['20252025', '20242024', '20232023', '20222022', '20212021'];
    
    for (const pattern of patterns) {
      const movieCount = await moviesCollection.countDocuments({ year: pattern });
      const seriesCount = await seriesCollection.countDocuments({ year: pattern });
      
      if (movieCount > 0 || seriesCount > 0) {
        console.log(`   ${pattern}: ${movieCount} movies, ${seriesCount} series`);
      }
    }
    
    // Show some examples of correct vs incorrect years
    console.log('\n📋 YEAR FORMAT EXAMPLES:');
    const sampleMovies = await moviesCollection.find({}).limit(20).toArray();
    const yearFormats = {};
    
    sampleMovies.forEach(movie => {
      const yearType = typeof movie.year;
      const yearValue = movie.year;
      const key = `${yearType}: ${yearValue}`;
      
      if (!yearFormats[key]) {
        yearFormats[key] = { count: 0, example: movie.title };
      }
      yearFormats[key].count++;
    });
    
    Object.entries(yearFormats).forEach(([format, data]) => {
      console.log(`   ${format} (${data.count} items) - Example: ${data.example}`);
    });
    
  } catch (error) {
    console.error('❌ Error checking duplicate years:', error);
  } finally {
    await client.close();
  }
}

checkDuplicateYears();
