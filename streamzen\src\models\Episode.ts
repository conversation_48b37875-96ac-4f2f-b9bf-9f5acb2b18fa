import mongoose, { Schema, Document } from 'mongoose';

export interface IEpisode extends Document {
  imdbId: string; // Series IMDb ID
  tmdbId?: string; // Series TMDB ID
  seriesTitle: string;
  season: number;
  episode: number;
  episodeTitle?: string;
  airDate?: Date;
  runtime?: string;
  imdbRating?: number;
  description?: string;
  embedUrl: string;
  embedUrlTmdb?: string;
  vidsrcUrl?: string; // VidSrc embed URL
  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL
  quality?: string;
  genres?: string[]; // Genres inherited from series
  language?: string; // Language inherited from series
  country?: string; // Country inherited from series
  posterUrl?: string; // Poster inherited from series
  isLatestRelease?: boolean; // Track if episode is from VidSrc latest
  createdAt: Date;
  updatedAt: Date;
}

const EpisodeSchema: Schema = new Schema({
  imdbId: { 
    type: String, 
    required: true,
    index: true 
  },
  tmdbId: { 
    type: String, 
    index: true 
  },
  seriesTitle: { 
    type: String, 
    required: true,
    index: true 
  },
  season: { 
    type: Number, 
    required: true,
    index: true 
  },
  episode: { 
    type: Number, 
    required: true,
    index: true 
  },
  episodeTitle: String,
  airDate: { 
    type: Date,
    index: true 
  },
  runtime: String,
  imdbRating: Number,
  description: String,
  embedUrl: {
    type: String,
    required: true
  },
  embedUrlTmdb: String,
  vidsrcUrl: String, // VidSrc embed URL
  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL
  quality: {
    type: String,
    index: true
  },
  genres: [{
    type: String,
    index: true
  }],
  language: {
    type: String,
    index: true
  },
  country: {
    type: String,
    index: true
  },
  posterUrl: String,
  isLatestRelease: {
    type: Boolean,
    default: false,
    index: true
  }
}, {
  timestamps: true
});

// Compound indexes for better query performance
EpisodeSchema.index({ imdbId: 1, season: 1, episode: 1 }, { unique: true });
EpisodeSchema.index({ airDate: -1 });
EpisodeSchema.index({ seriesTitle: 1, season: 1, episode: 1 });
EpisodeSchema.index({ createdAt: -1 }); // For latest episodes

export default mongoose.models.Episode || mongoose.model<IEpisode>('Episode', EpisodeSchema);
