import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

export async function GET() {
  return await backfillEpisodes();
}

export async function POST() {
  return await backfillEpisodes();
}

async function backfillEpisodes() {
  try {
    await connectDB();
    
    console.log('🔄 Starting Episode Backfill - Adding Language/Country/Poster from Series...');
    const startTime = Date.now();
    
    // Get all episodes that are missing language, country, or poster
    const episodesNeedingUpdate = await Episode.find({
      $or: [
        { language: { $exists: false } },
        { language: null },
        { language: '' },
        { country: { $exists: false } },
        { country: null },
        { country: '' },
        { posterUrl: { $exists: false } },
        { posterUrl: null },
        { posterUrl: '' }
      ]
    });
    
    console.log(`📊 Found ${episodesNeedingUpdate.length} episodes needing backfill`);
    
    if (episodesNeedingUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No episodes need backfilling',
        updated: 0
      });
    }
    
    // Group episodes by IMDb ID for efficient series lookup
    const episodesByImdb = episodesNeedingUpdate.reduce((acc, episode) => {
      if (!acc[episode.imdbId]) {
        acc[episode.imdbId] = [];
      }
      acc[episode.imdbId].push(episode);
      return acc;
    }, {} as Record<string, any[]>);
    
    const imdbIds = Object.keys(episodesByImdb);
    console.log(`🎬 Processing ${imdbIds.length} unique series...`);
    
    let updated = 0;
    let errors = 0;
    let seriesNotFound = 0;
    
    // Process in batches
    const batchSize = 10;
    for (let i = 0; i < imdbIds.length; i += batchSize) {
      const batch = imdbIds.slice(i, i + batchSize);
      console.log(`🔄 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(imdbIds.length/batchSize)}`);
      
      const batchPromises = batch.map(async (imdbId) => {
        try {
          // Get series data
          const series = await Series.findOne({ imdbId });
          
          if (!series) {
            console.log(`⚠️ Series not found for IMDb ID: ${imdbId}`);
            seriesNotFound++;
            return;
          }
          
          // Update all episodes for this series
          const episodesToUpdate = episodesByImdb[imdbId];
          
          for (const episode of episodesToUpdate) {
            try {
              const updateData: any = {};
              
              // Add missing language
              if (!episode.language) {
                updateData.language = series.language;
              }
              
              // Add missing country
              if (!episode.country) {
                updateData.country = series.country;
              }
              
              // Add missing poster
              if (!episode.posterUrl) {
                updateData.posterUrl = series.posterUrl;
              }
              
              // Add missing genres
              if (!episode.genres || episode.genres.length === 0) {
                updateData.genres = series.genres || [];
              }
              
              // Update timestamp
              updateData.updatedAt = new Date();
              
              if (Object.keys(updateData).length > 1) { // More than just updatedAt
                await Episode.findByIdAndUpdate(episode._id, updateData);
                updated++;
                
                if (updated % 50 === 0) {
                  console.log(`✅ Updated ${updated} episodes...`);
                }
              }
              
            } catch (error) {
              console.error(`❌ Error updating episode ${episode._id}:`, error.message);
              errors++;
            }
          }
          
        } catch (error) {
          console.error(`❌ Error processing series ${imdbId}:`, error.message);
          errors++;
        }
      });
      
      await Promise.all(batchPromises);
      
      // Small delay between batches
      if (i + batchSize < imdbIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`✅ Episode Backfill Complete in ${duration}s`);
    console.log(`📊 Results:`);
    console.log(`   Episodes updated: ${updated}`);
    console.log(`   Series not found: ${seriesNotFound}`);
    console.log(`   Errors: ${errors}`);
    
    return NextResponse.json({
      success: true,
      message: `Backfill completed successfully`,
      stats: {
        episodesProcessed: episodesNeedingUpdate.length,
        episodesUpdated: updated,
        seriesNotFound,
        errors,
        duration: `${duration}s`
      }
    });
    
  } catch (error) {
    console.error('❌ Episode Backfill Error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Backfill failed',
        message: error.message
      },
      { status: 500 }
    );
  }
}
