import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Series } from '@/models/Series';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    console.log('🧪 Testing latest episode detection logic...');

    // Find the series from your example (Randall & Hopkirk)
    const testSeries = await Series.findOne({ imdbId: 'tt0167701' });
    
    if (!testSeries) {
      return NextResponse.json({
        success: false,
        message: 'Test series not found'
      });
    }

    console.log(`📺 Found test series: ${testSeries.title}`);
    console.log(`📊 Episodes count: ${testSeries.episodes.length}`);

    // Show current episode states
    console.log('📝 Current episode states:');
    testSeries.episodes.forEach((ep, index) => {
      console.log(`   Episode ${index}: S${ep.season}E${ep.episode} - isLatestRelease: ${ep.isLatestRelease}`);
    });

    // Test the latest episode detection logic
    if (testSeries.episodes.length > 0) {
      // Clear all isLatestRelease flags
      testSeries.episodes.forEach(episode => {
        episode.isLatestRelease = false;
      });

      // Find the actual latest episode (highest season/episode number)
      const latestEpisode = testSeries.episodes.reduce((latest, current) => {
        if (current.season > latest.season) return current;
        if (current.season === latest.season && current.episode > latest.episode) return current;
        return latest;
      });

      console.log(`🎯 Latest episode detected: S${latestEpisode.season}E${latestEpisode.episode}`);

      // Mark it as latest
      const latestEpisodeIndex = testSeries.episodes.findIndex(ep =>
        ep.season === latestEpisode.season && ep.episode === latestEpisode.episode
      );

      if (latestEpisodeIndex >= 0) {
        testSeries.episodes[latestEpisodeIndex].isLatestRelease = true;
        await testSeries.save();
        console.log(`✅ Marked S${latestEpisode.season}E${latestEpisode.episode} as latest release`);
      }

      // Show updated episode states
      console.log('📝 Updated episode states:');
      testSeries.episodes.forEach((ep, index) => {
        console.log(`   Episode ${index}: S${ep.season}E${ep.episode} - isLatestRelease: ${ep.isLatestRelease}`);
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Latest episode logic test completed',
      seriesTitle: testSeries.title,
      episodeCount: testSeries.episodes.length,
      latestEpisode: testSeries.episodes.find(ep => ep.isLatestRelease)
    });

  } catch (error) {
    console.error('❌ Test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Test failed',
        message: error.message
      },
      { status: 500 }
    );
  }
}
