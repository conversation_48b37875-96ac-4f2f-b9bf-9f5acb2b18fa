import { NextRequest, NextResponse } from 'next/server';
import { invalidateHomepageCache } from '../../homepage/route';
import { invalidateMoviesPageCache } from '../../movies/optimized/route';
import { invalidateSeriesPageCache } from '../../series/optimized/route';

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    console.log(`🗑️ Cache invalidation requested for: ${type || 'all'}`);

    // Invalidate homepage cache
    if (!type || type === 'homepage' || type === 'all') {
      invalidateHomepageCache();
    }

    // Invalidate movies page cache
    if (!type || type === 'movies' || type === 'all') {
      invalidateMoviesPageCache();
    }

    // Invalidate series page cache
    if (!type || type === 'series' || type === 'all') {
      invalidateSeriesPageCache();
    }

    return NextResponse.json({ 
      success: true, 
      message: `Cache invalidated for: ${type || 'all'}`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error invalidating cache:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to invalidate cache' },
      { status: 500 }
    );
  }
}
