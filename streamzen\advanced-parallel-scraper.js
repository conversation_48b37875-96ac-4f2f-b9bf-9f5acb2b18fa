const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs').promises;
const mongoose = require('mongoose');
const { Worker } = require('worker_threads');
const os = require('os');

// Import models
require('./src/models/Movie');
require('./src/models/Series');

const Movie = mongoose.model('Movie');
const Series = mongoose.model('Series');

class AdvancedParallelScraper {
  constructor() {
    this.PARALLEL_WORKERS = 100; // 100 parallel processes
    this.BATCH_SIZE = 20; // Process 20 movies per batch (reduced for better stability)
    this.RETRY_ATTEMPTS = 3;
    this.DELAY_BETWEEN_BATCHES = 500; // 0.5 second delay between batches
    this.workers = [];
    this.activeWorkers = 0;
    this.processedCount = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.startTime = Date.now();
    this.results = {
      movies: [],
      series: [],
      errors: []
    };
    this.batchQueue = [];
    this.completedBatches = 0;
  }

  async initialize() {
    console.log('🚀 Initializing Advanced Parallel Scraper...');
    console.log(`💻 System: ${os.cpus().length} CPU cores, ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB RAM`);
    
    // Connect to MongoDB
    if (!mongoose.connection.readyState) {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
      await mongoose.connect(mongoUri);
      console.log('✅ Connected to MongoDB');
    }

    // Create worker pool
    await this.createWorkerPool();
    console.log(`✅ Created worker pool with ${this.PARALLEL_WORKERS} workers`);
  }

  async createWorkerPool() {
    const workerScript = `
      const { parentPort, workerData } = require('worker_threads');
      const axios = require('axios');
      const cheerio = require('cheerio');

      class WorkerScraper {
        constructor() {
          this.requestCount = 0;
          this.userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0'
          ];
        }

        getRandomUserAgent() {
          return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
        }

        async delay(ms) {
          return new Promise(resolve => setTimeout(resolve, ms));
        }

        async fetchPage(imdbId, retries = 3) {
          const url = \`https://www.imdb.com/title/\${imdbId}/\`;
          
          for (let attempt = 1; attempt <= retries; attempt++) {
            try {
              const headers = {
                'User-Agent': this.getRandomUserAgent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
              };

              // Progressive delay with jitter
              const baseDelay = Math.random() * 2000 + 1000; // 1-3 seconds
              const attemptDelay = attempt * 500; // Additional delay for retries
              await this.delay(baseDelay + attemptDelay);

              const response = await axios.get(url, { 
                headers, 
                timeout: 30000,
                maxRedirects: 5
              });
              
              return cheerio.load(response.data);
            } catch (error) {
              if (attempt === retries) {
                throw new Error(\`Failed after \${retries} attempts: \${error.message}\`);
              }
              await this.delay(attempt * 2000); // Exponential backoff
            }
          }
        }

        extractBasicInfo($) {
          const titleElement = $('h1[data-testid="hero__pageTitle"] span[data-testid="hero__primary-text"]');
          const title = titleElement.text().trim();
          
          if (!title) {
            throw new Error('Could not extract title from IMDb page');
          }

          const yearElement = $('ul.ipc-inline-list a[href*="/releaseinfo/"]');
          const yearText = yearElement.text().trim();
          const year = parseInt(yearText) || new Date().getFullYear();

          const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();
          const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');
          
          return { title, year, type: isMovie ? 'movie' : 'series' };
        }

        extractGenres($) {
          const genres = [];
          const genreSelectors = [
            '[data-testid="genres"] .ipc-chip .ipc-chip__text',
            '[data-testid="genres"] .ipc-chip__text',
            'li[data-testid="storyline-genres"] .ipc-metadata-list-item__list-content-item',
            'a[href*="/search/title/?genres="] span',
            'a[href*="genres="] span'
          ];

          for (const selector of genreSelectors) {
            const elements = $(selector);
            if (elements.length > 0) {
              elements.each((_, element) => {
                const genre = $(element).text().trim();
                if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {
                  const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];
                  if (!skipTexts.includes(genre)) {
                    genres.push(genre);
                  }
                }
              });
              if (genres.length > 0) break;
            }
          }
          return genres;
        }

        extractCast($) {
          const cast = [];
          const castSelectors = [
            'section[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
            '.cast_list .primary_photo + td a',
            'div[data-testid="title-cast-item"] a[href*="/name/"]'
          ];

          for (const selector of castSelectors) {
            const elements = $(selector);
            if (elements.length > 0) {
              elements.each((_, element) => {
                const actorName = $(element).text().trim();
                if (actorName && !cast.includes(actorName) && cast.length < 10) {
                  cast.push(actorName);
                }
              });
              break;
            }
          }
          return cast;
        }

        extractIMDbRating($) {
          const ratingElement = $('div[data-testid="hero-rating-bar__aggregate-rating__score"] span');
          const rating = parseFloat(ratingElement.text().trim()) || undefined;
          
          const votesElement = $('div.sc-d541859f-3');
          const votes = votesElement.text().trim() || undefined;
          
          return { rating, votes };
        }

        extractDescription($) {
          const selectors = [
            'span[data-testid="plot-xl"]',
            'span[data-testid="plot-l"]',
            'span[data-testid="plot"]'
          ];

          for (const selector of selectors) {
            const element = $(selector);
            const text = element.text().trim();
            if (text && text.length > 10) return text;
          }
          return undefined;
        }

        extractPosterUrl($) {
          const selectors = [
            'div[data-testid="hero-media__poster"] img',
            '.ipc-image[data-testid="hero-media__poster"]',
            '.poster img'
          ];

          for (const selector of selectors) {
            const element = $(selector);
            const src = element.attr('src');
            if (src && src.includes('media-amazon.com')) {
              return src.replace(/\\._.*?_\\./, '._V1_FMjpg_UX1000_.');
            }
          }
          return undefined;
        }

        extractLanguage($) {
          const languageSelectors = [
            'li[data-testid="title-details-languages"] .ipc-metadata-list-item__list-content-item',
            'div[data-testid="title-details-section"] li:contains("Language") .ipc-metadata-list-item__list-content-item'
          ];

          for (const selector of languageSelectors) {
            const element = $(selector).first();
            const language = element.text().trim();
            if (language) return language;
          }
          return undefined;
        }

        extractCountry($) {
          const countrySelectors = [
            'li[data-testid="title-details-origin"] .ipc-metadata-list-item__list-content-item',
            'div[data-testid="title-details-section"] li:contains("Country") .ipc-metadata-list-item__list-content-item'
          ];

          for (const selector of countrySelectors) {
            const element = $(selector).first();
            const country = element.text().trim();
            if (country) return country;
          }
          return undefined;
        }

        generateVidSrcUrl(imdbId, type) {
          if (type === 'movie') {
            return \`https://vidsrc.me/embed/movie?imdb=\${imdbId}\`;
          } else {
            return \`https://vidsrc.me/embed/tv?imdb=\${imdbId}&season=1&episode=1\`;
          }
        }

        async scrapeContent(imdbId) {
          try {
            const $ = await this.fetchPage(imdbId);
            const basicInfo = this.extractBasicInfo($);
            const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);

            const commonData = {
              imdbId,
              title: basicInfo.title,
              year: basicInfo.year,
              imdbRating,
              imdbVotes,
              description: this.extractDescription($),
              genres: this.extractGenres($),
              cast: this.extractCast($),
              language: this.extractLanguage($),
              country: this.extractCountry($),
              posterUrl: this.extractPosterUrl($),
              embedUrl: this.generateVidSrcUrl(imdbId, basicInfo.type),
              vidsrcUrl: this.generateVidSrcUrl(imdbId, basicInfo.type),
              createdAt: new Date(),
              updatedAt: new Date()
            };

            return {
              success: true,
              type: basicInfo.type,
              data: commonData
            };
          } catch (error) {
            return {
              success: false,
              error: error.message,
              imdbId
            };
          }
        }
      }

      parentPort.on('message', async (message) => {
        if (message.type === 'SCRAPE_BATCH') {
          const scraper = new WorkerScraper();
          const results = [];
          
          for (const imdbId of message.imdbIds) {
            const result = await scraper.scrapeContent(imdbId);
            results.push(result);
          }
          
          parentPort.postMessage({
            type: 'BATCH_COMPLETE',
            workerId: message.workerId,
            batchIndex: message.batchIndex,
            results
          });
        }
      });
    `;

    // Write worker script to temporary file
    const workerPath = path.join(__dirname, 'temp-worker.js');
    await fs.writeFile(workerPath, workerScript);

    // Create workers
    for (let i = 0; i < this.PARALLEL_WORKERS; i++) {
      const worker = new Worker(workerPath);
      worker.workerId = i;
      worker.isActive = false;
      
      worker.on('message', (message) => {
        this.handleWorkerMessage(message);
      });
      
      worker.on('error', (error) => {
        console.error(`❌ Worker ${worker.workerId} error:`, error);
        worker.isActive = false;
        this.activeWorkers--;
      });
      
      this.workers.push(worker);
    }
  }

  async loadExcelFile() {
    console.log('📊 Loading Excel file...');
    
    const excelPath = path.join(__dirname, 'imdb_movie_ids_final.xlsx');
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    
    const imdbIds = data.map(row => row.TitleID).filter(id => id && id.startsWith('tt'));
    
    console.log(`✅ Loaded ${imdbIds.length} IMDb IDs from Excel file`);
    return imdbIds;
  }

  async processBatch(imdbIds, batchIndex) {
    return new Promise((resolve) => {
      const worker = this.workers.find(w => !w.isActive);
      if (!worker) {
        setTimeout(() => {
          this.processBatch(imdbIds, batchIndex).then(resolve);
        }, 100);
        return;
      }

      worker.isActive = true;
      this.activeWorkers++;

      const timeout = setTimeout(() => {
        console.log(`⏰ Batch ${batchIndex} timed out`);
        worker.isActive = false;
        this.activeWorkers--;
        resolve([]);
      }, 120000); // 2 minute timeout

      worker.once('message', (message) => {
        clearTimeout(timeout);
        if (message.type === 'BATCH_COMPLETE') {
          worker.isActive = false;
          this.activeWorkers--;
          resolve(message.results);
        }
      });

      worker.postMessage({
        type: 'SCRAPE_BATCH',
        workerId: worker.workerId,
        batchIndex,
        imdbIds
      });
    });
  }

  handleWorkerMessage(message) {
    if (message.type === 'BATCH_COMPLETE') {
      this.completedBatches++;
      this.processedCount += message.results.length;
      
      message.results.forEach(result => {
        if (result.success) {
          this.successCount++;
          if (result.type === 'movie') {
            this.results.movies.push(result.data);
          } else {
            this.results.series.push(result.data);
          }
        } else {
          this.errorCount++;
          this.results.errors.push(result);
        }
      });

      this.logProgress();
    }
  }

  logProgress() {
    const elapsed = (Date.now() - this.startTime) / 1000;
    const rate = this.processedCount / elapsed;
    const eta = this.totalItems ? (this.totalItems - this.processedCount) / rate : 0;
    
    console.log(`📈 Progress: ${this.processedCount}/${this.totalItems || '?'} | Batches: ${this.completedBatches}/${this.totalBatches || '?'} | ✅ ${this.successCount} | ❌ ${this.errorCount} | Rate: ${rate.toFixed(2)}/s | ETA: ${Math.round(eta/60)}min`);
  }

  async saveToDatabase() {
    console.log('💾 Saving results to database...');
    
    let savedMovies = 0;
    let savedSeries = 0;
    let errors = 0;

    // Save movies in batches
    const movieBatchSize = 100;
    for (let i = 0; i < this.results.movies.length; i += movieBatchSize) {
      const batch = this.results.movies.slice(i, i + movieBatchSize);
      try {
        const operations = batch.map(movieData => ({
          updateOne: {
            filter: { imdbId: movieData.imdbId },
            update: movieData,
            upsert: true
          }
        }));
        
        await Movie.bulkWrite(operations);
        savedMovies += batch.length;
        console.log(`💾 Saved movie batch: ${savedMovies}/${this.results.movies.length}`);
      } catch (error) {
        console.error(`❌ Error saving movie batch:`, error.message);
        errors += batch.length;
      }
    }

    // Save series in batches
    const seriesBatchSize = 100;
    for (let i = 0; i < this.results.series.length; i += seriesBatchSize) {
      const batch = this.results.series.slice(i, i + seriesBatchSize);
      try {
        const operations = batch.map(seriesData => ({
          updateOne: {
            filter: { imdbId: seriesData.imdbId },
            update: seriesData,
            upsert: true
          }
        }));
        
        await Series.bulkWrite(operations);
        savedSeries += batch.length;
        console.log(`💾 Saved series batch: ${savedSeries}/${this.results.series.length}`);
      } catch (error) {
        console.error(`❌ Error saving series batch:`, error.message);
        errors += batch.length;
      }
    }

    console.log(`✅ Saved to database: ${savedMovies} movies, ${savedSeries} series`);
    if (errors > 0) {
      console.log(`❌ Database save errors: ${errors}`);
    }
  }

  async run() {
    try {
      await this.initialize();
      
      const imdbIds = await this.loadExcelFile();
      this.totalItems = imdbIds.length;
      
      console.log(`🚀 Starting parallel scraping of ${imdbIds.length} items with ${this.PARALLEL_WORKERS} workers...`);
      
      // Split into batches
      const batches = [];
      for (let i = 0; i < imdbIds.length; i += this.BATCH_SIZE) {
        batches.push(imdbIds.slice(i, i + this.BATCH_SIZE));
      }
      
      this.totalBatches = batches.length;
      console.log(`📦 Created ${batches.length} batches of ${this.BATCH_SIZE} items each`);
      
      // Process batches with controlled concurrency
      const maxConcurrentBatches = this.PARALLEL_WORKERS;
      const batchPromises = [];
      
      for (let i = 0; i < batches.length; i++) {
        const batchPromise = this.processBatch(batches[i], i + 1);
        batchPromises.push(batchPromise);
        
        // Control concurrency
        if (batchPromises.length >= maxConcurrentBatches) {
          await Promise.race(batchPromises);
          // Remove completed promises
          for (let j = batchPromises.length - 1; j >= 0; j--) {
            if (batchPromises[j].isFulfilled) {
              batchPromises.splice(j, 1);
            }
          }
        }
        
        // Add small delay to prevent overwhelming
        if (i > 0 && i % 50 === 0) {
          await new Promise(resolve => setTimeout(resolve, this.DELAY_BETWEEN_BATCHES));
        }
      }
      
      // Wait for remaining batches
      await Promise.all(batchPromises);
      
      console.log('🎉 All batches completed!');
      
      // Save results to database
      await this.saveToDatabase();
      
      // Generate final report
      this.generateReport();
      
      // Cleanup
      await this.cleanup();
      
    } catch (error) {
      console.error('❌ Scraper failed:', error);
      await this.cleanup();
    }
  }

  generateReport() {
    const totalTime = (Date.now() - this.startTime) / 1000;
    const avgRate = this.processedCount / totalTime;
    
    console.log('\\n🎯 FINAL SCRAPING REPORT');
    console.log('========================');
    console.log(\`📊 Total Processed: \${this.processedCount}\`);
    console.log(\`✅ Successful: \${this.successCount}\`);
    console.log(\`❌ Errors: \${this.errorCount}\`);
    console.log(\`🎬 Movies Found: \${this.results.movies.length}\`);
    console.log(\`📺 Series Found: \${this.results.series.length}\`);
    console.log(\`⏱️  Total Time: \${Math.round(totalTime/60)} minutes\`);
    console.log(\`⚡ Average Rate: \${avgRate.toFixed(2)} items/second\`);
    console.log(\`🔧 Workers Used: \${this.PARALLEL_WORKERS}\`);
    console.log(\`📦 Batches Processed: \${this.completedBatches}\`);
    console.log('========================\\n');
  }

  async cleanup() {
    console.log('🧹 Cleaning up workers...');
    
    for (const worker of this.workers) {
      await worker.terminate();
    }
    
    try {
      await fs.unlink(path.join(__dirname, 'temp-worker.js'));
    } catch (error) {
      // Ignore cleanup errors
    }
    
    console.log('✅ Cleanup completed');
  }
}

async function main() {
  const scraper = new AdvancedParallelScraper();
  await scraper.run();
  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = AdvancedParallelScraper;
