import { NextRequest, NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

export async function GET(request: NextRequest) {
  try {
    const filterOptions = await contentService.getMovieFilterOptions();
    
    return NextResponse.json(filterOptions);
  } catch (error) {
    console.error('Error fetching movie filter options:', error);
    return NextResponse.json(
      { error: 'Failed to fetch filter options' },
      { status: 500 }
    );
  }
}
