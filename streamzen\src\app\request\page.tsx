import RequestForm from '@/components/RequestForm';
import RequestHistory from '@/components/RequestHistory';
import { Sparkles, Zap, Shield, Clock } from 'lucide-react';

export default function RequestPage() {
  return (
    <div className="min-h-screen bg-black">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900/50 via-black to-black" />

        {/* Content */}
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-600 to-red-700 rounded-2xl mb-6">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tight">
              Request Content
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Submit IMDb IDs for movies and TV series you'd like to see added to StreamZen.
              Our advanced system automatically scrapes complete metadata and adds streaming links.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-800/50">
              <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-blue-400" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-2">Instant Processing</h3>
              <p className="text-gray-400 text-sm">
                Advanced IMDb scraping with complete metadata extraction including cast, genres, and episode data.
              </p>
            </div>

            <div className="bg-gray-900/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-800/50">
              <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center mb-4">
                <Shield className="w-6 h-6 text-green-400" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-2">Auto Detection</h3>
              <p className="text-gray-400 text-sm">
                Automatically detects content type and scrapes all seasons and episodes for TV series.
              </p>
            </div>

            <div className="bg-gray-900/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-800/50">
              <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center mb-4">
                <Clock className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-2">Real-time Updates</h3>
              <p className="text-gray-400 text-sm">
                Track processing status in real-time with detailed progress updates and error reporting.
              </p>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Request Form - Takes 2 columns */}
            <div className="xl:col-span-2">
              <RequestForm />
            </div>

            {/* Request History - Takes 1 column */}
            <div className="xl:col-span-1">
              <RequestHistory />
            </div>
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">How It Works</h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Our advanced system makes content requests simple and efficient
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="relative">
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-800/50 h-full">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center text-white font-bold text-lg mb-4">
                1
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Find IMDb IDs</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Visit IMDb.com and locate your desired movies or TV series.
                Copy the IMDb ID from the URL (e.g., tt0111161).
              </p>
            </div>
            {/* Connection Line */}
            <div className="hidden lg:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gradient-to-r from-blue-600 to-transparent transform -translate-y-1/2" />
          </div>

          <div className="relative">
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-800/50 h-full">
              <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center text-white font-bold text-lg mb-4">
                2
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Submit Request</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Paste the IMDb IDs in our form (one per line) and submit.
                Process up to 50 IDs simultaneously.
              </p>
            </div>
            <div className="hidden lg:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gradient-to-r from-green-600 to-transparent transform -translate-y-1/2" />
          </div>

          <div className="relative">
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-800/50 h-full">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl flex items-center justify-center text-white font-bold text-lg mb-4">
                3
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Auto Processing</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Our AI scrapes complete metadata, detects content type, and generates streaming links automatically.
              </p>
            </div>
            <div className="hidden lg:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gradient-to-r from-purple-600 to-transparent transform -translate-y-1/2" />
          </div>

          <div>
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-800/50 h-full">
              <div className="w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-xl flex items-center justify-center text-white font-bold text-lg mb-4">
                4
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Start Streaming</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Content appears in Movies/Series sections with full metadata and ready-to-stream links.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Examples Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl p-8 border border-gray-800/50">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Popular Content Examples</h2>
            <p className="text-gray-400">
              Here are some popular IMDb IDs you can try to get started
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Movies */}
            <div className="bg-gray-800/50 rounded-2xl p-6">
              <div className="flex items-center mb-4">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                <h3 className="text-white font-semibold text-lg">Top Movies</h3>
              </div>
              <div className="space-y-3">
                {[
                  { id: 'tt0111161', title: 'The Shawshank Redemption', year: '1994' },
                  { id: 'tt0068646', title: 'The Godfather', year: '1972' },
                  { id: 'tt0468569', title: 'The Dark Knight', year: '2008' },
                  { id: 'tt0108052', title: 'Schindler\'s List', year: '1993' },
                  { id: 'tt0167260', title: 'The Lord of the Rings: The Return of the King', year: '2003' }
                ].map((movie) => (
                  <div key={movie.id} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors">
                    <div>
                      <div className="text-white font-medium">{movie.title}</div>
                      <div className="text-gray-400 text-sm">{movie.year}</div>
                    </div>
                    <code className="text-blue-400 text-sm bg-gray-800 px-2 py-1 rounded">{movie.id}</code>
                  </div>
                ))}
              </div>
            </div>

            {/* TV Series */}
            <div className="bg-gray-800/50 rounded-2xl p-6">
              <div className="flex items-center mb-4">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                <h3 className="text-white font-semibold text-lg">Top TV Series</h3>
              </div>
              <div className="space-y-3">
                {[
                  { id: 'tt0944947', title: 'Game of Thrones', year: '2011-2019' },
                  { id: 'tt0903747', title: 'Breaking Bad', year: '2008-2013' },
                  { id: 'tt2356777', title: 'True Detective', year: '2014-' },
                  { id: 'tt0141842', title: 'The Sopranos', year: '1999-2007' },
                  { id: 'tt1316554', title: 'Black Butler', year: '2008-' }
                ].map((series) => (
                  <div key={series.id} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors">
                    <div>
                      <div className="text-white font-medium">{series.title}</div>
                      <div className="text-gray-400 text-sm">{series.year}</div>
                    </div>
                    <code className="text-green-400 text-sm bg-gray-800 px-2 py-1 rounded">{series.id}</code>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Pro Tips */}
          <div className="mt-8 p-6 bg-gradient-to-r from-red-900/20 to-red-800/20 border border-red-500/20 rounded-2xl">
            <h3 className="text-white font-semibold text-lg mb-3 flex items-center">
              <Sparkles className="w-5 h-5 mr-2 text-red-400" />
              Pro Tips
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-300">
              <div className="space-y-2">
                <div className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>IMDb IDs always start with "tt" followed by 7+ digits</span>
                </div>
                <div className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Find IMDb IDs in the URL of any IMDb page</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>TV series automatically include all seasons and episodes</span>
                </div>
                <div className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Processing takes 2-5 minutes per item</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
