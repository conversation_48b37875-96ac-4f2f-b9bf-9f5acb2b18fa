'use client';

import React from 'react';
import { motion, useInView } from 'framer-motion';
import { cn } from '@/lib/utils';

interface DirectorSpotlightProps {
  className?: string;
  style?: React.CSSProperties;
}

const DirectorSpotlight: React.FC<DirectorSpotlightProps> = ({
  className,
  style
}) => {
  const sectionRef = React.useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  // Placeholder component - can be enhanced later
  return (
    <motion.section
      ref={sectionRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8 }}
      className={cn("relative", className)}
      style={style}
    >
      {/* This component can be enhanced later with director-specific content */}
    </motion.section>
  );
};

export default DirectorSpotlight;
