'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Pause, 
  RefreshCw, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Globe,
  Film,
  Tv,
  PlayCircle
} from 'lucide-react';

interface SyncResult {
  movies: { success: boolean; count: number; duration: string; error?: string };
  series: { success: boolean; count: number; duration: string; error?: string };
  episodes: { success: boolean; count: number; duration: string; error?: string };
  seriesEpisodes: { success: boolean; seriesProcessed: number; episodesAdded: number; duration: string; error?: string };
}

interface SyncStatus {
  isRunning: boolean;
  lastSyncTime: string | null;
  nextSyncTime: string | null;
  syncType: string;
  lastResult?: {
    success: boolean;
    timestamp: string;
    utcTime: string;
    nextSyncTime: string;
    results: SyncResult;
    totalDuration: string;
  };
}

export default function UniversalSyncAdminPage() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [utcTime, setUtcTime] = useState<string>('');

  // Fetch sync status
  const fetchSyncStatus = async () => {
    try {
      const response = await fetch('/api/sync/universal?action=status');
      const result = await response.json();
      
      if (result.success) {
        setSyncStatus(result.data);
        setUtcTime(result.utcTime);
        setError(null);
      } else {
        setError(result.message || 'Failed to fetch sync status');
      }
    } catch (err) {
      setError('Network error while fetching sync status');
      console.error('Error fetching sync status:', err);
    }
  };

  // Force manual sync
  const forceSync = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/sync/universal', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'force' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        await fetchSyncStatus(); // Refresh status
      } else {
        setError(result.message || 'Sync failed');
      }
    } catch (err) {
      setError('Network error during sync');
      console.error('Error during sync:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh status every 30 seconds
  useEffect(() => {
    fetchSyncStatus();
    
    const interval = setInterval(fetchSyncStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  // Format time display
  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'Never';
    return new Date(timeString).toLocaleString('en-US', {
      timeZone: 'UTC',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    });
  };

  // Calculate time until next sync
  const getTimeUntilNextSync = () => {
    if (!syncStatus?.nextSyncTime) return 'Unknown';
    
    const now = new Date();
    const nextSync = new Date(syncStatus.nextSyncTime);
    const diff = nextSync.getTime() - now.getTime();
    
    if (diff <= 0) return 'Due now';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-8">
      <div className="max-w-6xl mx-auto">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-4">
            <Globe className="w-8 h-8 text-blue-400" />
            <h1 className="text-4xl font-bold text-white">Universal Sync Control</h1>
          </div>
          <p className="text-gray-400">
            Monitor and control the unified syncing system for movies, series, and episodes
          </p>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-6 p-4 bg-red-500/20 border border-red-500/50 rounded-xl text-red-300"
          >
            <div className="flex items-center space-x-2">
              <XCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </motion.div>
        )}

        {/* Current Status Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6 mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3">
              <Clock className="w-6 h-6 text-blue-400" />
              <span>Sync Status</span>
            </h2>
            
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
                syncStatus?.isRunning 
                  ? 'bg-yellow-500/20 border border-yellow-500/50 text-yellow-300'
                  : 'bg-green-500/20 border border-green-500/50 text-green-300'
              }`}>
                {syncStatus?.isRunning ? (
                  <>
                    <Pause className="w-4 h-4 animate-pulse" />
                    <span>Running</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4" />
                    <span>Scheduled</span>
                  </>
                )}
              </div>
              
              <motion.button
                onClick={fetchSyncStatus}
                className="p-2 bg-gray-700/50 hover:bg-gray-600/50 rounded-lg transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <RefreshCw className="w-4 h-4 text-gray-300" />
              </motion.button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="text-gray-400 text-sm">Current UTC Time</div>
              <div className="text-white font-mono">{formatTime(utcTime)}</div>
            </div>
            
            <div className="space-y-2">
              <div className="text-gray-400 text-sm">Last Sync</div>
              <div className="text-white font-mono">{formatTime(syncStatus?.lastSyncTime)}</div>
            </div>
            
            <div className="space-y-2">
              <div className="text-gray-400 text-sm">Next Sync</div>
              <div className="text-white font-mono">
                {formatTime(syncStatus?.nextSyncTime)}
                <div className="text-blue-400 text-xs mt-1">
                  ({getTimeUntilNextSync()})
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Last Sync Results */}
        {syncStatus?.lastResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6 mb-8"
          >
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center space-x-3">
              <CheckCircle className="w-6 h-6 text-green-400" />
              <span>Last Sync Results</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {/* Movies */}
              <div className="bg-gray-700/30 rounded-xl p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Film className="w-5 h-5 text-blue-400" />
                  <span className="font-semibold text-white">Movies</span>
                  {syncStatus.lastResult.results.movies.success ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-400" />
                  )}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="text-gray-300">Count: {syncStatus.lastResult.results.movies.count}</div>
                  <div className="text-gray-300">Duration: {syncStatus.lastResult.results.movies.duration}</div>
                  {syncStatus.lastResult.results.movies.error && (
                    <div className="text-red-400">Error: {syncStatus.lastResult.results.movies.error}</div>
                  )}
                </div>
              </div>

              {/* Series */}
              <div className="bg-gray-700/30 rounded-xl p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Tv className="w-5 h-5 text-purple-400" />
                  <span className="font-semibold text-white">Series</span>
                  {syncStatus.lastResult.results.series.success ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-400" />
                  )}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="text-gray-300">Count: {syncStatus.lastResult.results.series.count}</div>
                  <div className="text-gray-300">Duration: {syncStatus.lastResult.results.series.duration}</div>
                  {syncStatus.lastResult.results.series.error && (
                    <div className="text-red-400">Error: {syncStatus.lastResult.results.series.error}</div>
                  )}
                </div>
              </div>

              {/* Episodes */}
              <div className="bg-gray-700/30 rounded-xl p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <PlayCircle className="w-5 h-5 text-green-400" />
                  <span className="font-semibold text-white">Episodes</span>
                  {syncStatus.lastResult.results.episodes.success ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-400" />
                  )}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="text-gray-300">Count: {syncStatus.lastResult.results.episodes.count}</div>
                  <div className="text-gray-300">Duration: {syncStatus.lastResult.results.episodes.duration}</div>
                  {syncStatus.lastResult.results.episodes.error && (
                    <div className="text-red-400">Error: {syncStatus.lastResult.results.episodes.error}</div>
                  )}
                </div>
              </div>

              {/* Series Episodes */}
              <div className="bg-gray-700/30 rounded-xl p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Tv className="w-5 h-5 text-orange-400" />
                  <span className="font-semibold text-white">Series Episodes</span>
                  <CheckCircle className="w-4 h-4 text-green-400" />
                </div>
                <div className="space-y-1 text-sm">
                  <div className="text-gray-300">Status: On-Demand</div>
                  <div className="text-gray-300">Trigger: Series Page Visits</div>
                  <div className="text-gray-300">Mode: Smart Sync (Only Missing)</div>
                  <div className="text-yellow-400 text-xs mt-2">
                    Episodes are synced when users visit series pages with incomplete episode data
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center text-gray-400 text-sm">
              Total Duration: {syncStatus.lastResult.totalDuration} | 
              Completed: {formatTime(syncStatus.lastResult.timestamp)}
            </div>
          </motion.div>
        )}

        {/* Manual Sync Control */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6"
        >
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center space-x-3">
            <Play className="w-6 h-6 text-red-400" />
            <span>Manual Control</span>
          </h2>

          <div className="text-center">
            <motion.button
              onClick={forceSync}
              disabled={isLoading || syncStatus?.isRunning}
              className={`px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 ${
                isLoading || syncStatus?.isRunning
                  ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 shadow-lg shadow-red-500/30'
              }`}
              whileHover={!isLoading && !syncStatus?.isRunning ? { scale: 1.05 } : {}}
              whileTap={!isLoading && !syncStatus?.isRunning ? { scale: 0.95 } : {}}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <RefreshCw className="w-5 h-5 animate-spin" />
                  <span>Syncing...</span>
                </div>
              ) : syncStatus?.isRunning ? (
                <div className="flex items-center space-x-2">
                  <Pause className="w-5 h-5" />
                  <span>Sync Running</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Play className="w-5 h-5" />
                  <span>Force Sync Now</span>
                </div>
              )}
            </motion.button>
            
            <p className="text-gray-400 text-sm mt-4">
              Manually trigger a complete sync of movies, series, and latest episodes. Series episodes are synced on-demand when users visit series pages.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
