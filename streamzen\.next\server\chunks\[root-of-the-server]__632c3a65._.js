module.exports = {

"[project]/.next-internal/server/app/api/sync/universal/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/lib/universalSyncService.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
;
class UniversalSyncService {
    static instance;
    syncInterval = null;
    isInitialized = false;
    SYNC_TYPE = 'UNIVERSAL_SYNC';
    SYNC_INTERVAL_HOURS = 3;
    constructor(){}
    static getInstance() {
        if (!UniversalSyncService.instance) {
            UniversalSyncService.instance = new UniversalSyncService();
        }
        return UniversalSyncService.instance;
    }
    /**
   * Initialize the universal sync scheduler
   */ async initialize() {
        if (this.isInitialized) {
            console.log('🔄 Universal Sync Service already initialized');
            return;
        }
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            console.log('🚀 Initializing Universal Sync Service...');
            // Check if we need to create initial sync status
            await this.ensureSyncStatus();
            // Start the scheduler
            this.startUniversalScheduler();
            // Check if we need to sync immediately
            await this.checkAndRunSync();
            this.isInitialized = true;
            console.log('✅ Universal Sync Service initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize Universal Sync Service:', error);
            throw error;
        }
    }
    /**
   * Ensure sync status exists in database
   */ async ensureSyncStatus() {
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            const { default: SyncStatus } = await __turbopack_context__.r("[project]/src/models/SyncStatus.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const existing = await SyncStatus.findOne({
                syncType: this.SYNC_TYPE
            });
            if (!existing) {
                const now = new Date();
                const nextSync = this.calculateNextSyncTime(now);
                console.log(`📅 Creating initial Universal Sync status...`);
                console.log(`📅 Next sync scheduled for: ${nextSync.toISOString()}`);
                const newSyncStatus = await SyncStatus.create({
                    syncType: this.SYNC_TYPE,
                    lastSyncTime: new Date(0),
                    nextSyncTime: nextSync,
                    isRunning: false,
                    lastResult: null
                });
                console.log(`✅ Created initial sync status with ID: ${newSyncStatus._id}`);
            } else {
                // Check if existing record needs fixing
                let needsUpdate = false;
                const updateData = {};
                if (!existing.nextSyncTime) {
                    const now = new Date();
                    updateData.nextSyncTime = this.calculateNextSyncTime(now);
                    needsUpdate = true;
                    console.log(`🔧 Fixing missing nextSyncTime`);
                }
                if (!existing.lastSyncTime) {
                    updateData.lastSyncTime = new Date(0);
                    needsUpdate = true;
                    console.log(`🔧 Fixing missing lastSyncTime`);
                }
                if (typeof existing.isRunning !== 'boolean') {
                    updateData.isRunning = false;
                    needsUpdate = true;
                    console.log(`🔧 Fixing isRunning field`);
                }
                if (needsUpdate) {
                    await SyncStatus.updateOne({
                        syncType: this.SYNC_TYPE
                    }, updateData);
                    console.log(`🔧 Fixed Universal Sync status record`);
                }
                const nextSyncTime = updateData.nextSyncTime || existing.nextSyncTime;
                console.log(`📋 Universal Sync status ready. Next sync: ${nextSyncTime?.toISOString() || 'Unknown'}`);
            }
        } catch (error) {
            console.error('❌ Error ensuring sync status:', error);
            throw error;
        }
    }
    /**
   * Calculate next sync time based on UTC 3-hour intervals
   */ calculateNextSyncTime(fromTime) {
        const utcTime = new Date(fromTime.getTime());
        // Get current UTC hour
        const currentHour = utcTime.getUTCHours();
        // Find next 3-hour interval (0, 3, 6, 9, 12, 15, 18, 21)
        const nextSyncHour = Math.ceil((currentHour + 1) / this.SYNC_INTERVAL_HOURS) * this.SYNC_INTERVAL_HOURS;
        // Set to next sync time
        const nextSync = new Date(utcTime);
        nextSync.setUTCHours(nextSyncHour % 24, 0, 0, 0);
        // If we've passed today's last sync, move to tomorrow
        if (nextSyncHour >= 24) {
            nextSync.setUTCDate(nextSync.getUTCDate() + 1);
        }
        return nextSync;
    }
    /**
   * Check current UTC time and determine if sync should run
   */ async checkAndRunSync() {
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            const { default: SyncStatus } = await __turbopack_context__.r("[project]/src/models/SyncStatus.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const syncStatus = await SyncStatus.findOne({
                syncType: this.SYNC_TYPE
            });
            if (!syncStatus) {
                console.log('⚠️ No sync status found, ensuring sync status...');
                await this.ensureSyncStatus();
                return;
            }
            // Check if nextSyncTime is missing or invalid
            if (!syncStatus.nextSyncTime) {
                console.log('⚠️ nextSyncTime is missing, fixing sync status...');
                const now = new Date();
                const nextSync = this.calculateNextSyncTime(now);
                await SyncStatus.updateOne({
                    syncType: this.SYNC_TYPE
                }, {
                    nextSyncTime: nextSync
                });
                console.log(`📅 Fixed nextSyncTime: ${nextSync.toISOString()}`);
                return;
            }
            const now = new Date();
            const utcNow = new Date(now.getTime());
            console.log(`🕐 UTC Time Check: ${utcNow.toISOString()}`);
            console.log(`📅 Next Scheduled Sync: ${syncStatus.nextSyncTime.toISOString()}`);
            // Check if it's time to sync and not already running
            if (utcNow >= syncStatus.nextSyncTime && !syncStatus.isRunning) {
                console.log('⏰ UTC Time reached for universal sync!');
                console.log('🚀 Starting universal sync process...');
                await this.runUniversalSync();
            } else if (syncStatus.isRunning) {
                console.log('🔄 Sync already running, skipping...');
                console.log(`⚠️ Sync has been running since: ${syncStatus.updatedAt?.toISOString() || 'Unknown'}`);
                console.log(`💡 If this is stuck, use: GET /api/sync/universal?action=reset`);
            } else {
                const timeUntilNext = syncStatus.nextSyncTime.getTime() - utcNow.getTime();
                const hoursUntilNext = Math.round(timeUntilNext / (1000 * 60 * 60 * 100)) / 10;
                console.log(`⏳ Next universal sync in ${hoursUntilNext} hours`);
                console.log(`📊 Sync Status: isRunning=${syncStatus.isRunning}, lastSync=${syncStatus.lastSyncTime?.toISOString() || 'Never'}`);
            }
        } catch (error) {
            console.error('❌ Error in universal sync check:', error);
        }
    }
    /**
   * Start the universal scheduler that checks every 30 minutes
   */ startUniversalScheduler() {
        // Check every 30 minutes if it's time to sync
        this.syncInterval = setInterval(async ()=>{
            await this.checkAndRunSync();
        }, 30 * 60 * 1000); // 30 minutes
        console.log('🔄 Universal sync scheduler started (checks every 30 minutes)');
    }
    /**
   * Run the complete universal sync process
   */ async runUniversalSync() {
        const startTime = Date.now();
        const utcTime = new Date();
        console.log('🌍 STARTING UNIVERSAL SYNC PROCESS');
        console.log(`🕐 UTC Time: ${utcTime.toISOString()}`);
        // Mark sync as running
        await this.updateSyncStatus(true);
        const results = {
            success: false,
            timestamp: new Date().toISOString(),
            utcTime: utcTime.toISOString(),
            nextSyncTime: '',
            results: {
                movies: {
                    success: false,
                    count: 0,
                    duration: '0s'
                },
                series: {
                    success: false,
                    count: 0,
                    duration: '0s'
                },
                episodes: {
                    success: false,
                    count: 0,
                    duration: '0s'
                },
                seriesEpisodes: {
                    success: false,
                    seriesProcessed: 0,
                    episodesAdded: 0,
                    duration: '0s'
                }
            },
            totalDuration: '0s'
        };
        try {
            // STEP 1: Sync Movies
            console.log('🎬 STEP 1: Syncing Movies...');
            const step1Start = Date.now();
            results.results.movies = await this.syncMovies();
            const step1Duration = (Date.now() - step1Start) / 1000;
            console.log(`✅ STEP 1 COMPLETED: Movies sync finished in ${step1Duration}s`);
            // STEP 2: Sync Series
            console.log('📺 STEP 2: Syncing Series...');
            const step2Start = Date.now();
            results.results.series = await this.syncSeries();
            const step2Duration = (Date.now() - step2Start) / 1000;
            console.log(`✅ STEP 2 COMPLETED: Series sync finished in ${step2Duration}s`);
            // STEP 3: Sync Episodes
            console.log('🎭 STEP 3: Syncing Episodes...');
            const step3Start = Date.now();
            results.results.episodes = await this.syncEpisodes();
            const step3Duration = (Date.now() - step3Start) / 1000;
            console.log(`✅ STEP 3 COMPLETED: Episodes sync finished in ${step3Duration}s`);
            // STEP 4: Series Episodes Sync REMOVED
            // Episodes are now synced individually when users visit series pages
            console.log('📚 STEP 4: Series Episodes Sync - SKIPPED (handled on series page visits)');
            results.results.seriesEpisodes = {
                success: true,
                seriesProcessed: 0,
                episodesAdded: 0,
                duration: '0s'
            };
            // Calculate next sync time
            const nextSync = this.calculateNextSyncTime(utcTime);
            results.nextSyncTime = nextSync.toISOString();
            // Mark as successful if at least one sync succeeded
            results.success = results.results.movies.success || results.results.series.success || results.results.episodes.success || results.results.seriesEpisodes.success;
            const totalDuration = (Date.now() - startTime) / 1000;
            const totalMinutes = Math.round(totalDuration / 60 * 100) / 100;
            const endTime = new Date().toISOString();
            results.totalDuration = `${totalDuration}s`;
            // Update sync status
            await this.updateSyncStatus(false, results, nextSync);
            // Invalidate all page caches to show new content immediately
            try {
                console.log('🗑️ Invalidating all page caches after sync completion...');
                const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
                const cacheResponse = await fetch(`${baseUrl}/api/cache/invalidate?type=all`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                if (cacheResponse.ok) {
                    console.log('✅ All page caches invalidated successfully');
                } else {
                    console.log('⚠️ Failed to invalidate page caches, but sync completed');
                }
            } catch (cacheError) {
                console.log('⚠️ Cache invalidation failed, but sync completed:', cacheError);
            }
            console.log('🎉 UNIVERSAL SYNC COMPLETED SUCCESSFULLY!');
            console.log('═══════════════════════════════════════════════════════════');
            console.log(`⏰ Start Time: ${utcTime.toISOString()}`);
            console.log(`⏰ End Time: ${endTime}`);
            console.log(`⏱️ TOTAL DURATION: ${totalDuration}s (${totalMinutes} minutes)`);
            console.log('═══════════════════════════════════════════════════════════');
            console.log('📊 STEP-BY-STEP BREAKDOWN:');
            console.log(`   🎬 Step 1 - Movies: ${results.results.movies.duration} (${results.results.movies.count} items)`);
            console.log(`   📺 Step 2 - Series: ${results.results.series.duration} (${results.results.series.count} items)`);
            console.log(`   🎭 Step 3 - Episodes: ${results.results.episodes.duration} (${results.results.episodes.count} items)`);
            console.log(`   📚 Step 4 - Series Episodes: SKIPPED (handled on-demand when users visit series pages)`);
            console.log('═══════════════════════════════════════════════════════════');
            console.log(`📅 Next Sync Scheduled: ${results.nextSyncTime}`);
            console.log('═══════════════════════════════════════════════════════════');
            return results;
        } catch (error) {
            const totalDuration = (Date.now() - startTime) / 1000;
            const totalMinutes = Math.round(totalDuration / 60 * 100) / 100;
            const endTime = new Date().toISOString();
            results.totalDuration = `${totalDuration}s`;
            console.error('❌ UNIVERSAL SYNC FAILED!');
            console.error('═══════════════════════════════════════════════════════════');
            console.error(`⏰ Start Time: ${utcTime.toISOString()}`);
            console.error(`⏰ End Time: ${endTime}`);
            console.error(`⏱️ DURATION BEFORE FAILURE: ${totalDuration}s (${totalMinutes} minutes)`);
            console.error(`❌ Error: ${error.message}`);
            console.error('═══════════════════════════════════════════════════════════');
            // Update sync status with error
            await this.updateSyncStatus(false, results);
            throw error;
        }
    }
    /**
   * Sync movies using the full VidSrc sync
   */ async syncMovies() {
        const startTime = Date.now();
        try {
            console.log('📥 Calling VidSrc movies sync...');
            const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
            const response = await fetch(`${baseUrl}/api/sync?action=force`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            const duration = (Date.now() - startTime) / 1000;
            if (response.ok && result.success) {
                console.log(`✅ Movies sync completed: ${result.data?.movies || 0} movies`);
                return {
                    success: true,
                    count: result.data?.movies || 0,
                    duration: `${duration}s`
                };
            } else {
                throw new Error(result.message || 'Movies sync failed');
            }
        } catch (error) {
            const duration = (Date.now() - startTime) / 1000;
            console.error('❌ Movies sync error:', error);
            return {
                success: false,
                count: 0,
                duration: `${duration}s`,
                error: error.message
            };
        }
    }
    /**
   * Sync series using the full VidSrc sync
   */ async syncSeries() {
        const startTime = Date.now();
        try {
            console.log('📥 Calling VidSrc series sync...');
            const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
            const response = await fetch(`${baseUrl}/api/sync?action=force`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            const duration = (Date.now() - startTime) / 1000;
            if (response.ok && result.success) {
                console.log(`✅ Series sync completed: ${result.data?.series || 0} series`);
                return {
                    success: true,
                    count: result.data?.series || 0,
                    duration: `${duration}s`
                };
            } else {
                throw new Error(result.message || 'Series sync failed');
            }
        } catch (error) {
            const duration = (Date.now() - startTime) / 1000;
            console.error('❌ Series sync error:', error);
            return {
                success: false,
                count: 0,
                duration: `${duration}s`,
                error: error.message
            };
        }
    }
    /**
   * Sync episodes using the dedicated episodes sync
   */ async syncEpisodes() {
        const startTime = Date.now();
        try {
            console.log('📥 Calling VidSrc episodes sync...');
            const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
            const response = await fetch(`${baseUrl}/api/sync/vidsrc-episodes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            const duration = (Date.now() - startTime) / 1000;
            if (response.ok && result.success) {
                console.log(`✅ Episodes sync completed: ${result.stats?.totalEpisodes || 0} episodes`);
                return {
                    success: true,
                    count: result.stats?.totalEpisodes || 0,
                    duration: `${duration}s`
                };
            } else {
                throw new Error(result.message || 'Episodes sync failed');
            }
        } catch (error) {
            const duration = (Date.now() - startTime) / 1000;
            console.error('❌ Episodes sync error:', error);
            return {
                success: false,
                count: 0,
                duration: `${duration}s`,
                error: error.message
            };
        }
    }
    /**
   * Sync ALL episodes for ALL series in database using IMDb data (Comprehensive)
   * This replaces the individual episode syncing that happened on watch page visits
   */ async syncAllSeriesEpisodes() {
        const startTime = Date.now();
        try {
            console.log('📚 COMPREHENSIVE SERIES EPISODES SYNC - IMDb Based');
            console.log('🎯 Goal: Fetch ALL episodes for ALL series from IMDb and save to database');
            console.log('📋 This replaces individual episode syncing from watch pages');
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            const { default: Series } = await __turbopack_context__.r("[project]/src/models/Series.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const { default: Episode } = await __turbopack_context__.r("[project]/src/models/Episode.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const { default: IMDbEpisodeScraper } = await __turbopack_context__.r("[project]/src/lib/imdbEpisodeScraper.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            // Get all series from database (no type filter - Series collection only contains series)
            const allSeries = await Series.find({}, {
                imdbId: 1,
                title: 1,
                tmdbId: 1,
                _id: 1
            }).lean();
            console.log(`📊 Found ${allSeries.length} series in database to process for comprehensive episode sync`);
            if (allSeries.length === 0) {
                const duration = (Date.now() - startTime) / 1000;
                return {
                    success: true,
                    seriesProcessed: 0,
                    episodesAdded: 0,
                    duration: `${duration}s`
                };
            }
            let seriesProcessed = 0;
            let totalEpisodesAdded = 0;
            const batchSize = 10; // Process 10 series at a time to avoid overwhelming IMDb
            const imdbScraper = IMDbEpisodeScraper.getInstance();
            console.log(`🚀 OPTIMIZED PROCESSING: ${batchSize} series at a time with rate limiting for IMDb compatibility`);
            // Process series in large parallel batches
            for(let i = 0; i < allSeries.length; i += batchSize){
                const batch = allSeries.slice(i, i + batchSize);
                const batchNumber = Math.floor(i / batchSize) + 1;
                const totalBatches = Math.ceil(allSeries.length / batchSize);
                console.log(`⚡ Processing PARALLEL batch ${batchNumber}/${totalBatches} (${batch.length} series simultaneously)`);
                const batchStartTime = Date.now();
                const batchPromises = batch.map(async (series, index)=>{
                    try {
                        const seriesStartTime = Date.now();
                        console.log(`📺 [${index + 1}/${batch.length}] Processing: ${series.title} (${series.imdbId})`);
                        // First, get existing episodes to check if series is already complete
                        const existingEpisodes = await Episode.find({
                            imdbId: series.imdbId
                        }).lean();
                        // Create existing episodes map for fast lookup
                        const existingEpisodeMap = new Set();
                        existingEpisodes.forEach((ep)=>{
                            existingEpisodeMap.add(`S${ep.season}E${ep.episode}`);
                        });
                        console.log(`🔍 ${series.title}: Checking ${existingEpisodes.length} existing episodes...`);
                        // Get IMDb episode data to check completeness
                        const imdbEpisodeData = await imdbScraper.getSeriesEpisodes(series.imdbId);
                        if (!imdbEpisodeData) {
                            console.log(`⚠️ No IMDb data: ${series.title}`);
                            return {
                                success: false,
                                episodesAdded: 0,
                                seriesTitle: series.title
                            };
                        }
                        // Check if series is already complete (optimization)
                        const totalImdbEpisodes = imdbEpisodeData.totalEpisodes;
                        const existingCount = existingEpisodes.length;
                        if (existingCount >= totalImdbEpisodes && totalImdbEpisodes > 0) {
                            console.log(`⏭️ ${series.title}: Already complete (${existingCount}/${totalImdbEpisodes} episodes) - SKIPPING`);
                            return {
                                success: true,
                                episodesAdded: 0,
                                seriesTitle: series.title,
                                duration: (Date.now() - seriesStartTime) / 1000
                            };
                        }
                        console.log(`🎯 ${series.title}: ${totalImdbEpisodes} total episodes (${existingCount} existing, ${totalImdbEpisodes - existingCount} missing)`);
                        // Prepare episode operations for parallel processing (ONLY missing episodes)
                        const episodeOperations = [];
                        const newEpisodeKeys = [];
                        // Process ONLY missing episodes from IMDb data (optimization)
                        for (const season of imdbEpisodeData.seasons){
                            for (const episode of season.episodes){
                                const episodeKey = `S${episode.season}E${episode.episode}`;
                                // OPTIMIZATION: Skip if episode already exists
                                if (existingEpisodeMap.has(episodeKey)) {
                                    continue; // Skip existing episodes - no processing needed
                                }
                                // Generate VidSrc embed URLs for missing episodes only
                                const vidsrcEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${episode.season}&episode=${episode.episode}`;
                                const vidsrcTmdbUrl = series.tmdbId ? `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId}&season=${episode.season}&episode=${episode.episode}` : '';
                                // Create episode data with explicit field control (no seriesId)
                                const episodeData = {
                                    imdbId: series.imdbId,
                                    tmdbId: series.tmdbId || undefined,
                                    seriesTitle: series.title,
                                    season: episode.season,
                                    episode: episode.episode,
                                    episodeTitle: episode.title || `Episode ${episode.episode}`,
                                    description: episode.description || undefined,
                                    airDate: episode.airDate ? new Date(episode.airDate) : undefined,
                                    runtime: episode.runtime || '45 min',
                                    embedUrl: vidsrcEmbedUrl,
                                    embedUrlTmdb: vidsrcTmdbUrl || undefined,
                                    vidsrcUrl: vidsrcEmbedUrl,
                                    vidsrcTmdbUrl: vidsrcTmdbUrl || undefined
                                };
                                // Add to parallel operations (only for missing episodes)
                                episodeOperations.push(Episode.findOneAndUpdate({
                                    imdbId: series.imdbId,
                                    season: episode.season,
                                    episode: episode.episode
                                }, episodeData, {
                                    upsert: true,
                                    new: true
                                }));
                                // Track new episodes
                                newEpisodeKeys.push(episodeKey);
                            }
                        }
                        // Execute episode operations in parallel (only for missing episodes)
                        if (episodeOperations.length > 0) {
                            console.log(`📝 Processing ${episodeOperations.length} missing episodes for ${series.title}...`);
                            await Promise.allSettled(episodeOperations);
                        } else {
                            console.log(`⚡ No missing episodes to process for ${series.title}`);
                        }
                        const seriesDuration = (Date.now() - seriesStartTime) / 1000;
                        console.log(`✅ ${series.title}: ${newEpisodeKeys.length} new episodes added in ${seriesDuration}s`);
                        return {
                            success: true,
                            episodesAdded: newEpisodeKeys.length,
                            seriesTitle: series.title,
                            duration: seriesDuration
                        };
                    } catch (error) {
                        console.error(`❌ Error processing series ${series.title}:`, error);
                        return {
                            success: false,
                            episodesAdded: 0,
                            seriesTitle: series.title
                        };
                    }
                });
                // Execute all series in parallel and wait for completion
                const batchResults = await Promise.allSettled(batchPromises);
                const batchDuration = (Date.now() - batchStartTime) / 1000;
                // Count successful series and episodes
                let batchSuccessful = 0;
                let batchEpisodesAdded = 0;
                batchResults.forEach((result, index)=>{
                    if (result.status === 'fulfilled' && result.value.success) {
                        batchSuccessful++;
                        batchEpisodesAdded += result.value.episodesAdded;
                        seriesProcessed++;
                        totalEpisodesAdded += result.value.episodesAdded;
                    } else {
                        const seriesTitle = batch[index]?.title || 'Unknown';
                        console.error(`❌ Failed: ${seriesTitle}`);
                    }
                });
                console.log(`⚡ Batch ${batchNumber} completed: ${batchSuccessful}/${batch.length} series, ${batchEpisodesAdded} episodes in ${batchDuration}s`);
                // Respectful delay between batches to avoid overwhelming IMDb
                if (i + batchSize < allSeries.length) {
                    console.log('⏳ Respectful pause before next batch to avoid rate limiting...');
                    await new Promise((resolve)=>setTimeout(resolve, 3000)); // 3 second delay between batches
                }
            }
            const duration = (Date.now() - startTime) / 1000;
            console.log(`🎉 COMPREHENSIVE EPISODE SYNC COMPLETED!`);
            console.log(`📊 Results: ${seriesProcessed}/${allSeries.length} series processed`);
            console.log(`📺 Episodes: ${totalEpisodesAdded} new episodes added to database`);
            console.log(`⏱️ Duration: ${duration}s`);
            return {
                success: true,
                seriesProcessed,
                episodesAdded: totalEpisodesAdded,
                duration: `${duration}s`
            };
        } catch (error) {
            const duration = (Date.now() - startTime) / 1000;
            console.error('❌ Comprehensive series episodes sync error:', error);
            return {
                success: false,
                seriesProcessed: 0,
                episodesAdded: 0,
                duration: `${duration}s`,
                error: error.message
            };
        }
    }
    /**
   * Update sync status in database
   */ async updateSyncStatus(isRunning, results, nextSyncTime) {
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            const { default: SyncStatus } = await __turbopack_context__.r("[project]/src/models/SyncStatus.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const now = new Date();
            const updateData = {
                isRunning,
                updatedAt: now
            };
            if (!isRunning) {
                updateData.lastSyncTime = now;
                if (results) {
                    updateData.lastResult = results;
                }
                if (nextSyncTime) {
                    updateData.nextSyncTime = nextSyncTime;
                } else {
                    updateData.nextSyncTime = this.calculateNextSyncTime(now);
                }
            }
            await SyncStatus.updateOne({
                syncType: this.SYNC_TYPE
            }, updateData, {
                upsert: true
            });
            console.log(`📊 Updated Universal Sync status: isRunning=${isRunning}`);
        } catch (error) {
            console.error('❌ Error updating sync status:', error);
            throw error;
        }
    }
    /**
   * Get current sync status
   */ async getSyncStatus() {
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            const { default: SyncStatus } = await __turbopack_context__.r("[project]/src/models/SyncStatus.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            return await SyncStatus.findOne({
                syncType: this.SYNC_TYPE
            });
        } catch (error) {
            console.error('❌ Error getting sync status:', error);
            return null;
        }
    }
    /**
   * Force manual sync
   */ async forceSync() {
        console.log('🔧 Manual universal sync requested...');
        return this.runUniversalSync();
    }
    /**
   * Destroy the service
   */ destroy() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        this.isInitialized = false;
        console.log('🛑 Universal Sync Service destroyed');
    }
}
const __TURBOPACK__default__export__ = UniversalSyncService;
}}),
"[project]/src/app/api/sync/universal/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$universalSyncService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/universalSyncService.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
;
;
;
const universalSync = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$universalSyncService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getInstance();
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');
        if (action === 'status') {
            const status = await universalSync.getSyncStatus();
            const utcNow = new Date().toISOString();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                utcTime: utcNow,
                data: status
            });
        }
        if (action === 'force') {
            console.log('🔧 Manual universal sync requested via GET');
            const result = await universalSync.forceSync();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: 'Universal sync completed',
                data: result
            });
        }
        if (action === 'reset') {
            console.log('🔄 Resetting universal sync status...');
            // Reset the running status
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            const { default: SyncStatus } = await __turbopack_context__.r("[project]/src/models/SyncStatus.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            await SyncStatus.updateOne({
                syncType: 'UNIVERSAL_SYNC'
            }, {
                isRunning: false,
                updatedAt: new Date()
            });
            console.log('✅ Universal sync status reset');
            const status = await universalSync.getSyncStatus();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: 'Universal sync status reset successfully',
                data: status
            });
        }
        // Default: return status
        const status = await universalSync.getSyncStatus();
        const utcNow = new Date().toISOString();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            utcTime: utcNow,
            data: status
        });
    } catch (error) {
        console.error('❌ Universal sync GET error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to process request',
            message: error.message,
            utcTime: new Date().toISOString()
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json().catch(()=>({}));
        const { action } = body;
        if (action === 'force' || !action) {
            console.log('🔧 Manual universal sync requested via POST');
            const result = await universalSync.forceSync();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: 'Universal sync completed successfully',
                data: result
            });
        }
        if (action === 'status') {
            const status = await universalSync.getSyncStatus();
            const utcNow = new Date().toISOString();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                utcTime: utcNow,
                data: status
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Invalid action',
            message: 'Supported actions: force, status',
            utcTime: new Date().toISOString()
        }, {
            status: 400
        });
    } catch (error) {
        console.error('❌ Universal sync POST error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Universal sync failed',
            message: error.message,
            utcTime: new Date().toISOString()
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__632c3a65._.js.map