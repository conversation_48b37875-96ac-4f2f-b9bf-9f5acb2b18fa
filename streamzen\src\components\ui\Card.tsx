import React from 'react';
import { cn } from '@/lib/utils';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({ children, className, hover = true }) => {
  return (
    <div
      className={cn(
        'rounded-xl overflow-hidden transition-all duration-300',
        hover && 'hover:scale-105 hover:shadow-2xl cursor-pointer',
        className
      )}
    >
      {children}
    </div>
  );
};

export default Card;
