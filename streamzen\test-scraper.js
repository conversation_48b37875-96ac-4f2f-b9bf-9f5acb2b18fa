// Test script to verify the enhanced IMDb scraper functionality
const IMDbScraper = require('./src/lib/scraper.ts').default;

async function testScraper() {
  console.log('🧪 Testing Enhanced IMDb Scraper...\n');
  
  const scraper = IMDbScraper.getInstance();
  
  // Test movie scraping with a popular movie
  try {
    console.log('🎬 Testing Movie Scraping...');
    console.log('Scraping: The Dark Knight (tt0468569)');
    
    const movieData = await scraper.scrapeMovie('tt0468569');
    
    console.log('\n📊 Movie Data Results:');
    console.log('Title:', movieData.title);
    console.log('Year:', movieData.year);
    console.log('Director:', movieData.director);
    console.log('Genres:', movieData.genres);
    console.log('Cast:', movieData.cast);
    console.log('Language:', movieData.language);
    console.log('Country:', movieData.country);
    console.log('IMDb Rating:', movieData.imdbRating);
    console.log('Description:', movieData.description?.substring(0, 100) + '...');
    
  } catch (error) {
    console.error('❌ Movie scraping failed:', error.message);
  }
  
  // Test series scraping
  try {
    console.log('\n\n📺 Testing Series Scraping...');
    console.log('Scraping: Breaking Bad (tt0903747)');
    
    const seriesData = await scraper.scrapeSeries('tt0903747');
    
    console.log('\n📊 Series Data Results:');
    console.log('Title:', seriesData.title);
    console.log('Start Year:', seriesData.startYear);
    console.log('Creator:', seriesData.creator);
    console.log('Genres:', seriesData.genres);
    console.log('Cast:', seriesData.cast);
    console.log('Language:', seriesData.language);
    console.log('Country:', seriesData.country);
    console.log('IMDb Rating:', seriesData.imdbRating);
    console.log('Description:', seriesData.description?.substring(0, 100) + '...');
    
  } catch (error) {
    console.error('❌ Series scraping failed:', error.message);
  }
  
  console.log('\n✅ Scraper testing completed!');
}

// Run the test
testScraper().catch(console.error);
