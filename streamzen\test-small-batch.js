const XLSX = require('xlsx');
const path = require('path');
const mongoose = require('mongoose');

// Import models
require('./src/models/Movie');
require('./src/models/Series');

const Movie = mongoose.model('Movie');
const Series = mongoose.model('Series');

// Import the existing scraper
const IMDbScraper = require('./src/lib/scraper.ts').default;

class SmallBatchTester {
  constructor() {
    this.scraper = IMDbScraper.getInstance();
    this.results = {
      movies: [],
      series: [],
      errors: []
    };
  }

  async initialize() {
    console.log('🧪 Initializing Small Batch Tester...');
    
    // Connect to MongoDB
    if (!mongoose.connection.readyState) {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
      await mongoose.connect(mongoUri);
      console.log('✅ Connected to MongoDB');
    }
  }

  async loadSampleIds() {
    console.log('📊 Loading sample IMDb IDs from Excel...');
    
    const excelPath = path.join(__dirname, 'imdb_movie_ids_final.xlsx');
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    
    const allImdbIds = data.map(row => row.TitleID).filter(id => id && id.startsWith('tt'));
    
    // Take first 10 for testing
    const sampleIds = allImdbIds.slice(0, 10);
    
    console.log(`✅ Selected ${sampleIds.length} sample IDs for testing:`);
    sampleIds.forEach((id, index) => {
      console.log(`${index + 1}. ${id}`);
    });
    
    return sampleIds;
  }

  generateVidSrcUrl(imdbId, type) {
    if (type === 'movie') {
      return `https://vidsrc.me/embed/movie?imdb=${imdbId}`;
    } else {
      return `https://vidsrc.me/embed/tv?imdb=${imdbId}&season=1&episode=1`;
    }
  }

  async scrapeAndSave(imdbId) {
    try {
      console.log(`🔍 Scraping ${imdbId}...`);
      
      // Try to scrape as movie first
      let data;
      let type;
      
      try {
        data = await this.scraper.scrapeMovie(imdbId);
        type = 'movie';
        console.log(`✅ ${imdbId} identified as MOVIE: "${data.title}" (${data.year})`);
      } catch (movieError) {
        try {
          data = await this.scraper.scrapeSeries(imdbId);
          type = 'series';
          console.log(`✅ ${imdbId} identified as SERIES: "${data.title}" (${data.startYear})`);
        } catch (seriesError) {
          throw new Error(`Failed to scrape as both movie and series: ${movieError.message}`);
        }
      }

      // Add VidSrc URL and database fields
      const enhancedData = {
        ...data,
        imdbId,
        embedUrl: this.generateVidSrcUrl(imdbId, type),
        vidsrcUrl: this.generateVidSrcUrl(imdbId, type),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Save to database
      if (type === 'movie') {
        await Movie.findOneAndUpdate(
          { imdbId },
          enhancedData,
          { upsert: true, new: true }
        );
        this.results.movies.push(enhancedData);
        console.log(`💾 Saved movie to database: ${data.title}`);
      } else {
        await Series.findOneAndUpdate(
          { imdbId },
          enhancedData,
          { upsert: true, new: true }
        );
        this.results.series.push(enhancedData);
        console.log(`💾 Saved series to database: ${data.title}`);
      }

      return { success: true, type, data: enhancedData };

    } catch (error) {
      console.error(`❌ Error scraping ${imdbId}:`, error.message);
      this.results.errors.push({ imdbId, error: error.message });
      return { success: false, imdbId, error: error.message };
    }
  }

  async run() {
    try {
      await this.initialize();
      
      const sampleIds = await this.loadSampleIds();
      
      console.log(`\\n🚀 Starting test scraping of ${sampleIds.length} sample items...\\n`);
      
      const startTime = Date.now();
      
      // Process each ID sequentially for testing
      for (let i = 0; i < sampleIds.length; i++) {
        const imdbId = sampleIds[i];
        console.log(`\\n[${i + 1}/${sampleIds.length}] Processing ${imdbId}...`);
        
        const result = await this.scrapeAndSave(imdbId);
        
        // Add delay between requests
        if (i < sampleIds.length - 1) {
          console.log('⏳ Waiting 3 seconds before next request...');
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
      
      const totalTime = (Date.now() - startTime) / 1000;
      
      // Generate report
      console.log('\\n🎯 TEST SCRAPING REPORT');
      console.log('========================');
      console.log(`📊 Total Processed: ${sampleIds.length}`);
      console.log(`✅ Successful: ${this.results.movies.length + this.results.series.length}`);
      console.log(`❌ Errors: ${this.results.errors.length}`);
      console.log(`🎬 Movies Found: ${this.results.movies.length}`);
      console.log(`📺 Series Found: ${this.results.series.length}`);
      console.log(`⏱️  Total Time: ${Math.round(totalTime)}s`);
      console.log('========================\\n');
      
      if (this.results.movies.length > 0) {
        console.log('🎬 Sample Movie Data:');
        const movie = this.results.movies[0];
        console.log(`Title: ${movie.title}`);
        console.log(`Year: ${movie.year}`);
        console.log(`Genres: ${movie.genres?.join(', ') || 'N/A'}`);
        console.log(`Cast: ${movie.cast?.slice(0, 3).join(', ') || 'N/A'}`);
        console.log(`VidSrc URL: ${movie.vidsrcUrl}`);
        console.log(`IMDb Rating: ${movie.imdbRating || 'N/A'}`);
      }
      
      if (this.results.series.length > 0) {
        console.log('\\n📺 Sample Series Data:');
        const series = this.results.series[0];
        console.log(`Title: ${series.title}`);
        console.log(`Year: ${series.startYear}`);
        console.log(`Genres: ${series.genres?.join(', ') || 'N/A'}`);
        console.log(`Cast: ${series.cast?.slice(0, 3).join(', ') || 'N/A'}`);
        console.log(`VidSrc URL: ${series.vidsrcUrl}`);
        console.log(`IMDb Rating: ${series.imdbRating || 'N/A'}`);
      }
      
      if (this.results.errors.length > 0) {
        console.log('\\n❌ Errors:');
        this.results.errors.forEach(error => {
          console.log(`${error.imdbId}: ${error.error}`);
        });
      }
      
      console.log('\\n✅ Test completed successfully!');
      console.log('🚀 Ready to run full parallel scraper on all 18,950 items!');
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      await mongoose.disconnect();
    }
  }
}

// Run the test
async function main() {
  const tester = new SmallBatchTester();
  await tester.run();
  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SmallBatchTester;
