'use client';

import React from 'react';
import Image from 'next/image';
import { Star, Calendar, Clock, User, Users, Play, Heart, Share2, Bookmark, Award, Globe, Sparkles, TrendingUp } from 'lucide-react';
import { motion } from 'framer-motion';
import { getImageUrl, formatRating, cn } from '@/lib/utils';

interface ContentInfoProps {
  content: {
    title: string;
    year?: number;
    rating?: string;
    runtime?: string;
    imdbRating?: number;
    description?: string;
    genres?: string[];
    director?: string;
    cast?: string[];
    language?: string;
    country?: string;
    posterUrl?: string;
    imdbVotes?: string;
    popularity?: number;
  };
}

const ContentInfo: React.FC<ContentInfoProps> = ({ content }) => {
  return (
    <div className="bg-gradient-to-br from-gray-900/50 via-black/50 to-gray-900/50 backdrop-blur-xl rounded-3xl overflow-hidden border border-white/10 shadow-2xl">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="grid grid-cols-1 lg:grid-cols-4 gap-8 p-8"
      >
        {/* Enhanced Poster */}
        <motion.div
          className="lg:col-span-1"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="relative aspect-[2/3] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900 border border-white/20 shadow-2xl group">
            <Image
              src={getImageUrl(content.posterUrl)}
              alt={content.title}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            />
            
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Play button overlay */}
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center shadow-2xl">
                <Play className="w-6 h-6 text-white fill-current ml-1" />
              </div>
            </div>

            {/* Rating badge */}
            {content.imdbRating && (
              <div className="absolute top-4 right-4 flex items-center space-x-1 px-3 py-1.5 bg-black/70 backdrop-blur-sm rounded-full">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-white font-bold text-sm">{formatRating(content.imdbRating)}</span>
              </div>
            )}
          </div>
        </motion.div>

        {/* Content Details */}
        <motion.div
          className="lg:col-span-3 space-y-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {/* Title and Year */}
          <div className="space-y-2">
            <h1 className="text-4xl lg:text-5xl font-black text-white leading-tight">
              {content.title}
            </h1>
            {content.year && (
              <p className="text-xl text-gray-300 font-medium">{content.year}</p>
            )}
          </div>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-4 text-gray-300">
            {content.rating && (
              <span className="px-3 py-1 bg-white/10 rounded-full text-sm font-semibold">
                {content.rating}
              </span>
            )}
            {content.runtime && (
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span className="text-sm font-medium">{content.runtime}</span>
              </div>
            )}
            {content.language && (
              <div className="flex items-center space-x-1">
                <Globe className="w-4 h-4" />
                <span className="text-sm font-medium">{content.language}</span>
              </div>
            )}
          </div>

          {/* Genres */}
          {content.genres && content.genres.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {content.genres.slice(0, 5).map((genre, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-sm font-medium text-blue-300"
                >
                  {genre}
                </span>
              ))}
            </div>
          )}

          {/* Description */}
          {content.description && (
            <div className="space-y-2">
              <h3 className="text-lg font-bold text-white">Overview</h3>
              <p className="text-gray-300 leading-relaxed text-base">
                {content.description}
              </p>
            </div>
          )}

          {/* Cast and Crew */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {content.director && (
              <div className="space-y-2">
                <h4 className="text-sm font-bold text-white uppercase tracking-wider">Director</h4>
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-300 font-medium">{content.director}</span>
                </div>
              </div>
            )}

            {content.cast && content.cast.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-bold text-white uppercase tracking-wider">Cast</h4>
                <div className="flex items-start space-x-2">
                  <Users className="w-4 h-4 text-gray-400 mt-0.5" />
                  <div className="flex flex-wrap gap-1">
                    {content.cast.slice(0, 4).map((actor, index) => (
                      <span key={index} className="text-gray-300 font-medium">
                        {actor}{index < Math.min(content.cast!.length - 1, 3) ? ',' : ''}
                      </span>
                    ))}
                    {content.cast.length > 4 && (
                      <span className="text-gray-400">+{content.cast.length - 4} more</span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Additional Stats */}
          <div className="flex flex-wrap items-center gap-6 pt-4 border-t border-white/10">
            {content.imdbRating && (
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1 px-3 py-1.5 bg-yellow-500/20 rounded-full">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-yellow-400 font-bold">{formatRating(content.imdbRating)}</span>
                </div>
                {content.imdbVotes && (
                  <span className="text-gray-400 text-sm">({content.imdbVotes} votes)</span>
                )}
              </div>
            )}

            {content.popularity && (
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span className="text-green-400 font-medium text-sm">
                  {Math.round(content.popularity)} popularity
                </span>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap items-center gap-4 pt-4">
            <button className="flex items-center space-x-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white font-semibold rounded-xl transition-colors duration-200">
              <Play className="w-4 h-4 fill-current" />
              <span>Watch Now</span>
            </button>
            
            <button className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl transition-colors duration-200">
              <Bookmark className="w-4 h-4" />
              <span>Watchlist</span>
            </button>
            
            <button className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl transition-colors duration-200">
              <Heart className="w-4 h-4" />
              <span>Like</span>
            </button>
            
            <button className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl transition-colors duration-200">
              <Share2 className="w-4 h-4" />
              <span>Share</span>
            </button>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ContentInfo;
