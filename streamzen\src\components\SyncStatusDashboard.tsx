'use client';

import React, { useState, useEffect } from 'react';
import { RefreshC<PERSON>, Clock, CheckCircle, AlertCircle, Play, Database } from 'lucide-react';

interface SyncStatus {
  lastSyncTime?: string;
  nextSyncTime: string;
  isRunning: boolean;
  lastSyncResults: {
    movies: number;
    series: number;
    episodes: number;
  };
  createdAt: string;
  updatedAt: string;
}

const SyncStatusDashboard: React.FC = () => {
  const [status, setStatus] = useState<SyncStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/sync?action=status');
      const data = await response.json();
      if (data.success) {
        setStatus(data.data);
      }
    } catch (error) {
      console.error('Error fetching sync status:', error);
    } finally {
      setLoading(false);
    }
  };

  const forceSync = async () => {
    setSyncing(true);
    try {
      const response = await fetch('/api/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'force' }),
      });
      
      const data = await response.json();
      if (data.success) {
        await fetchStatus(); // Refresh status
        alert(`Sync completed! ${data.data.movies} movies, ${data.data.series} series, ${data.data.episodes} episodes`);
      } else {
        alert('Sync failed: ' + data.error);
      }
    } catch (error) {
      console.error('Error forcing sync:', error);
      alert('Sync failed: ' + error);
    } finally {
      setSyncing(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    const interval = setInterval(fetchStatus, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTimeUntilNext = () => {
    if (!status?.nextSyncTime) return 'Unknown';
    const now = new Date();
    const next = new Date(status.nextSyncTime);
    const diff = next.getTime() - now.getTime();
    
    if (diff <= 0) return 'Overdue';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6">
        <div className="flex items-center justify-center">
          <RefreshCw className="animate-spin mr-2" size={20} />
          <span className="text-white">Loading sync status...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-white font-semibold text-xl flex items-center">
          <Database className="mr-2" size={24} />
          VidSrc Auto-Sync Status
        </h2>
        <button
          onClick={fetchStatus}
          disabled={loading}
          className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
        >
          <RefreshCw className={loading ? 'animate-spin' : ''} size={16} />
        </button>
      </div>

      {status ? (
        <div className="space-y-4">
          {/* Sync Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-800/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                {status.isRunning ? (
                  <RefreshCw className="animate-spin text-blue-400 mr-2" size={16} />
                ) : (
                  <CheckCircle className="text-green-400 mr-2" size={16} />
                )}
                <span className="text-white font-medium">Status</span>
              </div>
              <p className={`text-sm ${status.isRunning ? 'text-blue-400' : 'text-green-400'}`}>
                {status.isRunning ? 'Syncing...' : 'Ready'}
              </p>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Clock className="text-gray-400 mr-2" size={16} />
                <span className="text-white font-medium">Next Sync</span>
              </div>
              <p className="text-sm text-gray-300">{getTimeUntilNext()}</p>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <CheckCircle className="text-gray-400 mr-2" size={16} />
                <span className="text-white font-medium">Last Sync</span>
              </div>
              <p className="text-sm text-gray-300">
                {status.lastSyncTime ? formatDate(status.lastSyncTime) : 'Never'}
              </p>
            </div>
          </div>

          {/* Last Sync Results */}
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h3 className="text-white font-medium mb-3">Last Sync Results</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {status.lastSyncResults.movies}
                </div>
                <div className="text-sm text-gray-400">Movies</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">
                  {status.lastSyncResults.series}
                </div>
                <div className="text-sm text-gray-400">Series</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {status.lastSyncResults.episodes}
                </div>
                <div className="text-sm text-gray-400">Episodes</div>
              </div>
            </div>
          </div>

          {/* Manual Sync Button */}
          <div className="flex justify-center">
            <button
              onClick={forceSync}
              disabled={syncing || status.isRunning}
              className="flex items-center space-x-2 px-6 py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white font-medium transition-colors"
            >
              {syncing ? (
                <RefreshCw className="animate-spin" size={20} />
              ) : (
                <Play size={20} />
              )}
              <span>{syncing ? 'Syncing...' : 'Force Sync Now'}</span>
            </button>
          </div>

          {/* Sync Schedule Info */}
          <div className="bg-blue-900/20 border border-blue-500/20 rounded-lg p-4">
            <h3 className="text-blue-400 font-medium mb-2">Automatic Sync Schedule</h3>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Syncs every 12 hours using universal time</li>
              <li>• Fetches pages 1-15 from VidSrc APIs</li>
              <li>• Movies: https://vidsrc.xyz/movies/latest/</li>
              <li>• Series: https://vidsrc.xyz/tvshows/latest/</li>
              <li>• Episodes: https://vidsrc.xyz/episodes/latest/</li>
              <li>• Auto-creates missing series for new episodes</li>
              <li>• Continues even after server restarts</li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <AlertCircle className="mx-auto text-red-400 mb-2" size={48} />
          <p className="text-white">No sync status available</p>
          <p className="text-gray-400 text-sm">The sync service may not be initialized</p>
        </div>
      )}
    </div>
  );
};

export default SyncStatusDashboard;
