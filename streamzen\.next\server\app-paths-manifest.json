{"/_not-found/page": "app/_not-found/page.js", "/admin/universal-sync/page": "app/admin/universal-sync/page.js", "/api/admin/init-universal-sync/route": "app/api/admin/init-universal-sync/route.js", "/api/debug/database-state/route": "app/api/debug/database-state/route.js", "/api/debug/episodes-check/route": "app/api/debug/episodes-check/route.js", "/api/debug/force-latest-flags/route": "app/api/debug/force-latest-flags/route.js", "/api/debug/migrate-episodes/route": "app/api/debug/migrate-episodes/route.js", "/api/debug/set-test-flags/route": "app/api/debug/set-test-flags/route.js", "/api/debug/test-episodes-query/route": "app/api/debug/test-episodes-query/route.js", "/api/debug/test-latest-episode-logic/route": "app/api/debug/test-latest-episode-logic/route.js", "/api/debug/vidsrc-check/route": "app/api/debug/vidsrc-check/route.js", "/api/debug/vidsrc-sync-debug/route": "app/api/debug/vidsrc-sync-debug/route.js", "/api/episodes/filters/route": "app/api/episodes/filters/route.js", "/api/episodes/route": "app/api/episodes/route.js", "/api/movies/[id]/route": "app/api/movies/[id]/route.js", "/api/movies/route": "app/api/movies/route.js", "/api/series/[id]/episodes/sync-embedded/route": "app/api/series/[id]/episodes/sync-embedded/route.js", "/api/series/[id]/route": "app/api/series/[id]/route.js", "/api/series/filters/route": "app/api/series/filters/route.js", "/api/series/route": "app/api/series/route.js", "/api/sync/route": "app/api/sync/route.js", "/api/sync/universal/route": "app/api/sync/universal/route.js", "/api/sync/vidsrc-episodes/route": "app/api/sync/vidsrc-episodes/route.js", "/episodes/page": "app/episodes/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js", "/series/page": "app/series/page.js", "/watch/movie/[id]/page": "app/watch/movie/[id]/page.js", "/watch/series/[id]/page": "app/watch/series/[id]/page.js"}