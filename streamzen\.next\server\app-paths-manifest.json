{"/api/admin/init-universal-sync/route": "app/api/admin/init-universal-sync/route.js", "/api/cache/invalidate/route": "app/api/cache/invalidate/route.js", "/api/episodes/filters/route": "app/api/episodes/filters/route.js", "/api/sync/universal/route": "app/api/sync/universal/route.js", "/episodes/page": "app/episodes/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/movies/page": "app/movies/page.js", "/page": "app/page.js", "/series/page": "app/series/page.js"}