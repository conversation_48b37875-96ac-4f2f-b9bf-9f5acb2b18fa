{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/universalSyncService.ts"], "sourcesContent": ["import connectDB from './mongodb';\nimport SyncStatus from '@/models/SyncStatus';\n\ninterface UniversalSyncResult {\n  success: boolean;\n  timestamp: string;\n  utcTime: string;\n  nextSyncTime: string;\n  results: {\n    movies: {\n      success: boolean;\n      count: number;\n      duration: string;\n      error?: string;\n    };\n    series: {\n      success: boolean;\n      count: number;\n      duration: string;\n      error?: string;\n    };\n    episodes: {\n      success: boolean;\n      count: number;\n      duration: string;\n      error?: string;\n    };\n    seriesEpisodes: {\n      success: boolean;\n      seriesProcessed: number;\n      episodesAdded: number;\n      duration: string;\n      error?: string;\n    };\n  };\n  totalDuration: string;\n}\n\nclass UniversalSyncService {\n  private static instance: UniversalSyncService;\n  private syncInterval: NodeJS.Timeout | null = null;\n  private isInitialized = false;\n  private readonly SYNC_TYPE = 'UNIVERSAL_SYNC';\n  private readonly SYNC_INTERVAL_HOURS = 3;\n\n  private constructor() {}\n\n  public static getInstance(): UniversalSyncService {\n    if (!UniversalSyncService.instance) {\n      UniversalSyncService.instance = new UniversalSyncService();\n    }\n    return UniversalSyncService.instance;\n  }\n\n  /**\n   * Initialize the universal sync scheduler\n   */\n  public async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('🔄 Universal Sync Service already initialized');\n      return;\n    }\n\n    try {\n      await connectDB();\n      console.log('🚀 Initializing Universal Sync Service...');\n\n      // Check if we need to create initial sync status\n      await this.ensureSyncStatus();\n\n      // Start the scheduler\n      this.startUniversalScheduler();\n\n      // Check if we need to sync immediately\n      await this.checkAndRunSync();\n\n      this.isInitialized = true;\n      console.log('✅ Universal Sync Service initialized successfully');\n\n    } catch (error) {\n      console.error('❌ Failed to initialize Universal Sync Service:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Ensure sync status exists in database\n   */\n  private async ensureSyncStatus(): Promise<void> {\n    try {\n      await connectDB();\n      const { default: SyncStatus } = await import('@/models/SyncStatus');\n\n      const existing = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });\n\n      if (!existing) {\n        const now = new Date();\n        const nextSync = this.calculateNextSyncTime(now);\n\n        console.log(`📅 Creating initial Universal Sync status...`);\n        console.log(`📅 Next sync scheduled for: ${nextSync.toISOString()}`);\n\n        const newSyncStatus = await SyncStatus.create({\n          syncType: this.SYNC_TYPE,\n          lastSyncTime: new Date(0), // Never synced (epoch time)\n          nextSyncTime: nextSync,\n          isRunning: false,\n          lastResult: null\n        });\n\n        console.log(`✅ Created initial sync status with ID: ${newSyncStatus._id}`);\n      } else {\n        // Check if existing record needs fixing\n        let needsUpdate = false;\n        const updateData: any = {};\n\n        if (!existing.nextSyncTime) {\n          const now = new Date();\n          updateData.nextSyncTime = this.calculateNextSyncTime(now);\n          needsUpdate = true;\n          console.log(`🔧 Fixing missing nextSyncTime`);\n        }\n\n        if (!existing.lastSyncTime) {\n          updateData.lastSyncTime = new Date(0);\n          needsUpdate = true;\n          console.log(`🔧 Fixing missing lastSyncTime`);\n        }\n\n        if (typeof existing.isRunning !== 'boolean') {\n          updateData.isRunning = false;\n          needsUpdate = true;\n          console.log(`🔧 Fixing isRunning field`);\n        }\n\n        if (needsUpdate) {\n          await SyncStatus.updateOne({ syncType: this.SYNC_TYPE }, updateData);\n          console.log(`🔧 Fixed Universal Sync status record`);\n        }\n\n        const nextSyncTime = updateData.nextSyncTime || existing.nextSyncTime;\n        console.log(`📋 Universal Sync status ready. Next sync: ${nextSyncTime?.toISOString() || 'Unknown'}`);\n      }\n    } catch (error) {\n      console.error('❌ Error ensuring sync status:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate next sync time based on UTC 3-hour intervals\n   */\n  private calculateNextSyncTime(fromTime: Date): Date {\n    const utcTime = new Date(fromTime.getTime());\n    \n    // Get current UTC hour\n    const currentHour = utcTime.getUTCHours();\n    \n    // Find next 3-hour interval (0, 3, 6, 9, 12, 15, 18, 21)\n    const nextSyncHour = Math.ceil((currentHour + 1) / this.SYNC_INTERVAL_HOURS) * this.SYNC_INTERVAL_HOURS;\n    \n    // Set to next sync time\n    const nextSync = new Date(utcTime);\n    nextSync.setUTCHours(nextSyncHour % 24, 0, 0, 0);\n    \n    // If we've passed today's last sync, move to tomorrow\n    if (nextSyncHour >= 24) {\n      nextSync.setUTCDate(nextSync.getUTCDate() + 1);\n    }\n    \n    return nextSync;\n  }\n\n  /**\n   * Check current UTC time and determine if sync should run\n   */\n  private async checkAndRunSync(): Promise<void> {\n    try {\n      await connectDB();\n      const { default: SyncStatus } = await import('@/models/SyncStatus');\n\n      const syncStatus = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });\n      if (!syncStatus) {\n        console.log('⚠️ No sync status found, ensuring sync status...');\n        await this.ensureSyncStatus();\n        return;\n      }\n\n      // Check if nextSyncTime is missing or invalid\n      if (!syncStatus.nextSyncTime) {\n        console.log('⚠️ nextSyncTime is missing, fixing sync status...');\n        const now = new Date();\n        const nextSync = this.calculateNextSyncTime(now);\n\n        await SyncStatus.updateOne(\n          { syncType: this.SYNC_TYPE },\n          { nextSyncTime: nextSync }\n        );\n\n        console.log(`📅 Fixed nextSyncTime: ${nextSync.toISOString()}`);\n        return;\n      }\n\n      const now = new Date();\n      const utcNow = new Date(now.getTime());\n\n      console.log(`🕐 UTC Time Check: ${utcNow.toISOString()}`);\n      console.log(`📅 Next Scheduled Sync: ${syncStatus.nextSyncTime.toISOString()}`);\n\n      // Check if it's time to sync and not already running\n      if (utcNow >= syncStatus.nextSyncTime && !syncStatus.isRunning) {\n        console.log('⏰ UTC Time reached for universal sync!');\n        console.log('🚀 Starting universal sync process...');\n        await this.runUniversalSync();\n      } else if (syncStatus.isRunning) {\n        console.log('🔄 Sync already running, skipping...');\n        console.log(`⚠️ Sync has been running since: ${syncStatus.updatedAt?.toISOString() || 'Unknown'}`);\n        console.log(`💡 If this is stuck, use: GET /api/sync/universal?action=reset`);\n      } else {\n        const timeUntilNext = syncStatus.nextSyncTime.getTime() - utcNow.getTime();\n        const hoursUntilNext = Math.round(timeUntilNext / (1000 * 60 * 60 * 100)) / 10;\n        console.log(`⏳ Next universal sync in ${hoursUntilNext} hours`);\n        console.log(`📊 Sync Status: isRunning=${syncStatus.isRunning}, lastSync=${syncStatus.lastSyncTime?.toISOString() || 'Never'}`);\n      }\n\n    } catch (error) {\n      console.error('❌ Error in universal sync check:', error);\n    }\n  }\n\n  /**\n   * Start the universal scheduler that checks every 30 minutes\n   */\n  private startUniversalScheduler(): void {\n    // Check every 30 minutes if it's time to sync\n    this.syncInterval = setInterval(async () => {\n      await this.checkAndRunSync();\n    }, 30 * 60 * 1000); // 30 minutes\n\n    console.log('🔄 Universal sync scheduler started (checks every 30 minutes)');\n  }\n\n  /**\n   * Run the complete universal sync process\n   */\n  public async runUniversalSync(): Promise<UniversalSyncResult> {\n    const startTime = Date.now();\n    const utcTime = new Date();\n    \n    console.log('🌍 STARTING UNIVERSAL SYNC PROCESS');\n    console.log(`🕐 UTC Time: ${utcTime.toISOString()}`);\n    \n    // Mark sync as running\n    await this.updateSyncStatus(true);\n    \n    const results: UniversalSyncResult = {\n      success: false,\n      timestamp: new Date().toISOString(),\n      utcTime: utcTime.toISOString(),\n      nextSyncTime: '',\n      results: {\n        movies: { success: false, count: 0, duration: '0s' },\n        series: { success: false, count: 0, duration: '0s' },\n        episodes: { success: false, count: 0, duration: '0s' },\n        seriesEpisodes: { success: false, seriesProcessed: 0, episodesAdded: 0, duration: '0s' }\n      },\n      totalDuration: '0s'\n    };\n\n    try {\n      // STEP 1: Sync Movies\n      console.log('🎬 STEP 1: Syncing Movies...');\n      const step1Start = Date.now();\n      results.results.movies = await this.syncMovies();\n      const step1Duration = (Date.now() - step1Start) / 1000;\n      console.log(`✅ STEP 1 COMPLETED: Movies sync finished in ${step1Duration}s`);\n\n      // STEP 2: Sync Series\n      console.log('📺 STEP 2: Syncing Series...');\n      const step2Start = Date.now();\n      results.results.series = await this.syncSeries();\n      const step2Duration = (Date.now() - step2Start) / 1000;\n      console.log(`✅ STEP 2 COMPLETED: Series sync finished in ${step2Duration}s`);\n\n      // STEP 3: Sync Episodes\n      console.log('🎭 STEP 3: Syncing Episodes...');\n      const step3Start = Date.now();\n      results.results.episodes = await this.syncEpisodes();\n      const step3Duration = (Date.now() - step3Start) / 1000;\n      console.log(`✅ STEP 3 COMPLETED: Episodes sync finished in ${step3Duration}s`);\n\n      // STEP 4: Series Episodes Sync REMOVED\n      // Episodes are now synced individually when users visit series pages\n      console.log('📚 STEP 4: Series Episodes Sync - SKIPPED (handled on series page visits)');\n      results.results.seriesEpisodes = {\n        success: true,\n        seriesProcessed: 0,\n        episodesAdded: 0,\n        duration: '0s'\n      };\n\n      // Calculate next sync time\n      const nextSync = this.calculateNextSyncTime(utcTime);\n      results.nextSyncTime = nextSync.toISOString();\n      \n      // Mark as successful if at least one sync succeeded\n      results.success = results.results.movies.success ||\n                       results.results.series.success ||\n                       results.results.episodes.success ||\n                       results.results.seriesEpisodes.success;\n      \n      const totalDuration = (Date.now() - startTime) / 1000;\n      const totalMinutes = Math.round(totalDuration / 60 * 100) / 100;\n      const endTime = new Date().toISOString();\n      results.totalDuration = `${totalDuration}s`;\n\n      // Update sync status\n      await this.updateSyncStatus(false, results, nextSync);\n\n      // Invalidate all page caches to show new content immediately\n      try {\n        console.log('🗑️ Invalidating all page caches after sync completion...');\n        const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n        const cacheResponse = await fetch(`${baseUrl}/api/cache/invalidate?type=all`, {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' }\n        });\n\n        if (cacheResponse.ok) {\n          console.log('✅ All page caches invalidated successfully');\n        } else {\n          console.log('⚠️ Failed to invalidate page caches, but sync completed');\n        }\n      } catch (cacheError) {\n        console.log('⚠️ Cache invalidation failed, but sync completed:', cacheError);\n      }\n\n      console.log('🎉 UNIVERSAL SYNC COMPLETED SUCCESSFULLY!');\n      console.log('═══════════════════════════════════════════════════════════');\n      console.log(`⏰ Start Time: ${utcTime.toISOString()}`);\n      console.log(`⏰ End Time: ${endTime}`);\n      console.log(`⏱️ TOTAL DURATION: ${totalDuration}s (${totalMinutes} minutes)`);\n      console.log('═══════════════════════════════════════════════════════════');\n      console.log('📊 STEP-BY-STEP BREAKDOWN:');\n      console.log(`   🎬 Step 1 - Movies: ${results.results.movies.duration} (${results.results.movies.count} items)`);\n      console.log(`   📺 Step 2 - Series: ${results.results.series.duration} (${results.results.series.count} items)`);\n      console.log(`   🎭 Step 3 - Episodes: ${results.results.episodes.duration} (${results.results.episodes.count} items)`);\n      console.log(`   📚 Step 4 - Series Episodes: SKIPPED (handled on-demand when users visit series pages)`);\n      console.log('═══════════════════════════════════════════════════════════');\n      console.log(`📅 Next Sync Scheduled: ${results.nextSyncTime}`);\n      console.log('═══════════════════════════════════════════════════════════');\n      \n      return results;\n      \n    } catch (error) {\n      const totalDuration = (Date.now() - startTime) / 1000;\n      const totalMinutes = Math.round(totalDuration / 60 * 100) / 100;\n      const endTime = new Date().toISOString();\n      results.totalDuration = `${totalDuration}s`;\n\n      console.error('❌ UNIVERSAL SYNC FAILED!');\n      console.error('═══════════════════════════════════════════════════════════');\n      console.error(`⏰ Start Time: ${utcTime.toISOString()}`);\n      console.error(`⏰ End Time: ${endTime}`);\n      console.error(`⏱️ DURATION BEFORE FAILURE: ${totalDuration}s (${totalMinutes} minutes)`);\n      console.error(`❌ Error: ${error.message}`);\n      console.error('═══════════════════════════════════════════════════════════');\n\n      // Update sync status with error\n      await this.updateSyncStatus(false, results);\n\n      throw error;\n    }\n  }\n\n  /**\n   * Sync movies using the full VidSrc sync\n   */\n  private async syncMovies(): Promise<{ success: boolean; count: number; duration: string; error?: string }> {\n    const startTime = Date.now();\n    \n    try {\n      console.log('📥 Calling VidSrc movies sync...');\n      \n      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n      const response = await fetch(`${baseUrl}/api/sync?action=force`, {\n        method: 'GET',\n        headers: { 'Content-Type': 'application/json' }\n      });\n      \n      const result = await response.json();\n      const duration = (Date.now() - startTime) / 1000;\n      \n      if (response.ok && result.success) {\n        console.log(`✅ Movies sync completed: ${result.data?.movies || 0} movies`);\n        return {\n          success: true,\n          count: result.data?.movies || 0,\n          duration: `${duration}s`\n        };\n      } else {\n        throw new Error(result.message || 'Movies sync failed');\n      }\n      \n    } catch (error) {\n      const duration = (Date.now() - startTime) / 1000;\n      console.error('❌ Movies sync error:', error);\n      return {\n        success: false,\n        count: 0,\n        duration: `${duration}s`,\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * Sync series using the full VidSrc sync\n   */\n  private async syncSeries(): Promise<{ success: boolean; count: number; duration: string; error?: string }> {\n    const startTime = Date.now();\n    \n    try {\n      console.log('📥 Calling VidSrc series sync...');\n      \n      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n      const response = await fetch(`${baseUrl}/api/sync?action=force`, {\n        method: 'GET',\n        headers: { 'Content-Type': 'application/json' }\n      });\n      \n      const result = await response.json();\n      const duration = (Date.now() - startTime) / 1000;\n      \n      if (response.ok && result.success) {\n        console.log(`✅ Series sync completed: ${result.data?.series || 0} series`);\n        return {\n          success: true,\n          count: result.data?.series || 0,\n          duration: `${duration}s`\n        };\n      } else {\n        throw new Error(result.message || 'Series sync failed');\n      }\n      \n    } catch (error) {\n      const duration = (Date.now() - startTime) / 1000;\n      console.error('❌ Series sync error:', error);\n      return {\n        success: false,\n        count: 0,\n        duration: `${duration}s`,\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * Sync episodes using the dedicated episodes sync\n   */\n  private async syncEpisodes(): Promise<{ success: boolean; count: number; duration: string; error?: string }> {\n    const startTime = Date.now();\n    \n    try {\n      console.log('📥 Calling VidSrc episodes sync...');\n      \n      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n      const response = await fetch(`${baseUrl}/api/sync/vidsrc-episodes`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' }\n      });\n      \n      const result = await response.json();\n      const duration = (Date.now() - startTime) / 1000;\n      \n      if (response.ok && result.success) {\n        console.log(`✅ Episodes sync completed: ${result.stats?.totalEpisodes || 0} episodes`);\n        return {\n          success: true,\n          count: result.stats?.totalEpisodes || 0,\n          duration: `${duration}s`\n        };\n      } else {\n        throw new Error(result.message || 'Episodes sync failed');\n      }\n      \n    } catch (error) {\n      const duration = (Date.now() - startTime) / 1000;\n      console.error('❌ Episodes sync error:', error);\n      return {\n        success: false,\n        count: 0,\n        duration: `${duration}s`,\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * Sync ALL episodes for ALL series in database using IMDb data (Comprehensive)\n   * This replaces the individual episode syncing that happened on watch page visits\n   */\n  private async syncAllSeriesEpisodes(): Promise<{ success: boolean; seriesProcessed: number; episodesAdded: number; duration: string; error?: string }> {\n    const startTime = Date.now();\n\n    try {\n      console.log('📚 COMPREHENSIVE SERIES EPISODES SYNC - IMDb Based');\n      console.log('🎯 Goal: Fetch ALL episodes for ALL series from IMDb and save to database');\n      console.log('📋 This replaces individual episode syncing from watch pages');\n\n      await connectDB();\n      const { default: Series } = await import('@/models/Series');\n      const { default: Episode } = await import('@/models/Episode');\n      const { default: IMDbEpisodeScraper } = await import('@/lib/imdbEpisodeScraper');\n\n      // Get all series from database (no type filter - Series collection only contains series)\n      const allSeries = await Series.find({}, { imdbId: 1, title: 1, tmdbId: 1, _id: 1 }).lean();\n      console.log(`📊 Found ${allSeries.length} series in database to process for comprehensive episode sync`);\n\n      if (allSeries.length === 0) {\n        const duration = (Date.now() - startTime) / 1000;\n        return {\n          success: true,\n          seriesProcessed: 0,\n          episodesAdded: 0,\n          duration: `${duration}s`\n        };\n      }\n\n      let seriesProcessed = 0;\n      let totalEpisodesAdded = 0;\n      const batchSize = 10; // Process 10 series at a time to avoid overwhelming IMDb\n      const imdbScraper = IMDbEpisodeScraper.getInstance();\n\n      console.log(`🚀 OPTIMIZED PROCESSING: ${batchSize} series at a time with rate limiting for IMDb compatibility`);\n\n      // Process series in large parallel batches\n      for (let i = 0; i < allSeries.length; i += batchSize) {\n        const batch = allSeries.slice(i, i + batchSize);\n        const batchNumber = Math.floor(i/batchSize) + 1;\n        const totalBatches = Math.ceil(allSeries.length/batchSize);\n\n        console.log(`⚡ Processing PARALLEL batch ${batchNumber}/${totalBatches} (${batch.length} series simultaneously)`);\n        const batchStartTime = Date.now();\n\n        const batchPromises = batch.map(async (series, index) => {\n          try {\n            const seriesStartTime = Date.now();\n            console.log(`📺 [${index + 1}/${batch.length}] Processing: ${series.title} (${series.imdbId})`);\n\n            // First, get existing episodes to check if series is already complete\n            const existingEpisodes = await Episode.find({ imdbId: series.imdbId }).lean();\n\n            // Create existing episodes map for fast lookup\n            const existingEpisodeMap = new Set<string>();\n            existingEpisodes.forEach(ep => {\n              existingEpisodeMap.add(`S${ep.season}E${ep.episode}`);\n            });\n\n            console.log(`🔍 ${series.title}: Checking ${existingEpisodes.length} existing episodes...`);\n\n            // Get IMDb episode data to check completeness\n            const imdbEpisodeData = await imdbScraper.getSeriesEpisodes(series.imdbId);\n\n            if (!imdbEpisodeData) {\n              console.log(`⚠️ No IMDb data: ${series.title}`);\n              return { success: false, episodesAdded: 0, seriesTitle: series.title };\n            }\n\n            // Check if series is already complete (optimization)\n            const totalImdbEpisodes = imdbEpisodeData.totalEpisodes;\n            const existingCount = existingEpisodes.length;\n\n            if (existingCount >= totalImdbEpisodes && totalImdbEpisodes > 0) {\n              console.log(`⏭️ ${series.title}: Already complete (${existingCount}/${totalImdbEpisodes} episodes) - SKIPPING`);\n              return {\n                success: true,\n                episodesAdded: 0,\n                seriesTitle: series.title,\n                duration: (Date.now() - seriesStartTime) / 1000\n              };\n            }\n\n            console.log(`🎯 ${series.title}: ${totalImdbEpisodes} total episodes (${existingCount} existing, ${totalImdbEpisodes - existingCount} missing)`);\n\n            // Prepare episode operations for parallel processing (ONLY missing episodes)\n            const episodeOperations = [];\n            const newEpisodeKeys = [];\n\n            // Process ONLY missing episodes from IMDb data (optimization)\n            for (const season of imdbEpisodeData.seasons) {\n              for (const episode of season.episodes) {\n                const episodeKey = `S${episode.season}E${episode.episode}`;\n\n                // OPTIMIZATION: Skip if episode already exists\n                if (existingEpisodeMap.has(episodeKey)) {\n                  continue; // Skip existing episodes - no processing needed\n                }\n\n                // Generate VidSrc embed URLs for missing episodes only\n                const vidsrcEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${episode.season}&episode=${episode.episode}`;\n                const vidsrcTmdbUrl = series.tmdbId ? `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId}&season=${episode.season}&episode=${episode.episode}` : '';\n\n                // Create episode data with explicit field control (no seriesId)\n                const episodeData = {\n                  imdbId: series.imdbId,\n                  tmdbId: series.tmdbId || undefined,\n                  seriesTitle: series.title,\n                  season: episode.season,\n                  episode: episode.episode,\n                  episodeTitle: episode.title || `Episode ${episode.episode}`,\n                  description: episode.description || undefined,\n                  airDate: episode.airDate ? new Date(episode.airDate) : undefined,\n                  runtime: episode.runtime || '45 min',\n                  embedUrl: vidsrcEmbedUrl,\n                  embedUrlTmdb: vidsrcTmdbUrl || undefined,\n                  vidsrcUrl: vidsrcEmbedUrl,\n                  vidsrcTmdbUrl: vidsrcTmdbUrl || undefined\n                };\n\n                // Add to parallel operations (only for missing episodes)\n                episodeOperations.push(\n                  Episode.findOneAndUpdate(\n                    {\n                      imdbId: series.imdbId,\n                      season: episode.season,\n                      episode: episode.episode\n                    },\n                    episodeData,\n                    {\n                      upsert: true,\n                      new: true\n                    }\n                  )\n                );\n\n                // Track new episodes\n                newEpisodeKeys.push(episodeKey);\n              }\n            }\n\n            // Execute episode operations in parallel (only for missing episodes)\n            if (episodeOperations.length > 0) {\n              console.log(`📝 Processing ${episodeOperations.length} missing episodes for ${series.title}...`);\n              await Promise.allSettled(episodeOperations);\n            } else {\n              console.log(`⚡ No missing episodes to process for ${series.title}`);\n            }\n\n            const seriesDuration = (Date.now() - seriesStartTime) / 1000;\n            console.log(`✅ ${series.title}: ${newEpisodeKeys.length} new episodes added in ${seriesDuration}s`);\n\n            return {\n              success: true,\n              episodesAdded: newEpisodeKeys.length,\n              seriesTitle: series.title,\n              duration: seriesDuration\n            };\n\n          } catch (error) {\n            console.error(`❌ Error processing series ${series.title}:`, error);\n            return { success: false, episodesAdded: 0, seriesTitle: series.title };\n          }\n        });\n\n        // Execute all series in parallel and wait for completion\n        const batchResults = await Promise.allSettled(batchPromises);\n        const batchDuration = (Date.now() - batchStartTime) / 1000;\n\n        // Count successful series and episodes\n        let batchSuccessful = 0;\n        let batchEpisodesAdded = 0;\n\n        batchResults.forEach((result, index) => {\n          if (result.status === 'fulfilled' && result.value.success) {\n            batchSuccessful++;\n            batchEpisodesAdded += result.value.episodesAdded;\n            seriesProcessed++;\n            totalEpisodesAdded += result.value.episodesAdded;\n          } else {\n            const seriesTitle = batch[index]?.title || 'Unknown';\n            console.error(`❌ Failed: ${seriesTitle}`);\n          }\n        });\n\n        console.log(`⚡ Batch ${batchNumber} completed: ${batchSuccessful}/${batch.length} series, ${batchEpisodesAdded} episodes in ${batchDuration}s`);\n\n        // Respectful delay between batches to avoid overwhelming IMDb\n        if (i + batchSize < allSeries.length) {\n          console.log('⏳ Respectful pause before next batch to avoid rate limiting...');\n          await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay between batches\n        }\n      }\n\n      const duration = (Date.now() - startTime) / 1000;\n\n      console.log(`🎉 COMPREHENSIVE EPISODE SYNC COMPLETED!`);\n      console.log(`📊 Results: ${seriesProcessed}/${allSeries.length} series processed`);\n      console.log(`📺 Episodes: ${totalEpisodesAdded} new episodes added to database`);\n      console.log(`⏱️ Duration: ${duration}s`);\n\n      return {\n        success: true,\n        seriesProcessed,\n        episodesAdded: totalEpisodesAdded,\n        duration: `${duration}s`\n      };\n\n    } catch (error) {\n      const duration = (Date.now() - startTime) / 1000;\n      console.error('❌ Comprehensive series episodes sync error:', error);\n      return {\n        success: false,\n        seriesProcessed: 0,\n        episodesAdded: 0,\n        duration: `${duration}s`,\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * Update sync status in database\n   */\n  private async updateSyncStatus(isRunning: boolean, results?: UniversalSyncResult, nextSyncTime?: Date): Promise<void> {\n    try {\n      await connectDB();\n      const { default: SyncStatus } = await import('@/models/SyncStatus');\n\n      const now = new Date();\n      const updateData: any = {\n        isRunning,\n        updatedAt: now\n      };\n\n      if (!isRunning) {\n        updateData.lastSyncTime = now;\n        if (results) {\n          updateData.lastResult = results;\n        }\n        if (nextSyncTime) {\n          updateData.nextSyncTime = nextSyncTime;\n        } else {\n          updateData.nextSyncTime = this.calculateNextSyncTime(now);\n        }\n      }\n\n      await SyncStatus.updateOne(\n        { syncType: this.SYNC_TYPE },\n        updateData,\n        { upsert: true }\n      );\n\n      console.log(`📊 Updated Universal Sync status: isRunning=${isRunning}`);\n    } catch (error) {\n      console.error('❌ Error updating sync status:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current sync status\n   */\n  public async getSyncStatus(): Promise<any> {\n    try {\n      await connectDB();\n      const { default: SyncStatus } = await import('@/models/SyncStatus');\n      return await SyncStatus.findOne({ syncType: this.SYNC_TYPE });\n    } catch (error) {\n      console.error('❌ Error getting sync status:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Force manual sync\n   */\n  public async forceSync(): Promise<UniversalSyncResult> {\n    console.log('🔧 Manual universal sync requested...');\n    return this.runUniversalSync();\n  }\n\n  /**\n   * Destroy the service\n   */\n  public destroy(): void {\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval);\n      this.syncInterval = null;\n    }\n    this.isInitialized = false;\n    console.log('🛑 Universal Sync Service destroyed');\n  }\n}\n\nexport default UniversalSyncService;\n"], "names": [], "mappings": ";;;AAAA;;AAsCA,MAAM;IACJ,OAAe,SAA+B;IACtC,eAAsC,KAAK;IAC3C,gBAAgB,MAAM;IACb,YAAY,iBAAiB;IAC7B,sBAAsB,EAAE;IAEzC,aAAsB,CAAC;IAEvB,OAAc,cAAoC;QAChD,IAAI,CAAC,qBAAqB,QAAQ,EAAE;YAClC,qBAAqB,QAAQ,GAAG,IAAI;QACtC;QACA,OAAO,qBAAqB,QAAQ;IACtC;IAEA;;GAEC,GACD,MAAa,aAA4B;QACvC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,MAAM,IAAI,CAAC,gBAAgB;YAE3B,sBAAsB;YACtB,IAAI,CAAC,uBAAuB;YAE5B,uCAAuC;YACvC,MAAM,IAAI,CAAC,eAAe;YAE1B,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAc,mBAAkC;QAC9C,IAAI;YACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;YAEhC,MAAM,WAAW,MAAM,WAAW,OAAO,CAAC;gBAAE,UAAU,IAAI,CAAC,SAAS;YAAC;YAErE,IAAI,CAAC,UAAU;gBACb,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC;gBAE5C,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;gBAC1D,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,SAAS,WAAW,IAAI;gBAEnE,MAAM,gBAAgB,MAAM,WAAW,MAAM,CAAC;oBAC5C,UAAU,IAAI,CAAC,SAAS;oBACxB,cAAc,IAAI,KAAK;oBACvB,cAAc;oBACd,WAAW;oBACX,YAAY;gBACd;gBAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,cAAc,GAAG,EAAE;YAC3E,OAAO;gBACL,wCAAwC;gBACxC,IAAI,cAAc;gBAClB,MAAM,aAAkB,CAAC;gBAEzB,IAAI,CAAC,SAAS,YAAY,EAAE;oBAC1B,MAAM,MAAM,IAAI;oBAChB,WAAW,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC;oBACrD,cAAc;oBACd,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC;gBAC9C;gBAEA,IAAI,CAAC,SAAS,YAAY,EAAE;oBAC1B,WAAW,YAAY,GAAG,IAAI,KAAK;oBACnC,cAAc;oBACd,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC;gBAC9C;gBAEA,IAAI,OAAO,SAAS,SAAS,KAAK,WAAW;oBAC3C,WAAW,SAAS,GAAG;oBACvB,cAAc;oBACd,QAAQ,GAAG,CAAC,CAAC,yBAAyB,CAAC;gBACzC;gBAEA,IAAI,aAAa;oBACf,MAAM,WAAW,SAAS,CAAC;wBAAE,UAAU,IAAI,CAAC,SAAS;oBAAC,GAAG;oBACzD,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC;gBACrD;gBAEA,MAAM,eAAe,WAAW,YAAY,IAAI,SAAS,YAAY;gBACrE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,cAAc,iBAAiB,WAAW;YACtG;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,QAAc,EAAQ;QAClD,MAAM,UAAU,IAAI,KAAK,SAAS,OAAO;QAEzC,uBAAuB;QACvB,MAAM,cAAc,QAAQ,WAAW;QAEvC,yDAAyD;QACzD,MAAM,eAAe,KAAK,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB;QAEvG,wBAAwB;QACxB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,WAAW,CAAC,eAAe,IAAI,GAAG,GAAG;QAE9C,sDAAsD;QACtD,IAAI,gBAAgB,IAAI;YACtB,SAAS,UAAU,CAAC,SAAS,UAAU,KAAK;QAC9C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,kBAAiC;QAC7C,IAAI;YACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;YAEhC,MAAM,aAAa,MAAM,WAAW,OAAO,CAAC;gBAAE,UAAU,IAAI,CAAC,SAAS;YAAC;YACvE,IAAI,CAAC,YAAY;gBACf,QAAQ,GAAG,CAAC;gBACZ,MAAM,IAAI,CAAC,gBAAgB;gBAC3B;YACF;YAEA,8CAA8C;YAC9C,IAAI,CAAC,WAAW,YAAY,EAAE;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC;gBAE5C,MAAM,WAAW,SAAS,CACxB;oBAAE,UAAU,IAAI,CAAC,SAAS;gBAAC,GAC3B;oBAAE,cAAc;gBAAS;gBAG3B,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,SAAS,WAAW,IAAI;gBAC9D;YACF;YAEA,MAAM,MAAM,IAAI;YAChB,MAAM,SAAS,IAAI,KAAK,IAAI,OAAO;YAEnC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,WAAW,IAAI;YACxD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,WAAW,YAAY,CAAC,WAAW,IAAI;YAE9E,qDAAqD;YACrD,IAAI,UAAU,WAAW,YAAY,IAAI,CAAC,WAAW,SAAS,EAAE;gBAC9D,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBACZ,MAAM,IAAI,CAAC,gBAAgB;YAC7B,OAAO,IAAI,WAAW,SAAS,EAAE;gBAC/B,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,WAAW,SAAS,EAAE,iBAAiB,WAAW;gBACjG,QAAQ,GAAG,CAAC,CAAC,8DAA8D,CAAC;YAC9E,OAAO;gBACL,MAAM,gBAAgB,WAAW,YAAY,CAAC,OAAO,KAAK,OAAO,OAAO;gBACxE,MAAM,iBAAiB,KAAK,KAAK,CAAC,gBAAgB,CAAC,OAAO,KAAK,KAAK,GAAG,KAAK;gBAC5E,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,eAAe,MAAM,CAAC;gBAC9D,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,WAAW,SAAS,CAAC,WAAW,EAAE,WAAW,YAAY,EAAE,iBAAiB,SAAS;YAChI;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA;;GAEC,GACD,AAAQ,0BAAgC;QACtC,8CAA8C;QAC9C,IAAI,CAAC,YAAY,GAAG,YAAY;YAC9B,MAAM,IAAI,CAAC,eAAe;QAC5B,GAAG,KAAK,KAAK,OAAO,aAAa;QAEjC,QAAQ,GAAG,CAAC;IACd;IAEA;;GAEC,GACD,MAAa,mBAAiD;QAC5D,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,UAAU,IAAI;QAEpB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,WAAW,IAAI;QAEnD,uBAAuB;QACvB,MAAM,IAAI,CAAC,gBAAgB,CAAC;QAE5B,MAAM,UAA+B;YACnC,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS,QAAQ,WAAW;YAC5B,cAAc;YACd,SAAS;gBACP,QAAQ;oBAAE,SAAS;oBAAO,OAAO;oBAAG,UAAU;gBAAK;gBACnD,QAAQ;oBAAE,SAAS;oBAAO,OAAO;oBAAG,UAAU;gBAAK;gBACnD,UAAU;oBAAE,SAAS;oBAAO,OAAO;oBAAG,UAAU;gBAAK;gBACrD,gBAAgB;oBAAE,SAAS;oBAAO,iBAAiB;oBAAG,eAAe;oBAAG,UAAU;gBAAK;YACzF;YACA,eAAe;QACjB;QAEA,IAAI;YACF,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa,KAAK,GAAG;YAC3B,QAAQ,OAAO,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU;YAC9C,MAAM,gBAAgB,CAAC,KAAK,GAAG,KAAK,UAAU,IAAI;YAClD,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,cAAc,CAAC,CAAC;YAE3E,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa,KAAK,GAAG;YAC3B,QAAQ,OAAO,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU;YAC9C,MAAM,gBAAgB,CAAC,KAAK,GAAG,KAAK,UAAU,IAAI;YAClD,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,cAAc,CAAC,CAAC;YAE3E,wBAAwB;YACxB,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa,KAAK,GAAG;YAC3B,QAAQ,OAAO,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY;YAClD,MAAM,gBAAgB,CAAC,KAAK,GAAG,KAAK,UAAU,IAAI;YAClD,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,cAAc,CAAC,CAAC;YAE7E,uCAAuC;YACvC,qEAAqE;YACrE,QAAQ,GAAG,CAAC;YACZ,QAAQ,OAAO,CAAC,cAAc,GAAG;gBAC/B,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,UAAU;YACZ;YAEA,2BAA2B;YAC3B,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC;YAC5C,QAAQ,YAAY,GAAG,SAAS,WAAW;YAE3C,oDAAoD;YACpD,QAAQ,OAAO,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,OAAO,IAC/B,QAAQ,OAAO,CAAC,MAAM,CAAC,OAAO,IAC9B,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,IAChC,QAAQ,OAAO,CAAC,cAAc,CAAC,OAAO;YAEvD,MAAM,gBAAgB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YACjD,MAAM,eAAe,KAAK,KAAK,CAAC,gBAAgB,KAAK,OAAO;YAC5D,MAAM,UAAU,IAAI,OAAO,WAAW;YACtC,QAAQ,aAAa,GAAG,GAAG,cAAc,CAAC,CAAC;YAE3C,qBAAqB;YACrB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,SAAS;YAE5C,6DAA6D;YAC7D,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI;gBAC5C,MAAM,gBAAgB,MAAM,MAAM,GAAG,QAAQ,8BAA8B,CAAC,EAAE;oBAC5E,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;gBAChD;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,GAAG,CAAC,qDAAqD;YACnE;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,WAAW,IAAI;YACpD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS;YACpC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,cAAc,GAAG,EAAE,aAAa,SAAS,CAAC;YAC5E,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YAC/G,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YAC/G,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC;YACrH,QAAQ,GAAG,CAAC,CAAC,yFAAyF,CAAC;YACvG,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ,YAAY,EAAE;YAC7D,QAAQ,GAAG,CAAC;YAEZ,OAAO;QAET,EAAE,OAAO,OAAO;YACd,MAAM,gBAAgB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YACjD,MAAM,eAAe,KAAK,KAAK,CAAC,gBAAgB,KAAK,OAAO;YAC5D,MAAM,UAAU,IAAI,OAAO,WAAW;YACtC,QAAQ,aAAa,GAAG,GAAG,cAAc,CAAC,CAAC;YAE3C,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,QAAQ,WAAW,IAAI;YACtD,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,SAAS;YACtC,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,cAAc,GAAG,EAAE,aAAa,SAAS,CAAC;YACvF,QAAQ,KAAK,CAAC,CAAC,SAAS,EAAE,MAAM,OAAO,EAAE;YACzC,QAAQ,KAAK,CAAC;YAEd,gCAAgC;YAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAEnC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAc,aAA6F;QACzG,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI;YAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,sBAAsB,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAE5C,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,OAAO,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC;gBACzE,OAAO;oBACL,SAAS;oBACT,OAAO,OAAO,IAAI,EAAE,UAAU;oBAC9B,UAAU,GAAG,SAAS,CAAC,CAAC;gBAC1B;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAC5C,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU,GAAG,SAAS,CAAC,CAAC;gBACxB,OAAO,MAAM,OAAO;YACtB;QACF;IACF;IAEA;;GAEC,GACD,MAAc,aAA6F;QACzG,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI;YAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,sBAAsB,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAE5C,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,OAAO,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC;gBACzE,OAAO;oBACL,SAAS;oBACT,OAAO,OAAO,IAAI,EAAE,UAAU;oBAC9B,UAAU,GAAG,SAAS,CAAC,CAAC;gBAC1B;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAC5C,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU,GAAG,SAAS,CAAC,CAAC;gBACxB,OAAO,MAAM,OAAO;YACtB;QACF;IACF;IAEA;;GAEC,GACD,MAAc,eAA+F;QAC3G,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI;YAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,yBAAyB,CAAC,EAAE;gBAClE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAE5C,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,OAAO,KAAK,EAAE,iBAAiB,EAAE,SAAS,CAAC;gBACrF,OAAO;oBACL,SAAS;oBACT,OAAO,OAAO,KAAK,EAAE,iBAAiB;oBACtC,UAAU,GAAG,SAAS,CAAC,CAAC;gBAC1B;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAC5C,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU,GAAG,SAAS,CAAC,CAAC;gBACxB,OAAO,MAAM,OAAO;YACtB;QACF;IACF;IAEA;;;GAGC,GACD,MAAc,wBAAyI;QACrJ,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YAEZ,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,MAAM,EAAE,SAAS,MAAM,EAAE,GAAG;YAC5B,MAAM,EAAE,SAAS,OAAO,EAAE,GAAG;YAC7B,MAAM,EAAE,SAAS,kBAAkB,EAAE,GAAG;YAExC,yFAAyF;YACzF,MAAM,YAAY,MAAM,OAAO,IAAI,CAAC,CAAC,GAAG;gBAAE,QAAQ;gBAAG,OAAO;gBAAG,QAAQ;gBAAG,KAAK;YAAE,GAAG,IAAI;YACxF,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,MAAM,CAAC,6DAA6D,CAAC;YAEvG,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;gBAC5C,OAAO;oBACL,SAAS;oBACT,iBAAiB;oBACjB,eAAe;oBACf,UAAU,GAAG,SAAS,CAAC,CAAC;gBAC1B;YACF;YAEA,IAAI,kBAAkB;YACtB,IAAI,qBAAqB;YACzB,MAAM,YAAY,IAAI,yDAAyD;YAC/E,MAAM,cAAc,mBAAmB,WAAW;YAElD,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,UAAU,2DAA2D,CAAC;YAE9G,2CAA2C;YAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,UAAW;gBACpD,MAAM,QAAQ,UAAU,KAAK,CAAC,GAAG,IAAI;gBACrC,MAAM,cAAc,KAAK,KAAK,CAAC,IAAE,aAAa;gBAC9C,MAAM,eAAe,KAAK,IAAI,CAAC,UAAU,MAAM,GAAC;gBAEhD,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,EAAE,aAAa,EAAE,EAAE,MAAM,MAAM,CAAC,uBAAuB,CAAC;gBAChH,MAAM,iBAAiB,KAAK,GAAG;gBAE/B,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO,QAAQ;oBAC7C,IAAI;wBACF,MAAM,kBAAkB,KAAK,GAAG;wBAChC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,MAAM,CAAC,cAAc,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;wBAE9F,sEAAsE;wBACtE,MAAM,mBAAmB,MAAM,QAAQ,IAAI,CAAC;4BAAE,QAAQ,OAAO,MAAM;wBAAC,GAAG,IAAI;wBAE3E,+CAA+C;wBAC/C,MAAM,qBAAqB,IAAI;wBAC/B,iBAAiB,OAAO,CAAC,CAAA;4BACvB,mBAAmB,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE;wBACtD;wBAEA,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC,WAAW,EAAE,iBAAiB,MAAM,CAAC,qBAAqB,CAAC;wBAE1F,8CAA8C;wBAC9C,MAAM,kBAAkB,MAAM,YAAY,iBAAiB,CAAC,OAAO,MAAM;wBAEzE,IAAI,CAAC,iBAAiB;4BACpB,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,KAAK,EAAE;4BAC9C,OAAO;gCAAE,SAAS;gCAAO,eAAe;gCAAG,aAAa,OAAO,KAAK;4BAAC;wBACvE;wBAEA,qDAAqD;wBACrD,MAAM,oBAAoB,gBAAgB,aAAa;wBACvD,MAAM,gBAAgB,iBAAiB,MAAM;wBAE7C,IAAI,iBAAiB,qBAAqB,oBAAoB,GAAG;4BAC/D,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC,oBAAoB,EAAE,cAAc,CAAC,EAAE,kBAAkB,qBAAqB,CAAC;4BAC9G,OAAO;gCACL,SAAS;gCACT,eAAe;gCACf,aAAa,OAAO,KAAK;gCACzB,UAAU,CAAC,KAAK,GAAG,KAAK,eAAe,IAAI;4BAC7C;wBACF;wBAEA,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,kBAAkB,iBAAiB,EAAE,cAAc,WAAW,EAAE,oBAAoB,cAAc,SAAS,CAAC;wBAE/I,6EAA6E;wBAC7E,MAAM,oBAAoB,EAAE;wBAC5B,MAAM,iBAAiB,EAAE;wBAEzB,8DAA8D;wBAC9D,KAAK,MAAM,UAAU,gBAAgB,OAAO,CAAE;4BAC5C,KAAK,MAAM,WAAW,OAAO,QAAQ,CAAE;gCACrC,MAAM,aAAa,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;gCAE1D,+CAA+C;gCAC/C,IAAI,mBAAmB,GAAG,CAAC,aAAa;oCACtC,UAAU,gDAAgD;gCAC5D;gCAEA,uDAAuD;gCACvD,MAAM,iBAAiB,CAAC,gCAAgC,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;gCAC7H,MAAM,gBAAgB,OAAO,MAAM,GAAG,CAAC,gCAAgC,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE,GAAG;gCAE/I,gEAAgE;gCAChE,MAAM,cAAc;oCAClB,QAAQ,OAAO,MAAM;oCACrB,QAAQ,OAAO,MAAM,IAAI;oCACzB,aAAa,OAAO,KAAK;oCACzB,QAAQ,QAAQ,MAAM;oCACtB,SAAS,QAAQ,OAAO;oCACxB,cAAc,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE;oCAC3D,aAAa,QAAQ,WAAW,IAAI;oCACpC,SAAS,QAAQ,OAAO,GAAG,IAAI,KAAK,QAAQ,OAAO,IAAI;oCACvD,SAAS,QAAQ,OAAO,IAAI;oCAC5B,UAAU;oCACV,cAAc,iBAAiB;oCAC/B,WAAW;oCACX,eAAe,iBAAiB;gCAClC;gCAEA,yDAAyD;gCACzD,kBAAkB,IAAI,CACpB,QAAQ,gBAAgB,CACtB;oCACE,QAAQ,OAAO,MAAM;oCACrB,QAAQ,QAAQ,MAAM;oCACtB,SAAS,QAAQ,OAAO;gCAC1B,GACA,aACA;oCACE,QAAQ;oCACR,KAAK;gCACP;gCAIJ,qBAAqB;gCACrB,eAAe,IAAI,CAAC;4BACtB;wBACF;wBAEA,qEAAqE;wBACrE,IAAI,kBAAkB,MAAM,GAAG,GAAG;4BAChC,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,kBAAkB,MAAM,CAAC,sBAAsB,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC;4BAC/F,MAAM,QAAQ,UAAU,CAAC;wBAC3B,OAAO;4BACL,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,OAAO,KAAK,EAAE;wBACpE;wBAEA,MAAM,iBAAiB,CAAC,KAAK,GAAG,KAAK,eAAe,IAAI;wBACxD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,eAAe,MAAM,CAAC,uBAAuB,EAAE,eAAe,CAAC,CAAC;wBAElG,OAAO;4BACL,SAAS;4BACT,eAAe,eAAe,MAAM;4BACpC,aAAa,OAAO,KAAK;4BACzB,UAAU;wBACZ;oBAEF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,EAAE;wBAC5D,OAAO;4BAAE,SAAS;4BAAO,eAAe;4BAAG,aAAa,OAAO,KAAK;wBAAC;oBACvE;gBACF;gBAEA,yDAAyD;gBACzD,MAAM,eAAe,MAAM,QAAQ,UAAU,CAAC;gBAC9C,MAAM,gBAAgB,CAAC,KAAK,GAAG,KAAK,cAAc,IAAI;gBAEtD,uCAAuC;gBACvC,IAAI,kBAAkB;gBACtB,IAAI,qBAAqB;gBAEzB,aAAa,OAAO,CAAC,CAAC,QAAQ;oBAC5B,IAAI,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,CAAC,OAAO,EAAE;wBACzD;wBACA,sBAAsB,OAAO,KAAK,CAAC,aAAa;wBAChD;wBACA,sBAAsB,OAAO,KAAK,CAAC,aAAa;oBAClD,OAAO;wBACL,MAAM,cAAc,KAAK,CAAC,MAAM,EAAE,SAAS;wBAC3C,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,aAAa;oBAC1C;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,YAAY,EAAE,gBAAgB,CAAC,EAAE,MAAM,MAAM,CAAC,SAAS,EAAE,mBAAmB,aAAa,EAAE,cAAc,CAAC,CAAC;gBAE9I,8DAA8D;gBAC9D,IAAI,IAAI,YAAY,UAAU,MAAM,EAAE;oBACpC,QAAQ,GAAG,CAAC;oBACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,iCAAiC;gBAC5F;YACF;YAEA,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAE5C,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC;YACtD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,gBAAgB,CAAC,EAAE,UAAU,MAAM,CAAC,iBAAiB,CAAC;YACjF,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,mBAAmB,+BAA+B,CAAC;YAC/E,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAEvC,OAAO;gBACL,SAAS;gBACT;gBACA,eAAe;gBACf,UAAU,GAAG,SAAS,CAAC,CAAC;YAC1B;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;YAC5C,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,OAAO;gBACL,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,UAAU,GAAG,SAAS,CAAC,CAAC;gBACxB,OAAO,MAAM,OAAO;YACtB;QACF;IACF;IAEA;;GAEC,GACD,MAAc,iBAAiB,SAAkB,EAAE,OAA6B,EAAE,YAAmB,EAAiB;QACpH,IAAI;YACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;YAEhC,MAAM,MAAM,IAAI;YAChB,MAAM,aAAkB;gBACtB;gBACA,WAAW;YACb;YAEA,IAAI,CAAC,WAAW;gBACd,WAAW,YAAY,GAAG;gBAC1B,IAAI,SAAS;oBACX,WAAW,UAAU,GAAG;gBAC1B;gBACA,IAAI,cAAc;oBAChB,WAAW,YAAY,GAAG;gBAC5B,OAAO;oBACL,WAAW,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC;gBACvD;YACF;YAEA,MAAM,WAAW,SAAS,CACxB;gBAAE,UAAU,IAAI,CAAC,SAAS;YAAC,GAC3B,YACA;gBAAE,QAAQ;YAAK;YAGjB,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,WAAW;QACxE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAa,gBAA8B;QACzC,IAAI;YACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;YAChC,OAAO,MAAM,WAAW,OAAO,CAAC;gBAAE,UAAU,IAAI,CAAC,SAAS;YAAC;QAC7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAa,YAA0C;QACrD,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,cAAc,IAAI,CAAC,YAAY;YAC/B,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/admin/init-universal-sync/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport UniversalSyncService from '@/lib/universalSyncService';\n\n/**\n * Initialize the Universal Sync Service\n * This endpoint should be called once when the application starts\n */\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('🌍 Initializing Universal Sync Service...');\n    \n    const universalSync = UniversalSyncService.getInstance();\n    await universalSync.initialize();\n    \n    // Get current status\n    const status = await universalSync.getSyncStatus();\n    const utcNow = new Date().toISOString();\n    \n    console.log('✅ Universal Sync Service initialized successfully');\n    \n    return NextResponse.json({\n      success: true,\n      message: 'Universal Sync Service initialized successfully',\n      utcTime: utcNow,\n      status: {\n        isRunning: status?.isRunning || false,\n        lastSyncTime: status?.lastSyncTime || null,\n        nextSyncTime: status?.nextSyncTime || null,\n        syncType: status?.syncType || 'UNIVERSAL_SYNC'\n      }\n    });\n\n  } catch (error) {\n    console.error('❌ Failed to initialize Universal Sync Service:', error);\n    \n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to initialize Universal Sync Service',\n        message: error.message,\n        utcTime: new Date().toISOString()\n      },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * POST method for manual initialization\n */\nexport async function POST(request: NextRequest) {\n  return GET(request);\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAMO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB,oIAAA,CAAA,UAAoB,CAAC,WAAW;QACtD,MAAM,cAAc,UAAU;QAE9B,qBAAqB;QACrB,MAAM,SAAS,MAAM,cAAc,aAAa;QAChD,MAAM,SAAS,IAAI,OAAO,WAAW;QAErC,QAAQ,GAAG,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;gBACN,WAAW,QAAQ,aAAa;gBAChC,cAAc,QAAQ,gBAAgB;gBACtC,cAAc,QAAQ,gBAAgB;gBACtC,UAAU,QAAQ,YAAY;YAChC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAEhE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,MAAM,OAAO;YACtB,SAAS,IAAI,OAAO,WAAW;QACjC,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe,KAAK,OAAoB;IAC7C,OAAO,IAAI;AACb", "debugId": null}}]}