import mongoose, { Schema, Document } from 'mongoose';

// Episode interface for embedded episodes within series
export interface IEpisode {
  season: number;
  episode: number;
  episodeTitle?: string;
  description?: string;
  airDate?: Date;
  runtime?: string;
  imdbRating?: number;
  posterUrl?: string;
  embedUrl: string;
  embedUrlTmdb?: string;
  vidsrcUrl: string;
  vidsrcTmdbUrl?: string;
  isLatestRelease?: boolean; // ✅ CRITICAL: VidSrc latest release flag
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ISeries extends Document {
  imdbId: string;
  tmdbId?: string;
  title: string;
  startYear: number;
  endYear?: number;
  rating?: string; // MPAA rating
  imdbRating?: number;
  imdbVotes?: string;
  popularity?: number;
  popularityDelta?: number;
  posterUrl?: string;
  trailerUrl?: string;
  description?: string;
  genres?: string[];
  creator?: string;
  cast?: string[];
  language?: string;
  country?: string;
  totalSeasons?: number;
  status?: string; // 'ongoing', 'ended', 'cancelled'
  embedUrl: string;
  embedUrlTmdb?: string;
  vidsrcUrl?: string; // VidSrc embed URL
  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL
  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection
  episodes: IEpisode[];
  episodeCount?: number; // Cache for quick access
  lastEpisodeUpdate?: Date; // Track when episodes were last synced
  createdAt: Date;
  updatedAt: Date;
}

const SeriesSchema: Schema = new Schema({
  imdbId: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  },
  tmdbId: { 
    type: String, 
    index: true 
  },
  title: {
    type: String,
    required: true
  },
  startYear: { 
    type: Number, 
    required: true,
    index: true 
  },
  endYear: { 
    type: Number,
    index: true 
  },
  rating: String,
  imdbRating: { 
    type: Number,
    index: true 
  },
  imdbVotes: String,
  popularity: { 
    type: Number,
    index: true 
  },
  popularityDelta: Number,
  posterUrl: String,
  trailerUrl: String,
  description: String,
  genres: [{ 
    type: String,
    index: true 
  }],
  creator: String,
  cast: [String],
  language: { 
    type: String,
    index: true 
  },
  country: {
    type: String,
    index: true
  },
  totalSeasons: Number,
  status: { 
    type: String,
    enum: ['ongoing', 'ended', 'cancelled'],
    index: true 
  },
  embedUrl: {
    type: String,
    required: true
  },
  embedUrlTmdb: String,
  vidsrcUrl: String, // VidSrc embed URL
  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL
  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection
  episodes: [{
    season: { type: Number, required: true },
    episode: { type: Number, required: true },
    episodeTitle: String,
    description: String,
    airDate: Date,
    runtime: { type: String, default: '45 min' },
    imdbRating: Number,
    posterUrl: String,
    embedUrl: { type: String, required: true },
    embedUrlTmdb: String,
    vidsrcUrl: { type: String, required: true },
    vidsrcTmdbUrl: String,
    isLatestRelease: { type: Boolean, default: false, index: true }, // ✅ CRITICAL: VidSrc latest release flag
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  }],
  episodeCount: { type: Number, default: 0 }, // Cache for quick access
  lastEpisodeUpdate: Date // Track when episodes were last synced
}, {
  timestamps: true
});

// Compound indexes for better query performance
SeriesSchema.index({ startYear: -1, imdbRating: -1 });
SeriesSchema.index({ genres: 1, startYear: -1 });
SeriesSchema.index({ status: 1, startYear: -1 });
// Removed text index to avoid language override issues
SeriesSchema.index({ title: 1 });
SeriesSchema.index({ language: 1, country: 1 });
// 🆕 EPISODE-SPECIFIC INDEXES for embedded episodes
SeriesSchema.index({ 'episodes.season': 1, 'episodes.episode': 1 });
SeriesSchema.index({ 'episodes.isLatestRelease': 1 }); // ✅ CRITICAL: For VidSrc latest episodes queries
SeriesSchema.index({ lastEpisodeUpdate: -1 }); // For sync tracking
SeriesSchema.index({ episodeCount: -1 }); // For quick episode count queries

export default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);
