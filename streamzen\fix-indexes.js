const mongoose = require('mongoose');

async function fixIndexes() {
  console.log('🚀 Starting index fix...');

  try {
    // Connect to MongoDB
    const mongoUri = 'mongodb+srv://saimmanchester12121212:<EMAIL>/streamzen?retryWrites=true&w=majority&appName=Cluster0';
    console.log('🔗 Connecting to MongoDB Atlas...');

    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB successfully');

    const db = mongoose.connection.db;

    // List existing indexes first
    console.log('📋 Checking existing indexes...');
    const movieIndexes = await db.collection('movies').indexes();
    const seriesIndexes = await db.collection('series').indexes();

    console.log('Movie indexes:', movieIndexes.map(idx => idx.name));
    console.log('Series indexes:', seriesIndexes.map(idx => idx.name));

    // Drop problematic text indexes
    for (const collection of ['movies', 'series']) {
      try {
        console.log(`🗑️ Dropping text indexes for ${collection}...`);
        const indexes = await db.collection(collection).indexes();

        for (const index of indexes) {
          if (index.name.includes('text')) {
            console.log(`Dropping index: ${index.name}`);
            await db.collection(collection).dropIndex(index.name);
            console.log(`✅ Dropped ${index.name}`);
          }
        }
      } catch (error) {
        console.log(`ℹ️ No text indexes to drop for ${collection}:`, error.message);
      }
    }

    // Create simple indexes instead of text indexes to avoid language issues
    console.log('🔧 Creating simple indexes...');

    await db.collection('movies').createIndex({ title: 1 }, { name: 'title_1' });
    await db.collection('movies').createIndex({ language: 1, country: 1 }, { name: 'language_country_1' });
    console.log('✅ Created Movie simple indexes');

    await db.collection('series').createIndex({ title: 1 }, { name: 'title_1' });
    await db.collection('series').createIndex({ language: 1, country: 1 }, { name: 'language_country_1' });
    console.log('✅ Created Series simple indexes');

    console.log('🎉 All indexes fixed successfully!');

  } catch (error) {
    console.error('❌ Error fixing indexes:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    try {
      await mongoose.disconnect();
      console.log('🔌 Disconnected from MongoDB');
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
    process.exit(0);
  }
}

console.log('Starting script...');
fixIndexes().catch(console.error);
