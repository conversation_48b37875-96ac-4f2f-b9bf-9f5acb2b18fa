'use client';

import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import ContentCard from './ContentCard';

interface ContentItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  seriesPosterUrl?: string; // For episodes to use their series poster
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series' | 'episode';
  season?: number;
  episode?: number;
  seriesTitle?: string;
}

interface ContentSectionProps {
  title: string;
  items: ContentItem[];
  viewAllHref?: string;
  className?: string;
}

const ContentSection: React.FC<ContentSectionProps> = ({
  title,
  items,
  viewAllHref,
  className
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  const checkScrollButtons = () => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    setCanScrollLeft(container.scrollLeft > 0);
    setCanScrollRight(
      container.scrollLeft < container.scrollWidth - container.clientWidth - 1
    );
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, [items]);

  const scroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const cardWidth = 280; // Approximate card width including gap
    const scrollAmount = cardWidth * 4; // Scroll 4 cards at a time

    container.scrollBy({
      left: direction === 'left' ? -scrollAmount : scrollAmount,
      behavior: 'smooth'
    });
  };

  if (!items.length) {
    return null;
  }

  return (
    <section
      className={cn('py-6 sm:py-8 md:py-12 animate-fade-in', className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="max-w-[2560px] mx-auto px-4 sm:px-6 lg:px-12">
        {/* Section Header - Mobile Optimized */}
        <div className="flex items-center justify-between mb-4 sm:mb-6 md:mb-8">
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-white tracking-tight">
            {title}
          </h2>
          {viewAllHref && (
            <Link
              href={viewAllHref}
              className="flex items-center space-x-1 sm:space-x-2 text-gray-400 hover:text-red-500 transition-all duration-300 group focus-ring rounded-lg px-2 sm:px-3 py-1 sm:py-2"
            >
              <span className="font-medium text-sm sm:text-base">View All</span>
              <ArrowRight size={16} className="sm:w-[18px] sm:h-[18px] group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          )}
        </div>

        {/* Content Container */}
        <div className="relative group/section">
          {/* Left Navigation Button */}
          <button
            onClick={() => scroll('left')}
            disabled={!canScrollLeft}
            className={cn(
              'absolute left-0 top-1/2 -translate-y-1/2 -translate-x-16 z-20',
              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',
              'transition-all duration-300 focus-ring shadow-2xl',
              'hover:scale-110 hover:bg-red-600/20',
              isHovered && canScrollLeft ? 'opacity-100 -translate-x-8' : 'opacity-0 -translate-x-16',
              !canScrollLeft && 'cursor-not-allowed opacity-30'
            )}
          >
            <ChevronLeft size={26} className="text-white" />
          </button>

          {/* Right Navigation Button */}
          <button
            onClick={() => scroll('right')}
            disabled={!canScrollRight}
            className={cn(
              'absolute right-0 top-1/2 -translate-y-1/2 translate-x-16 z-20',
              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',
              'transition-all duration-300 focus-ring shadow-2xl',
              'hover:scale-110 hover:bg-red-600/20',
              isHovered && canScrollRight ? 'opacity-100 translate-x-8' : 'opacity-0 translate-x-16',
              !canScrollRight && 'cursor-not-allowed opacity-30'
            )}
          >
            <ChevronRight size={26} className="text-white" />
          </button>

          {/* Scrollable Content - Mobile Optimized */}
          <div
            ref={scrollContainerRef}
            className="flex space-x-3 sm:space-x-4 md:space-x-6 overflow-x-auto scrollbar-hide pb-4 sm:pb-6 scroll-smooth"
            style={{ scrollSnapType: 'x mandatory' }}
          >
            {items.map((item, index) => (
              <div
                key={`${item.type}-${item.id}`}
                className="flex-none w-40 sm:w-48 md:w-56 lg:w-64 xl:w-72 animate-slide-up"
                style={{
                  scrollSnapAlign: 'start',
                  animationDelay: `${index * 0.1}s`
                }}
              >
                <ContentCard
                  id={item.id}
                  imdbId={item.imdbId}
                  title={item.title}
                  year={item.year}
                  posterUrl={item.posterUrl}
                  seriesPosterUrl={item.seriesPosterUrl}
                  imdbRating={item.imdbRating}
                  description={item.description}
                  type={item.type}
                  season={item.season}
                  episode={item.episode}
                  seriesTitle={item.seriesTitle}
                />
              </div>
            ))}
          </div>

          {/* Premium Fade Gradients */}
          <div className="absolute left-0 top-0 bottom-6 w-16 bg-gradient-to-r from-black via-black/80 to-transparent pointer-events-none z-10" />
          <div className="absolute right-0 top-0 bottom-6 w-16 bg-gradient-to-l from-black via-black/80 to-transparent pointer-events-none z-10" />
        </div>

        {/* Progress Indicator */}
        <div className="flex justify-center mt-6">
          <div className="flex space-x-2">
            {Array.from({ length: Math.ceil(items.length / 4) }).map((_, index) => (
              <div
                key={index}
                className="w-2 h-2 rounded-full bg-white/20 transition-all duration-300"
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContentSection;
