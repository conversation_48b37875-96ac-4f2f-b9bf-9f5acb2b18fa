import mongoose, { Schema, Document } from 'mongoose';

export interface IRequest extends Document {
  imdbIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processedCount: number;
  totalCount: number;
  failedIds: string[];
  errorMessages: string[];
  submittedBy?: string; // IP address or user identifier
  contentType: 'auto' | 'movie' | 'series'; // Content type preference
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

const RequestSchema: Schema = new Schema({
  imdbIds: [{ 
    type: String, 
    required: true 
  }],
  status: { 
    type: String, 
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending',
    index: true 
  },
  processedCount: { 
    type: Number, 
    default: 0 
  },
  totalCount: { 
    type: Number, 
    required: true 
  },
  failedIds: [String],
  errorMessages: [String],
  submittedBy: {
    type: String,
    index: true
  },
  contentType: {
    type: String,
    enum: ['auto', 'movie', 'series'],
    default: 'auto'
  }
}, {
  timestamps: true
});

// Index for querying pending requests
RequestSchema.index({ status: 1, createdAt: 1 });

export default mongoose.models.Request || mongoose.model<IRequest>('Request', RequestSchema);
