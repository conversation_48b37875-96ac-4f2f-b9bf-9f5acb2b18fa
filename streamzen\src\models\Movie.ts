import mongoose, { Schema, Document } from 'mongoose';

export interface IMovie extends Document {
  imdbId: string;
  tmdbId?: string;
  title: string;
  year: number;
  rating?: string; // MPAA rating (R, PG-13, etc.)
  runtime?: string; // e.g., "2h 22m"
  imdbRating?: number;
  imdbVotes?: string; // e.g., "3.1M"
  popularity?: number;
  popularityDelta?: number;
  posterUrl?: string;
  trailerUrl?: string;
  trailerRuntime?: string;
  trailerLikes?: string;
  description?: string;
  genres?: string[];
  director?: string;
  cast?: string[];
  language?: string;
  country?: string;
  embedUrl: string;
  embedUrlTmdb?: string;
  vidsrcUrl?: string; // VidSrc embed URL
  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL
  quality?: string;
  createdAt: Date;
  updatedAt: Date;
}

const MovieSchema: Schema = new Schema({
  imdbId: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  },
  tmdbId: { 
    type: String, 
    index: true 
  },
  title: {
    type: String,
    required: true
  },
  year: { 
    type: Number, 
    required: true,
    index: true 
  },
  rating: String,
  runtime: String,
  imdbRating: { 
    type: Number,
    index: true 
  },
  imdbVotes: String,
  popularity: { 
    type: Number,
    index: true 
  },
  popularityDelta: Number,
  posterUrl: String,
  trailerUrl: String,
  trailerRuntime: String,
  trailerLikes: String,
  description: String,
  genres: [{ 
    type: String,
    index: true 
  }],
  director: String,
  cast: [String],
  language: { 
    type: String,
    index: true 
  },
  country: {
    type: String,
    index: true
  },
  embedUrl: {
    type: String,
    required: true
  },
  embedUrlTmdb: String,
  vidsrcUrl: String, // VidSrc embed URL
  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL
  quality: {
    type: String,
    index: true
  }
}, {
  timestamps: true
});

// Compound indexes for better query performance
MovieSchema.index({ year: -1, imdbRating: -1 });
MovieSchema.index({ genres: 1, year: -1 });
// Removed text index to avoid language override issues
MovieSchema.index({ title: 1 });
MovieSchema.index({ language: 1, country: 1 });

export default mongoose.models.Movie || mongoose.model<IMovie>('Movie', MovieSchema);
