const IMDbScraper = require('./src/lib/scraper.ts').default;

async function testGenreExtraction() {
  console.log('🧪 Testing genre extraction...');
  
  const scraper = IMDbScraper.getInstance();
  
  // Test with a popular movie that should have genres
  const testMovies = [
    'tt0111161', // The Shawshank Redemption
    'tt0068646', // The Godfather
    'tt0468569', // The Dark Knight
  ];

  for (const imdbId of testMovies) {
    try {
      console.log(`\n🔍 Testing movie: ${imdbId}`);
      const movieData = await scraper.scrapeMovie(imdbId);
      
      console.log(`📽️ Title: ${movieData.title}`);
      console.log(`🎭 Genres: [${movieData.genres?.join(', ') || 'none'}]`);
      console.log(`🌐 Language: ${movieData.language || 'none'}`);
      console.log(`🏳️ Country: ${movieData.country || 'none'}`);
      console.log(`👥 Cast: [${movieData.cast?.slice(0, 3).join(', ') || 'none'}]`);
      
    } catch (error) {
      console.error(`❌ Error testing ${imdbId}:`, error.message);
    }
  }
}

testGenreExtraction().catch(console.error);
