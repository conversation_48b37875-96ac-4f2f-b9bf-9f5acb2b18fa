import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Movie from '@/models/Movie';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const type = searchParams.get('type') || 'all'; // all, movies, series, episodes
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        results: [],
        pagination: {
          page: 1,
          limit,
          total: 0,
          pages: 0
        }
      });
    }

    const searchQuery = query.trim();
    const skip = (page - 1) * limit;
    
    // Create search regex for flexible matching
    const searchRegex = new RegExp(searchQuery.split(' ').join('|'), 'i');
    const exactRegex = new RegExp(searchQuery, 'i');
    
    let results: any[] = [];
    let totalCount = 0;

    if (type === 'all' || type === 'movies') {
      // Search movies with scoring
      const moviePipeline = [
        {
          $match: {
            $or: [
              { title: exactRegex },
              { description: exactRegex },
              { genres: { $in: [exactRegex] } },
              { cast: { $in: [exactRegex] } },
              { director: exactRegex },
              { title: searchRegex },
              { description: searchRegex }
            ]
          }
        },
        {
          $addFields: {
            score: {
              $add: [
                // Exact title match gets highest score
                { $cond: [{ $regexMatch: { input: "$title", regex: exactRegex } }, 100, 0] },
                // Title contains search gets high score
                { $cond: [{ $regexMatch: { input: "$title", regex: searchRegex } }, 50, 0] },
                // Genre match gets medium score
                { $cond: [{ $in: [searchRegex, "$genres"] }, 30, 0] },
                // Cast match gets medium score
                { $cond: [{ $in: [searchRegex, "$cast"] }, 25, 0] },
                // Director match gets medium score
                { $cond: [{ $regexMatch: { input: "$director", regex: exactRegex } }, 20, 0] },
                // Description match gets low score
                { $cond: [{ $regexMatch: { input: "$description", regex: searchRegex } }, 10, 0] },
                // IMDb rating bonus (only for numeric ratings)
                { $multiply: [
                  { $cond: [
                    { $and: [
                      { $ne: ["$imdbRating", null] },
                      { $ne: ["$imdbRating", ""] },
                      { $eq: [{ $type: "$imdbRating" }, "number"] }
                    ]},
                    "$imdbRating",
                    0
                  ]},
                  2
                ]}
              ]
            },
            type: { $literal: "movie" }
          }
        },
        {
          $addFields: {
            rating: "$imdbRating", // Use imdbRating as the numeric rating for frontend
            mpaaRating: "$rating"   // Keep original rating as mpaaRating
          }
        },
        { $sort: { score: -1, rating: -1, createdAt: -1 } }
      ];

      if (type === 'movies') {
        const movieCount = await Movie.aggregate([
          ...moviePipeline,
          { $count: "total" }
        ]);
        totalCount = movieCount[0]?.total || 0;
        
        const movies = await Movie.aggregate([
          ...moviePipeline,
          { $skip: skip },
          { $limit: limit }
        ]);
        results = movies;
      } else {
        const movies = await Movie.aggregate([
          ...moviePipeline,
          { $limit: Math.ceil(limit / 3) }
        ]);
        results.push(...movies);
      }
    }

    if (type === 'all' || type === 'series') {
      // Search series with scoring
      const seriesPipeline = [
        {
          $match: {
            $or: [
              { title: exactRegex },
              { description: exactRegex },
              { genres: { $in: [exactRegex] } },
              { cast: { $in: [exactRegex] } },
              { title: searchRegex },
              { description: searchRegex }
            ]
          }
        },
        {
          $addFields: {
            score: {
              $add: [
                // Exact title match gets highest score
                { $cond: [{ $regexMatch: { input: "$title", regex: exactRegex } }, 100, 0] },
                // Title contains search gets high score
                { $cond: [{ $regexMatch: { input: "$title", regex: searchRegex } }, 50, 0] },
                // Genre match gets medium score
                { $cond: [{ $in: [searchRegex, "$genres"] }, 30, 0] },
                // Cast match gets medium score
                { $cond: [{ $in: [searchRegex, "$cast"] }, 25, 0] },
                // Description match gets low score
                { $cond: [{ $regexMatch: { input: "$description", regex: searchRegex } }, 10, 0] },
                // IMDb rating bonus (only for numeric ratings)
                { $multiply: [
                  { $cond: [
                    { $and: [
                      { $ne: ["$imdbRating", null] },
                      { $ne: ["$imdbRating", ""] },
                      { $eq: [{ $type: "$imdbRating" }, "number"] }
                    ]},
                    "$imdbRating",
                    0
                  ]},
                  2
                ]},
                // Total seasons bonus
                { $multiply: [{ $toInt: { $ifNull: ["$totalSeasons", 1] } }, 1] }
              ]
            },
            type: { $literal: "series" }
          }
        },
        {
          $addFields: {
            rating: "$imdbRating", // Use imdbRating as the numeric rating for frontend
            mpaaRating: "$rating"   // Keep original rating as mpaaRating
          }
        },
        { $sort: { score: -1, rating: -1, createdAt: -1 } }
      ];

      if (type === 'series') {
        const seriesCount = await Series.aggregate([
          ...seriesPipeline,
          { $count: "total" }
        ]);
        totalCount = seriesCount[0]?.total || 0;
        
        const series = await Series.aggregate([
          ...seriesPipeline,
          { $skip: skip },
          { $limit: limit }
        ]);
        results = series;
      } else {
        const series = await Series.aggregate([
          ...seriesPipeline,
          { $limit: Math.ceil(limit / 3) }
        ]);
        results.push(...series);
      }
    }

    if (type === 'all' || type === 'episodes') {
      // Search episodes with scoring
      const episodePipeline = [
        {
          $match: {
            $or: [
              { title: exactRegex },
              { description: exactRegex },
              { title: searchRegex },
              { description: searchRegex }
            ]
          }
        },
        {
          $lookup: {
            from: 'series',
            localField: 'seriesImdbId',
            foreignField: 'imdbId',
            as: 'series'
          }
        },
        {
          $addFields: {
            score: {
              $add: [
                // Exact title match gets highest score
                { $cond: [{ $regexMatch: { input: "$title", regex: exactRegex } }, 100, 0] },
                // Title contains search gets high score
                { $cond: [{ $regexMatch: { input: "$title", regex: searchRegex } }, 50, 0] },
                // Description match gets medium score
                { $cond: [{ $regexMatch: { input: "$description", regex: searchRegex } }, 20, 0] },
                // Episode rating bonus (only for numeric ratings)
                { $multiply: [
                  { $cond: [
                    { $and: [
                      { $ne: ["$imdbRating", null] },
                      { $ne: ["$imdbRating", ""] },
                      { $eq: [{ $type: "$imdbRating" }, "number"] }
                    ]},
                    "$imdbRating",
                    0
                  ]},
                  2
                ]},
                // Recent episodes get bonus
                { $cond: [{ $gte: ["$createdAt", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)] }, 10, 0] }
              ]
            },
            type: { $literal: "episode" },
            seriesTitle: { $arrayElemAt: ["$series.title", 0] },
            seriesPoster: { $arrayElemAt: ["$series.posterUrl", 0] },
            rating: "$imdbRating", // Use imdbRating as the numeric rating for frontend
            mpaaRating: "$rating"   // Keep original rating as mpaaRating
          }
        },
        { $sort: { score: -1, rating: -1, createdAt: -1 } }
      ];

      if (type === 'episodes') {
        const episodeCount = await Episode.aggregate([
          ...episodePipeline,
          { $count: "total" }
        ]);
        totalCount = episodeCount[0]?.total || 0;
        
        const episodes = await Episode.aggregate([
          ...episodePipeline,
          { $skip: skip },
          { $limit: limit }
        ]);
        results = episodes;
      } else {
        const episodes = await Episode.aggregate([
          ...episodePipeline,
          { $limit: Math.ceil(limit / 3) }
        ]);
        results.push(...episodes);
      }
    }

    // For 'all' type, sort combined results by score
    if (type === 'all') {
      results.sort((a, b) => {
        if (b.score !== a.score) return b.score - a.score;
        if (b.rating !== a.rating) return (b.rating || 0) - (a.rating || 0);
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
      
      totalCount = results.length;
      results = results.slice(skip, skip + limit);
    }

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      results,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: totalPages
      },
      query: searchQuery
    });

  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Search failed' },
      { status: 500 }
    );
  }
}
