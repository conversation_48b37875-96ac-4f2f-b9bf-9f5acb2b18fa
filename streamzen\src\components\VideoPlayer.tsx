'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Play, RotateCcw, Settings, Maximize, Volume2, VolumeX, SkipBack, Skip<PERSON>or<PERSON>, Loader, Wifi, WifiOff, Star, Share2, Download, Bookmark } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Button from './ui/Button';
import { cn } from '@/lib/utils';

interface StreamingSource {
  source: string;
  name: string;
  url: string;
  quality: string;
  priority: number;
}

interface VideoPlayerProps {
  streamingSources: StreamingSource[];
  title: string;
  type: 'movie' | 'series' | 'episode';
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ streamingSources = [], title, type }) => {
  const router = useRouter();
  const [currentSourceIndex, setCurrentSourceIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showSourceSelector, setShowSourceSelector] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline' | 'slow'>('online');
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [showShareMenu, setShowShareMenu] = useState(false);

  const playerRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  const currentSource = streamingSources[currentSourceIndex];

  // If no streaming sources available, show error
  if (!streamingSources || streamingSources.length === 0) {
    return (
      <div className="relative bg-black h-[70vh] flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No Streaming Sources Available</h2>
          <p className="text-gray-400 mb-6">Unable to load streaming sources for this content.</p>
          <Button onClick={() => router.back()} variant="primary">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const goBack = () => {
    router.back();
  };

  const switchSource = (index: number) => {
    setIsLoading(true);
    setCurrentSourceIndex(index);
    // Reset loading state after iframe loads
    setTimeout(() => setIsLoading(false), 2000);
  };

  const reloadCurrentSource = () => {
    setIsLoading(true);
    setLoadingProgress(0);
    // Force iframe reload by changing key
    const iframe = iframeRef.current;
    if (iframe) {
      iframe.src = iframe.src;
    }
    setTimeout(() => setIsLoading(false), 2000);
  };

  // Enhanced functionality
  useEffect(() => {
    // Auto-hide controls after 3 seconds of inactivity
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      setShowControls(true);
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    };

    resetControlsTimeout();
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, []);

  // Loading progress simulation
  useEffect(() => {
    if (isLoading) {
      setLoadingProgress(0);
      const interval = setInterval(() => {
        setLoadingProgress(prev => {
          if (prev >= 90) {
            clearInterval(interval);
            return prev;
          }
          return prev + Math.random() * 15;
        });
      }, 200);

      return () => clearInterval(interval);
    }
  }, [isLoading]);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      playerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleMouseMove = () => {
    setShowControls(true);
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  };

  const shareContent = () => {
    if (navigator.share) {
      navigator.share({
        title: title,
        url: window.location.href
      });
    } else {
      setShowShareMenu(true);
    }
  };

  return (
    <div
      ref={playerRef}
      className="relative bg-gradient-to-br from-black via-gray-900 to-black min-h-screen overflow-hidden"
      onMouseMove={handleMouseMove}
    >
      {/* Animated Background Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute -top-40 -left-40 w-80 h-80 bg-red-600/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute -bottom-40 -right-40 w-80 h-80 bg-blue-600/5 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.1, 0.05, 0.1]
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      {/* Enhanced Header Controls - Mobile Optimized */}
      <div className="relative w-full bg-gradient-to-b from-black/80 via-black/40 to-transparent backdrop-blur-sm">
            <div className="max-w-[2560px] mx-auto px-4 sm:px-6 lg:px-12 py-3 sm:py-4 md:py-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 sm:space-x-4 md:space-x-6">
                  <motion.button
                    onClick={goBack}
                    className="flex items-center space-x-2 sm:space-x-3 px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-lg sm:rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ArrowLeft size={16} className="sm:w-5 sm:h-5" />
                    <span className="font-medium text-sm sm:text-base">Back</span>
                  </motion.button>
                  <div className="flex flex-col min-w-0 flex-1">
                    <h1 className="text-white text-lg sm:text-xl md:text-2xl font-bold truncate max-w-full">
                      {title}
                    </h1>
                    <div className="flex items-center space-x-2 sm:space-x-3 text-sm">
                      <p className="text-gray-400 text-xs sm:text-sm">Now Playing</p>
                      <div className="flex items-center space-x-1">
                        {connectionStatus === 'online' ? (
                          <Wifi size={12} className="sm:w-[14px] sm:h-[14px] text-green-400" />
                        ) : connectionStatus === 'slow' ? (
                          <Wifi size={12} className="sm:w-[14px] sm:h-[14px] text-yellow-400" />
                        ) : (
                          <WifiOff size={12} className="sm:w-[14px] sm:h-[14px] text-red-400" />
                        )}
                        <span className={cn(
                          "text-xs font-medium",
                          connectionStatus === 'online' ? 'text-green-400' :
                          connectionStatus === 'slow' ? 'text-yellow-400' : 'text-red-400'
                        )}>
                          {connectionStatus === 'online' ? 'HD' : connectionStatus === 'slow' ? 'SD' : 'Offline'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 sm:space-x-3">
                  {/* Source Selector - Mobile Optimized */}
                  <div className="relative">
                    <motion.button
                      onClick={() => setShowSourceSelector(!showSourceSelector)}
                      className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-lg sm:rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Settings size={14} className="sm:w-[18px] sm:h-[18px]" />
                      <span className="font-medium text-xs sm:text-sm">Source {currentSourceIndex + 1}</span>
                    </motion.button>

                    <AnimatePresence>
                      {showSourceSelector && (
                        <motion.div
                          initial={{ opacity: 0, y: 10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 0.95 }}
                          className="absolute top-full right-0 mt-2 w-64 bg-black/90 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl overflow-hidden z-60"
                        >
                          <div className="p-3 border-b border-white/10">
                            <h3 className="text-white font-semibold">Streaming Sources</h3>
                          </div>
                          <div className="max-h-64 overflow-y-auto">
                            {streamingSources.map((source, index) => (
                              <motion.button
                                key={index}
                                onClick={() => switchSource(index)}
                                className={cn(
                                  "w-full flex items-center justify-between px-4 py-3 text-left transition-all duration-200",
                                  index === currentSourceIndex
                                    ? "bg-red-500/20 text-white border-l-2 border-red-500"
                                    : "text-gray-300 hover:text-white hover:bg-white/10"
                                )}
                                whileHover={{ x: 4 }}
                              >
                                <div>
                                  <span className="font-medium">{source.name}</span>
                                  <p className="text-xs text-gray-400">{source.quality}</p>
                                </div>
                                {index === currentSourceIndex && (
                                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                                )}
                              </motion.button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* Action Buttons */}
                  <motion.button
                    onClick={shareContent}
                    className="p-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Share2 size={18} />
                  </motion.button>

                  <motion.button
                    onClick={reloadCurrentSource}
                    className="flex items-center space-x-2 px-4 py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <RotateCcw size={18} />
                    <span className="font-medium">Reload</span>
                  </motion.button>
                </div>
              </div>
        </div>
      </div>

      {/* Enhanced Video Player Container */}
      <div className="relative w-full h-[75vh] lg:h-[85vh] mx-auto max-w-[2560px] px-6 lg:px-12">
        <motion.div
          className="relative w-full h-full bg-black rounded-3xl overflow-hidden shadow-2xl border border-white/20"
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Enhanced Loading Overlay */}
          <AnimatePresence>
            {isLoading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-gradient-to-br from-black/90 via-black/80 to-black/90 backdrop-blur-sm flex items-center justify-center z-30"
              >
                <div className="text-center text-white">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="w-20 h-20 border-4 border-gray-600 border-t-red-500 rounded-full mx-auto mb-6"
                  />
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <p className="text-xl font-semibold mb-2">Loading {currentSource?.name}...</p>
                    <p className="text-sm text-gray-400 mb-4">Quality: {currentSource?.quality}</p>

                    {/* Loading Progress Bar */}
                    <div className="w-64 h-2 bg-gray-700 rounded-full overflow-hidden mx-auto">
                      <motion.div
                        className="h-full bg-gradient-to-r from-red-500 to-red-600 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${loadingProgress}%` }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">{Math.round(loadingProgress)}% loaded</p>
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Video iframe */}
          {currentSource && (
            <iframe
              ref={iframeRef}
              key={`${currentSource.source}-${currentSourceIndex}`}
              src={currentSource.url}
              className="w-full h-full border-0"
              allowFullScreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              title={title}
              onLoad={() => {
                setIsLoading(false);
                setLoadingProgress(100);
              }}
            />
          )}


        </motion.div>
      </div>

      {/* Premium Source Selector */}
      <div className="glass border-t border-white/5">
        <div className="max-w-[1920px] mx-auto px-6 lg:px-12 py-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <h3 className="text-white text-2xl font-bold">Streaming Sources</h3>
              <div className="glass px-4 py-2 rounded-full">
                <span className="text-gray-300 text-sm font-medium">
                  {streamingSources.length} Available
                </span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-3 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                <span className="text-white font-medium">
                  {currentSource?.name} • {currentSource?.quality}
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            {streamingSources.map((source, index) => (
              <button
                key={source.source}
                onClick={() => switchSource(index)}
                className={cn(
                  'group relative p-6 rounded-2xl border transition-all duration-300 text-left hover:scale-105 focus-ring',
                  index === currentSourceIndex
                    ? 'bg-gradient-to-br from-blue-600 to-blue-700 border-blue-500 text-white shadow-2xl'
                    : 'glass-elevated border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20'
                )}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300',
                    index === currentSourceIndex
                      ? 'bg-white/20'
                      : 'bg-white/10 group-hover:bg-white/20'
                  )}>
                    <Play size={16} className={index === currentSourceIndex ? 'text-white' : 'text-gray-400'} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-sm">{source.name}</h4>
                    <p className="text-xs opacity-75">{source.quality}</p>
                  </div>
                </div>

                {/* Priority indicator */}
                <div className="flex items-center justify-between">
                  <div className="flex space-x-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div
                        key={i}
                        className={cn(
                          'w-1.5 h-1.5 rounded-full',
                          i < (6 - source.priority)
                            ? (index === currentSourceIndex ? 'bg-white/60' : 'bg-blue-400/60')
                            : 'bg-white/20'
                        )}
                      />
                    ))}
                  </div>
                  {index === currentSourceIndex && (
                    <div className="text-xs font-medium bg-white/20 px-2 py-1 rounded-full">
                      Active
                    </div>
                  )}
                </div>

                {/* Glow effect for active source */}
                {index === currentSourceIndex && (
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/50 to-purple-500/50 rounded-2xl blur-xl -z-10" />
                )}
              </button>
            ))}
          </div>

          <div className="mt-8 text-center">
            <div className="glass-elevated inline-flex items-center space-x-3 px-6 py-4 rounded-2xl">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
              <p className="text-gray-300 text-sm font-medium">
                Premium sources are automatically optimized for the best viewing experience
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
