'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Play, Search, Star, ChevronLeft, ChevronRight, Film, Tv, 
  Calendar, Clock, TrendingUp, Fire, Zap, ArrowRight
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface HeroItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series';
}

interface PracticalHeroSectionProps {
  items: HeroItem[];
}

const PracticalHeroSection: React.FC<PracticalHeroSectionProps> = ({ items }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % items.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, items.length]);

  const currentItem = items[currentIndex];
  const watchHref = currentItem?.type === 'movie'
    ? `/watch/movie/${currentItem.imdbId}`
    : `/watch/series/${currentItem.imdbId}`;

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 8000);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 8000);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  if (!currentItem || items.length === 0) {
    return (
      <div className="relative h-[60vh] bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Film className="w-12 h-12 text-white/30 mx-auto mb-4 animate-pulse" />
          <h2 className="text-xl font-bold text-white/50">Loading featured content...</h2>
        </div>
      </div>
    );
  }

  return (
    <section className="relative h-[70vh] overflow-hidden bg-black">
      {/* Background Image */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          className="absolute inset-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Image
            src={getImageUrl(currentItem.posterUrl)}
            alt={currentItem.title}
            fill
            className="object-cover object-center"
            priority
            sizes="100vw"
          />
          
          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/60 to-black/30" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40" />
        </motion.div>
      </AnimatePresence>

      {/* Main Content - Mobile Optimized */}
      <div className="relative h-full flex items-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-12 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 items-center">
            
            {/* Left Content - Mobile Optimized */}
            <div className="space-y-4 sm:space-y-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30 }}
                  transition={{ duration: 0.6 }}
                  className="space-y-4 sm:space-y-6"
                >
                  {/* Badge - Mobile Optimized */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                    <div className="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-3 py-1 bg-red-500/20 border border-red-500/30 rounded-md sm:rounded-lg backdrop-blur-sm">
                      {currentItem.type === 'movie' ? (
                        <Film className="w-3 h-3 sm:w-4 sm:h-4 text-red-400" />
                      ) : (
                        <Tv className="w-3 h-3 sm:w-4 sm:h-4 text-red-400" />
                      )}
                      <span className="text-red-300 font-semibold text-xs sm:text-sm uppercase">
                        {currentItem.type === 'movie' ? 'Featured Movie' : 'Featured Series'}
                      </span>
                    </div>

                    {currentItem.imdbRating && (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded-md sm:rounded-lg backdrop-blur-sm">
                        <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-current" />
                        <span className="text-yellow-300 font-bold text-xs sm:text-sm">
                          {formatRating(currentItem.imdbRating)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Title - Mobile Optimized */}
                  <div className="space-y-1 sm:space-y-2">
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-white leading-tight tracking-tight">
                      {currentItem.title}
                    </h1>
                    {currentItem.year && (
                      <div className="flex items-center space-x-2 sm:space-x-3 text-sm sm:text-base md:text-lg text-gray-300">
                        <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-red-400" />
                        <span className="font-semibold">{currentItem.year}</span>
                        <span className="text-gray-500">•</span>
                        <span className="text-gray-400">HD Quality</span>
                      </div>
                    )}
                  </div>

                  {/* Description - Mobile Optimized */}
                  {currentItem.description && (
                    <p className="text-sm sm:text-base md:text-lg text-gray-300 leading-relaxed max-w-full sm:max-w-xl">
                      {truncateText(currentItem.description, 120)}
                    </p>
                  )}

                  {/* Action Buttons - Mobile Optimized */}
                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 pt-2">
                    <Link href={watchHref} className="w-full sm:w-auto">
                      <motion.button
                        className="flex items-center justify-center space-x-2 sm:space-x-3 px-4 sm:px-6 py-3 bg-red-500 hover:bg-red-600 text-white font-bold rounded-lg sm:rounded-xl shadow-lg transition-all duration-300 w-full sm:w-auto"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Play className="w-4 h-4 sm:w-5 sm:h-5 fill-current" />
                        <span className="text-sm sm:text-base">Watch Now</span>
                      </motion.button>
                    </Link>

                    <Link href={currentItem.type === 'movie' ? '/movies' : '/series'} className="w-full sm:w-auto">
                      <motion.button
                        className="flex items-center justify-center space-x-2 px-4 sm:px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-lg sm:rounded-xl border border-white/20 backdrop-blur-sm transition-all duration-300 w-full sm:w-auto"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span className="text-sm sm:text-base">Browse More</span>
                        <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
                      </motion.button>
                    </Link>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Right Content - Search & Quick Actions - Mobile Optimized */}
            <div className="space-y-4 sm:space-y-6 mt-8 lg:mt-0">
              {/* Search Bar */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
                className="bg-black/40 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
                  <Search className="w-5 h-5 text-red-400" />
                  <span>Find Your Next Watch</span>
                </h3>
                
                <form onSubmit={handleSearch} className="space-y-4">
                  <div className="relative">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search movies, series, episodes..."
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-red-500/50 focus:bg-white/20 transition-all duration-300"
                    />
                    <button
                      type="submit"
                      className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-red-500 hover:bg-red-600 rounded-lg transition-colors duration-300"
                    >
                      <Search className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </form>

                {/* Quick Links */}
                <div className="mt-4 space-y-2">
                  <p className="text-sm text-gray-400">Quick Browse:</p>
                  <div className="flex flex-wrap gap-2">
                    <Link href="/movies" className="px-3 py-1 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-colors duration-300">
                      Movies
                    </Link>
                    <Link href="/series" className="px-3 py-1 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-colors duration-300">
                      Series
                    </Link>
                    <Link href="/episodes" className="px-3 py-1 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-colors duration-300">
                      Latest Episodes
                    </Link>
                  </div>
                </div>
              </motion.div>

              {/* Trending Badge */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
                className="bg-gradient-to-r from-red-500/20 to-orange-500/20 backdrop-blur-xl rounded-2xl border border-red-500/30 p-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-red-500/30 rounded-lg">
                    <TrendingUp className="w-5 h-5 text-red-400" />
                  </div>
                  <div>
                    <h4 className="text-white font-bold">Trending Now</h4>
                    <p className="text-gray-300 text-sm">200K+ movies & series available</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      {items.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/40 backdrop-blur-xl border border-white/20 rounded-xl flex items-center justify-center text-white hover:bg-black/60 transition-all duration-300 z-30"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/40 backdrop-blur-xl border border-white/20 rounded-xl flex items-center justify-center text-white hover:bg-black/60 transition-all duration-300 z-30"
          >
            <ChevronRight className="w-5 h-5" />
          </button>

          {/* Slide Indicators */}
          <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-30">
            <div className="flex items-center space-x-2 px-4 py-2 bg-black/40 backdrop-blur-xl rounded-xl border border-white/20">
              {items.map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setCurrentIndex(index);
                    setIsAutoPlaying(false);
                    setTimeout(() => setIsAutoPlaying(true), 8000);
                  }}
                  className={cn(
                    'transition-all duration-300',
                    index === currentIndex
                      ? 'w-6 h-2 bg-red-500 rounded-full'
                      : 'w-2 h-2 bg-gray-500 rounded-full hover:bg-gray-400'
                  )}
                />
              ))}
            </div>
          </div>
        </>
      )}

      {/* Auto-play Progress Bar */}
      {isAutoPlaying && items.length > 1 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/30 z-20">
          <motion.div
            className="h-full bg-red-500"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 5, ease: "linear" }}
            key={currentIndex}
          />
        </div>
      )}
    </section>
  );
};

export default PracticalHeroSection;
