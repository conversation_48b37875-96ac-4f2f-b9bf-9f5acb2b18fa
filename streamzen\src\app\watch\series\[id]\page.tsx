import { notFound } from 'next/navigation';
import { Metada<PERSON> } from 'next';
import { apiClient } from '@/lib/api';
import VideoPlayer from '@/components/VideoPlayer';
import ContentInfo from '@/components/ContentInfo';
import EpisodeSelector from '@/components/EpisodeSelector';
import VidSrcAPI from '@/lib/vidsrc';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface SeriesWatchPageProps {
  params: Promise<{
    id: string;
  }>;
  searchParams: Promise<{
    season?: string;
    episode?: string;
  }>;
}

async function getSeriesData(id: string, season?: number) {
  try {
    const [series, episodes] = await Promise.all([
      apiClient.getSeriesById(id),
      apiClient.getSeriesEpisodes(id, season)
    ]);
    return { series, episodes };
  } catch (error) {
    // Only log error if it's not a 404 (which is expected for non-existent content)
    if (!error.message?.includes('404')) {
      console.error('Error fetching series data:', error);
    }
    return { series: null, episodes: [] };
  }
}

// Episode syncing on series page visit - optimized to only sync when episodes are missing
// This provides immediate episode availability when users visit series pages

async function getSeriesDataWithSync(id: string, season?: number) {
  try {
    // First, get the basic series data
    const series = await apiClient.getSeriesById(id);

    if (!series) {
      return { series: null, episodes: [] };
    }

    // Get ALL episodes for this series from embedded episodes array
    // This ensures we have data for all seasons for the episode selector
    const allEpisodes = series.episodes || [];

    console.log(`📊 Fetched ${allEpisodes.length} episodes for series ${series.title} across all seasons`);

    // OPTIMIZATION: Only trigger episode sync if we have very few or no episodes
    // This avoids unnecessary syncing for series that already have complete episode data
    if (allEpisodes.length < 5) { // Threshold: sync if less than 5 episodes (likely incomplete)
      console.log(`🔄 Series ${series.title} has only ${allEpisodes.length} episodes, triggering embedded episode sync...`);

      try {
        // Trigger ULTRA-FAST embedded episode sync for this series (non-blocking)
        fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/series/${id}/episodes/sync-embedded`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        }).then(async (response) => {
          if (response.ok) {
            const result = await response.json();
            console.log(`⚡ Ultra-fast embedded episode sync completed in ${result.stats?.duration}: ${result.stats?.newEpisodesAdded || 0} new episodes added`);
          } else {
            console.error('❌ Ultra-fast embedded episode sync failed:', response.statusText);
          }
        }).catch((syncError) => {
          console.error('❌ Error in ultra-fast embedded episode sync:', syncError);
        });
      } catch (syncError) {
        console.error('❌ Error triggering ultra-fast embedded episode sync:', syncError);
      }
    } else {
      console.log(`✅ Series ${series.title} has sufficient episodes (${allEpisodes.length}), skipping sync`);
    }

    // Log season breakdown for debugging
    const seasonCounts = allEpisodes.reduce((acc, ep) => {
      acc[ep.season] = (acc[ep.season] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    console.log('📺 Season breakdown:', Object.entries(seasonCounts).map(([season, count]) =>
      `Season ${season}: ${count} episodes`
    ).join(', '));

    return { series, episodes: allEpisodes };
  } catch (error) {
    // Only log error if it's not a 404 (which is expected for non-existent content)
    if (!error.message?.includes('404')) {
      console.error('Error fetching series data:', error);
    }
    return { series: null, episodes: [] };
  }
}

export async function generateMetadata({ params, searchParams }: SeriesWatchPageProps): Promise<Metadata> {
  const { id } = await params;
  const { season, episode } = await searchParams;
  const selectedSeason = season ? parseInt(season) : 1;
  const selectedEpisode = episode ? parseInt(episode) : 1;

  const { series, episodes } = await getSeriesDataWithSync(id);

  if (!series) {
    return {
      title: 'Series Not Found | freeMoviesWatchNow',
      description: 'The requested series could not be found.',
    };
  }

  // If specific episode is requested, generate episode metadata
  if (season && episode) {
    const currentEpisode = episodes.find(
      ep => ep.season === selectedSeason && ep.episode === selectedEpisode
    );

    if (currentEpisode) {
      return SEOGenerator.generateEpisodeMetadata(currentEpisode, series);
    }
  }

  // Otherwise generate series metadata
  return SEOGenerator.generateSeriesMetadata(series);
}

export default async function SeriesWatchPage({ params, searchParams }: SeriesWatchPageProps) {
  const { id } = await params;
  const { season, episode } = await searchParams;
  const selectedSeason = season ? parseInt(season) : 1;
  const selectedEpisode = episode ? parseInt(episode) : 1;

  const { series, episodes } = await getSeriesDataWithSync(id);

  if (!series) {
    notFound();
  }

  // Find the current episode
  const currentEpisode = episodes.find(
    ep => ep.season === selectedSeason && ep.episode === selectedEpisode
  );

  const contentInfo = {
    title: series.title,
    year: series.startYear,
    rating: series.rating,
    imdbRating: series.imdbRating,
    description: series.description,
    genres: series.genres,
    creator: series.creator,
    cast: series.cast,
    language: series.language,
    country: series.country,
    posterUrl: series.posterUrl,
    type: 'series' as const,
    totalSeasons: series.totalSeasons,
    status: series.status
  };

  // Generate all streaming sources for the current episode
  const vidsrc = VidSrcAPI.getInstance();
  const streamingSources = vidsrc.generateAllEpisodeEmbedUrls(id, selectedSeason, selectedEpisode, series.tmdbId);

  // Generate structured data
  const isEpisodeView = season && episode && currentEpisode;
  const schema = isEpisodeView
    ? SchemaGenerator.generateEpisodeSchema(currentEpisode, series)
    : SchemaGenerator.generateSeriesSchema(series);

  const breadcrumbSchema = SchemaGenerator.generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'TV Series', url: '/series' },
    { name: series.title, url: `/watch/series/${series.imdbId}` },
    ...(isEpisodeView ? [{
      name: `S${selectedSeason}E${selectedEpisode}`,
      url: `/watch/series/${series.imdbId}?season=${selectedSeason}&episode=${selectedEpisode}`
    }] : [])
  ]);

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            schema,
            breadcrumbSchema
          ])
        }}
      />
      <VideoPlayer
        streamingSources={streamingSources}
        title={`${series.title} - S${selectedSeason}E${selectedEpisode}`}
        type="series"
      />

      <div className="max-w-[2560px] mx-auto px-8 lg:px-24 py-12">
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-16">
          {/* Content Info */}
          <div className="xl:col-span-3">
            <ContentInfo content={contentInfo} />
          </div>

          {/* Episode Selector */}
          <div className="xl:col-span-2">
            <EpisodeSelector
              seriesId={id}
              episodes={episodes}
              currentSeason={selectedSeason}
              currentEpisode={selectedEpisode}
            />
          </div>
        </div>

        {/* SEO Blog-Style Content */}
        <div className="mt-16 max-w-4xl mx-auto">
          <article className="prose prose-invert prose-lg max-w-none">
            {isEpisodeView && currentEpisode ? (
              <>
                <h2 className="text-3xl font-bold text-white mb-6">
                  {series.title} Season {selectedSeason} Episode {selectedEpisode}
                  {currentEpisode.episodeTitle && ` - ${currentEpisode.episodeTitle}`}
                </h2>

                <div className="text-gray-300 leading-relaxed space-y-4">
                  <p>
                    Watch <strong>{series.title}</strong> Season {selectedSeason} Episode {selectedEpisode}
                    {currentEpisode.episodeTitle && ` "${currentEpisode.episodeTitle}"`} online free in HD quality.
                    {currentEpisode.description && ` ${currentEpisode.description}`}
                  </p>

                  {currentEpisode.airDate && (
                    <p>
                      This episode originally aired on <strong>{new Date(currentEpisode.airDate).toLocaleDateString()}</strong>.
                    </p>
                  )}

                  <p>
                    Continue following the story of <strong>{series.title}</strong>, a captivating {series.genres?.join(', ').toLowerCase()} series
                    that has been entertaining audiences since {series.startYear}.
                  </p>
                </div>
              </>
            ) : (
              <>
                <h2 className="text-3xl font-bold text-white mb-6">
                  About {series.title} ({series.startYear}{series.endYear ? `-${series.endYear}` : ''})
                </h2>

                <div className="text-gray-300 leading-relaxed space-y-4">
                  <p>
                    <strong>{series.title}</strong> is a {series.genres?.join(', ').toLowerCase()} series that premiered in {series.startYear}.
                    {series.description && ` ${series.description}`}
                  </p>

                  {series.creator && (
                    <p>
                      Created by <strong>{series.creator}</strong>, this series has captivated audiences with its compelling storylines and character development.
                    </p>
                  )}

                  {series.cast && series.cast.length > 0 && (
                    <p>
                      The series features an exceptional cast including <strong>{series.cast.slice(0, 5).join(', ')}</strong>
                      {series.cast.length > 5 && ' and many more talented actors'}.
                    </p>
                  )}

                  {series.totalSeasons && (
                    <p>
                      With <strong>{series.totalSeasons} season{series.totalSeasons > 1 ? 's' : ''}</strong> available,
                      {series.title} offers hours of premium entertainment for binge-watching.
                    </p>
                  )}

                  {series.imdbRating && (
                    <p>
                      Rated <strong>{series.imdbRating}/10</strong> on IMDb, {series.title} has received
                      {series.imdbRating >= 8 ? ' critical acclaim' : series.imdbRating >= 7 ? ' positive reviews' : ' mixed reviews'}
                      from viewers worldwide.
                    </p>
                  )}
                </div>
              </>
            )}

            <p className="text-gray-300 leading-relaxed mt-6">
              Watch <strong>{series.title}</strong> online free in HD quality on freeMoviesWatchNow. Our platform provides
              multiple streaming sources and all episodes for the ultimate binge-watching experience.
            </p>

            <div className="mt-8 p-6 bg-gray-900/50 rounded-lg border border-gray-800">
              <h3 className="text-xl font-semibold text-white mb-4">Why Choose freeMoviesWatchNow for TV Series?</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• All episodes available in HD quality</li>
                <li>• Multiple streaming sources for reliability</li>
                <li>• Easy episode navigation and season selection</li>
                <li>• No ads interrupting your viewing experience</li>
                <li>• Compatible with all devices and browsers</li>
                <li>• Latest episodes added as soon as they air</li>
              </ul>
            </div>
          </article>
        </div>
      </div>
    </div>
  );
}
