const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

async function optimizeMovieIndexes() {
  console.log('🚀 Optimizing movie database indexes for 15,461+ movies...');

  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI;
    console.log('🔗 Connecting to MongoDB...');

    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB successfully');

    const db = mongoose.connection.db;

    // Check current collection stats
    console.log('📊 Checking collection stats...');
    const movieStats = await db.collection('movies').stats();
    console.log(`📈 Movies collection: ${movieStats.count} documents, ${Math.round(movieStats.size / 1024 / 1024)}MB`);

    // List existing indexes
    console.log('📋 Current movie indexes:');
    const movieIndexes = await db.collection('movies').indexes();
    movieIndexes.forEach(idx => {
      console.log(`  - ${idx.name}: ${JSON.stringify(idx.key)}`);
    });

    // Create optimized indexes for large dataset
    console.log('🔧 Creating optimized indexes for large movie dataset...');

    // 1. Primary sorting indexes (most important for pagination)
    await db.collection('movies').createIndex(
      { createdAt: -1 }, 
      { name: 'createdAt_desc', background: true }
    );
    console.log('✅ Created createdAt descending index');

    await db.collection('movies').createIndex(
      { year: -1 }, 
      { name: 'year_desc', background: true }
    );
    console.log('✅ Created year descending index');

    await db.collection('movies').createIndex(
      { imdbRating: -1 }, 
      { name: 'imdbRating_desc', background: true }
    );
    console.log('✅ Created imdbRating descending index');

    // 2. Filter indexes
    await db.collection('movies').createIndex(
      { genres: 1 }, 
      { name: 'genres_1', background: true }
    );
    console.log('✅ Created genres index');

    await db.collection('movies').createIndex(
      { language: 1 }, 
      { name: 'language_1', background: true }
    );
    console.log('✅ Created language index');

    await db.collection('movies').createIndex(
      { country: 1 }, 
      { name: 'country_1', background: true }
    );
    console.log('✅ Created country index');

    await db.collection('movies').createIndex(
      { rating: 1 }, 
      { name: 'rating_1', background: true }
    );
    console.log('✅ Created rating index');

    // 3. Compound indexes for common filter combinations
    await db.collection('movies').createIndex(
      { genres: 1, createdAt: -1 }, 
      { name: 'genres_createdAt_desc', background: true }
    );
    console.log('✅ Created genres + createdAt compound index');

    await db.collection('movies').createIndex(
      { language: 1, createdAt: -1 }, 
      { name: 'language_createdAt_desc', background: true }
    );
    console.log('✅ Created language + createdAt compound index');

    await db.collection('movies').createIndex(
      { year: 1, createdAt: -1 }, 
      { name: 'year_createdAt_desc', background: true }
    );
    console.log('✅ Created year + createdAt compound index');

    // 4. Search indexes
    await db.collection('movies').createIndex(
      { title: 1 }, 
      { name: 'title_1', background: true }
    );
    console.log('✅ Created title index');

    // 5. Unique constraint on imdbId
    await db.collection('movies').createIndex(
      { imdbId: 1 }, 
      { name: 'imdbId_unique', unique: true, background: true }
    );
    console.log('✅ Created unique imdbId index');

    // Check final indexes
    console.log('📋 Final movie indexes:');
    const finalIndexes = await db.collection('movies').indexes();
    finalIndexes.forEach(idx => {
      console.log(`  - ${idx.name}: ${JSON.stringify(idx.key)}`);
    });

    console.log('🎉 Movie database optimization completed!');
    console.log('📈 Performance improvements:');
    console.log('  - Faster pagination with createdAt, year, imdbRating sorting');
    console.log('  - Efficient filtering by genres, language, country, rating');
    console.log('  - Optimized compound queries for common filter combinations');
    console.log('  - Fast title-based searches');
    console.log('  - Unique constraint prevents duplicate movies');

  } catch (error) {
    console.error('❌ Error optimizing indexes:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    try {
      await mongoose.disconnect();
      console.log('🔌 Disconnected from MongoDB');
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
    process.exit(0);
  }
}

console.log('Starting movie database optimization...');
optimizeMovieIndexes().catch(console.error);
