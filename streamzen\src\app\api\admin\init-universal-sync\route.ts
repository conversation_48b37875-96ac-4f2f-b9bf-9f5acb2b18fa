import { NextRequest, NextResponse } from 'next/server';
import UniversalSyncService from '@/lib/universalSyncService';

/**
 * Initialize the Universal Sync Service
 * This endpoint should be called once when the application starts
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🌍 Initializing Universal Sync Service...');
    
    const universalSync = UniversalSyncService.getInstance();
    await universalSync.initialize();
    
    // Get current status
    const status = await universalSync.getSyncStatus();
    const utcNow = new Date().toISOString();
    
    console.log('✅ Universal Sync Service initialized successfully');
    
    return NextResponse.json({
      success: true,
      message: 'Universal Sync Service initialized successfully',
      utcTime: utcNow,
      status: {
        isRunning: status?.isRunning || false,
        lastSyncTime: status?.lastSyncTime || null,
        nextSyncTime: status?.nextSyncTime || null,
        syncType: status?.syncType || 'UNIVERSAL_SYNC'
      }
    });

  } catch (error) {
    console.error('❌ Failed to initialize Universal Sync Service:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to initialize Universal Sync Service',
        message: error.message,
        utcTime: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * POST method for manual initialization
 */
export async function POST(request: NextRequest) {
  return GET(request);
}
