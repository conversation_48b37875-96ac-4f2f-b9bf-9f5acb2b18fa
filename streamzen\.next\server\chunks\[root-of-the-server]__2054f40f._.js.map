{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Series.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\n// Episode interface for embedded episodes within series\nexport interface IEpisode {\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  description?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  posterUrl?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl: string;\n  vidsrcTmdbUrl?: string;\n  isLatestRelease?: boolean; // ✅ CRITICAL: VidSrc latest release flag\n  createdAt?: Date;\n  updatedAt?: Date;\n}\n\nexport interface ISeries extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string; // MPAA rating\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string; // 'ongoing', 'ended', 'cancelled'\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection\n  episodes: IEpisode[];\n  episodeCount?: number; // Cache for quick access\n  lastEpisodeUpdate?: Date; // Track when episodes were last synced\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst SeriesSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: {\n    type: String,\n    required: true\n  },\n  startYear: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  endYear: { \n    type: Number,\n    index: true \n  },\n  rating: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  creator: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  totalSeasons: Number,\n  status: { \n    type: String,\n    enum: ['ongoing', 'ended', 'cancelled'],\n    index: true \n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection\n  episodes: [{\n    season: { type: Number, required: true },\n    episode: { type: Number, required: true },\n    episodeTitle: String,\n    description: String,\n    airDate: Date,\n    runtime: { type: String, default: '45 min' },\n    imdbRating: Number,\n    posterUrl: String,\n    embedUrl: { type: String, required: true },\n    embedUrlTmdb: String,\n    vidsrcUrl: { type: String, required: true },\n    vidsrcTmdbUrl: String,\n    isLatestRelease: { type: Boolean, default: false, index: true }, // ✅ CRITICAL: VidSrc latest release flag\n    createdAt: { type: Date, default: Date.now },\n    updatedAt: { type: Date, default: Date.now }\n  }],\n  episodeCount: { type: Number, default: 0 }, // Cache for quick access\n  lastEpisodeUpdate: Date // Track when episodes were last synced\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nSeriesSchema.index({ startYear: -1, imdbRating: -1 });\nSeriesSchema.index({ genres: 1, startYear: -1 });\nSeriesSchema.index({ status: 1, startYear: -1 });\n// Removed text index to avoid language override issues\nSeriesSchema.index({ title: 1 });\nSeriesSchema.index({ language: 1, country: 1 });\n// 🆕 EPISODE-SPECIFIC INDEXES for embedded episodes\nSeriesSchema.index({ 'episodes.season': 1, 'episodes.episode': 1 });\nSeriesSchema.index({ 'episodes.isLatestRelease': 1 }); // ✅ CRITICAL: For VidSrc latest episodes queries\nSeriesSchema.index({ lastEpisodeUpdate: -1 }); // For sync tracking\nSeriesSchema.index({ episodeCount: -1 }); // For quick episode count queries\n\nexport default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAsDA,MAAM,eAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;IACR,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,SAAS;IACT,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,cAAc;IACd,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAS;SAAY;QACvC,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,oEAAoE;IACpE,UAAU;QAAC;YACT,QAAQ;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACvC,SAAS;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACxC,cAAc;YACd,aAAa;YACb,SAAS;YACT,SAAS;gBAAE,MAAM;gBAAQ,SAAS;YAAS;YAC3C,YAAY;YACZ,WAAW;YACX,UAAU;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACzC,cAAc;YACd,WAAW;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YAC1C,eAAe;YACf,iBAAiB;gBAAE,MAAM;gBAAS,SAAS;gBAAO,OAAO;YAAK;YAC9D,WAAW;gBAAE,MAAM;gBAAM,SAAS,KAAK,GAAG;YAAC;YAC3C,WAAW;gBAAE,MAAM;gBAAM,SAAS,KAAK,GAAG;YAAC;QAC7C;KAAE;IACF,cAAc;QAAE,MAAM;QAAQ,SAAS;IAAE;IACzC,mBAAmB,KAAK,uCAAuC;AACjE,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,aAAa,KAAK,CAAC;IAAE,WAAW,CAAC;IAAG,YAAY,CAAC;AAAE;AACnD,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,uDAAuD;AACvD,aAAa,KAAK,CAAC;IAAE,OAAO;AAAE;AAC9B,aAAa,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;AAC7C,oDAAoD;AACpD,aAAa,KAAK,CAAC;IAAE,mBAAmB;IAAG,oBAAoB;AAAE;AACjE,aAAa,KAAK,CAAC;IAAE,4BAA4B;AAAE,IAAI,iDAAiD;AACxG,aAAa,KAAK,CAAC;IAAE,mBAAmB,CAAC;AAAE,IAAI,oBAAoB;AACnE,aAAa,KAAK,CAAC;IAAE,cAAc,CAAC;AAAE,IAAI,kCAAkC;uCAE7D,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/debug/test-latest-episode-logic/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { connectDB } from '@/lib/mongodb';\nimport { Series } from '@/models/Series';\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n\n    console.log('🧪 Testing latest episode detection logic...');\n\n    // Find the series from your example (Randall & Hopkirk)\n    const testSeries = await Series.findOne({ imdbId: 'tt0167701' });\n    \n    if (!testSeries) {\n      return NextResponse.json({\n        success: false,\n        message: 'Test series not found'\n      });\n    }\n\n    console.log(`📺 Found test series: ${testSeries.title}`);\n    console.log(`📊 Episodes count: ${testSeries.episodes.length}`);\n\n    // Show current episode states\n    console.log('📝 Current episode states:');\n    testSeries.episodes.forEach((ep, index) => {\n      console.log(`   Episode ${index}: S${ep.season}E${ep.episode} - isLatestRelease: ${ep.isLatestRelease}`);\n    });\n\n    // Test the latest episode detection logic\n    if (testSeries.episodes.length > 0) {\n      // Clear all isLatestRelease flags\n      testSeries.episodes.forEach(episode => {\n        episode.isLatestRelease = false;\n      });\n\n      // Find the actual latest episode (highest season/episode number)\n      const latestEpisode = testSeries.episodes.reduce((latest, current) => {\n        if (current.season > latest.season) return current;\n        if (current.season === latest.season && current.episode > latest.episode) return current;\n        return latest;\n      });\n\n      console.log(`🎯 Latest episode detected: S${latestEpisode.season}E${latestEpisode.episode}`);\n\n      // Mark it as latest\n      const latestEpisodeIndex = testSeries.episodes.findIndex(ep =>\n        ep.season === latestEpisode.season && ep.episode === latestEpisode.episode\n      );\n\n      if (latestEpisodeIndex >= 0) {\n        testSeries.episodes[latestEpisodeIndex].isLatestRelease = true;\n        await testSeries.save();\n        console.log(`✅ Marked S${latestEpisode.season}E${latestEpisode.episode} as latest release`);\n      }\n\n      // Show updated episode states\n      console.log('📝 Updated episode states:');\n      testSeries.episodes.forEach((ep, index) => {\n        console.log(`   Episode ${index}: S${ep.season}E${ep.episode} - isLatestRelease: ${ep.isLatestRelease}`);\n      });\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: 'Latest episode logic test completed',\n      seriesTitle: testSeries.title,\n      episodeCount: testSeries.episodes.length,\n      latestEpisode: testSeries.episodes.find(ep => ep.isLatestRelease)\n    });\n\n  } catch (error) {\n    console.error('❌ Test error:', error);\n    return NextResponse.json(\n      { \n        success: false,\n        error: 'Test failed',\n        message: error.message\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;QAEd,QAAQ,GAAG,CAAC;QAEZ,wDAAwD;QACxD,MAAM,aAAa,MAAM,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC;YAAE,QAAQ;QAAY;QAE9D,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,WAAW,KAAK,EAAE;QACvD,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,WAAW,QAAQ,CAAC,MAAM,EAAE;QAE9D,8BAA8B;QAC9B,QAAQ,GAAG,CAAC;QACZ,WAAW,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI;YAC/B,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,oBAAoB,EAAE,GAAG,eAAe,EAAE;QACzG;QAEA,0CAA0C;QAC1C,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;YAClC,kCAAkC;YAClC,WAAW,QAAQ,CAAC,OAAO,CAAC,CAAA;gBAC1B,QAAQ,eAAe,GAAG;YAC5B;YAEA,iEAAiE;YACjE,MAAM,gBAAgB,WAAW,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ;gBACxD,IAAI,QAAQ,MAAM,GAAG,OAAO,MAAM,EAAE,OAAO;gBAC3C,IAAI,QAAQ,MAAM,KAAK,OAAO,MAAM,IAAI,QAAQ,OAAO,GAAG,OAAO,OAAO,EAAE,OAAO;gBACjF,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,cAAc,MAAM,CAAC,CAAC,EAAE,cAAc,OAAO,EAAE;YAE3F,oBAAoB;YACpB,MAAM,qBAAqB,WAAW,QAAQ,CAAC,SAAS,CAAC,CAAA,KACvD,GAAG,MAAM,KAAK,cAAc,MAAM,IAAI,GAAG,OAAO,KAAK,cAAc,OAAO;YAG5E,IAAI,sBAAsB,GAAG;gBAC3B,WAAW,QAAQ,CAAC,mBAAmB,CAAC,eAAe,GAAG;gBAC1D,MAAM,WAAW,IAAI;gBACrB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,cAAc,MAAM,CAAC,CAAC,EAAE,cAAc,OAAO,CAAC,kBAAkB,CAAC;YAC5F;YAEA,8BAA8B;YAC9B,QAAQ,GAAG,CAAC;YACZ,WAAW,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI;gBAC/B,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,oBAAoB,EAAE,GAAG,eAAe,EAAE;YACzG;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,aAAa,WAAW,KAAK;YAC7B,cAAc,WAAW,QAAQ,CAAC,MAAM;YACxC,eAAe,WAAW,QAAQ,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,eAAe;QAClE;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,MAAM,OAAO;QACxB,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}