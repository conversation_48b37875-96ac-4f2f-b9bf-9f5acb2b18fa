import { NextRequest, NextResponse } from 'next/server';
import UniversalSyncService from '@/lib/universalSyncService';
import connectDB from '@/lib/mongodb';

const universalSync = UniversalSyncService.getInstance();

/**
 * GET - Check universal sync status or force sync
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'status') {
      const status = await universalSync.getSyncStatus();
      const utcNow = new Date().toISOString();
      
      return NextResponse.json({
        success: true,
        utcTime: utcNow,
        data: status
      });
    }

    if (action === 'force') {
      console.log('🔧 Manual universal sync requested via GET');
      const result = await universalSync.forceSync();
      return NextResponse.json({
        success: true,
        message: 'Universal sync completed',
        data: result
      });
    }

    if (action === 'reset') {
      console.log('🔄 Resetting universal sync status...');

      // Reset the running status
      await connectDB();
      const { default: SyncStatus } = await import('@/models/SyncStatus');

      await SyncStatus.updateOne(
        { syncType: 'UNIVERSAL_SYNC' },
        {
          isRunning: false,
          updatedAt: new Date()
        }
      );

      console.log('✅ Universal sync status reset');

      const status = await universalSync.getSyncStatus();
      return NextResponse.json({
        success: true,
        message: 'Universal sync status reset successfully',
        data: status
      });
    }

    // Default: return status
    const status = await universalSync.getSyncStatus();
    const utcNow = new Date().toISOString();
    
    return NextResponse.json({
      success: true,
      utcTime: utcNow,
      data: status
    });

  } catch (error) {
    console.error('❌ Universal sync GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process request',
        message: error.message,
        utcTime: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Force manual universal sync
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { action } = body;

    if (action === 'force' || !action) {
      console.log('🔧 Manual universal sync requested via POST');
      const result = await universalSync.forceSync();
      
      return NextResponse.json({
        success: true,
        message: 'Universal sync completed successfully',
        data: result
      });
    }

    if (action === 'status') {
      const status = await universalSync.getSyncStatus();
      const utcNow = new Date().toISOString();
      
      return NextResponse.json({
        success: true,
        utcTime: utcNow,
        data: status
      });
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Invalid action',
        message: 'Supported actions: force, status',
        utcTime: new Date().toISOString()
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('❌ Universal sync POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Universal sync failed',
        message: error.message,
        utcTime: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
