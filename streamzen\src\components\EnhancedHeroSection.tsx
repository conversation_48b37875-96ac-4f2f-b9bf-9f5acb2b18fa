'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, Play, Info, Star, Volume2, VolumeX, Sparkles, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface HeroItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series';
}

interface EnhancedHeroSectionProps {
  items: HeroItem[];
}

const EnhancedHeroSection: React.FC<EnhancedHeroSectionProps> = ({ items }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isMuted, setIsMuted] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % items.length);
    }, 10000); // Longer duration for immersive experience

    return () => clearInterval(interval);
  }, [isAutoPlaying, items.length]);

  // Mouse tracking for parallax effects
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setMousePosition({
      x: (e.clientX - rect.left) / rect.width,
      y: (e.clientY - rect.top) / rect.height,
    });
  }, []);

  // Preload next image
  useEffect(() => {
    if (items.length > 1) {
      const nextIndex = (currentIndex + 1) % items.length;
      const img = new window.Image();
      img.src = getImageUrl(items[nextIndex].posterUrl);
    }
  }, [currentIndex, items]);

  const currentItem = items[currentIndex];

  if (!currentItem || items.length === 0) {
    return (
      <div className="relative h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Sparkles className="w-16 h-16 text-white/50 mx-auto mb-4 animate-pulse" />
          <h2 className="text-2xl font-bold text-white/70">Loading amazing content...</h2>
        </div>
      </div>
    );
  }

  const navigateToItem = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 15000); // Resume auto-play after 15s
  };

  const nextItem = () => navigateToItem((currentIndex + 1) % items.length);
  const prevItem = () => navigateToItem((currentIndex - 1 + items.length) % items.length);

  return (
    <div 
      className="relative h-screen overflow-hidden bg-black"
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      {/* Dynamic Background with Parallax */}
      <div className="absolute inset-0">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ 
              opacity: 1, 
              scale: 1,
              x: (mousePosition.x - 0.5) * 20,
              y: (mousePosition.y - 0.5) * 20
            }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 1.2, ease: "easeInOut" }}
            className="absolute inset-0"
          >
            <Image
              src={getImageUrl(currentItem.posterUrl)}
              alt={currentItem.title}
              fill
              className="object-cover object-center"
              priority
              onLoad={() => setIsLoaded(true)}
            />
            {/* Multi-layer Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-r from-black via-black/70 to-transparent" />
            <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-black/30" />
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black" />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Floating Particles Effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            animate={{
              x: [0, Math.random() * 100, 0],
              y: [0, Math.random() * 100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              ease: "linear",
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative h-full flex items-center">
        <div className="max-w-7xl mx-auto px-6 lg:px-12 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content Side */}
            <motion.div
              key={`content-${currentIndex}`}
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="space-y-8"
            >
              {/* Category Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="flex items-center space-x-3"
              >
                <div className="px-4 py-2 bg-gradient-to-r from-red-600/20 to-red-700/20 border border-red-500/30 rounded-full backdrop-blur-sm">
                  <span className="text-red-400 text-sm font-semibold uppercase tracking-wider">
                    {currentItem.type === 'movie' ? '🎬 Featured Movie' : '📺 Featured Series'}
                  </span>
                </div>
                {currentItem.imdbRating && currentItem.imdbRating >= 8.0 && (
                  <div className="flex items-center space-x-1 px-3 py-1 bg-yellow-500/20 border border-yellow-400/30 rounded-full backdrop-blur-sm">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-yellow-400 text-sm font-bold">Top Rated</span>
                  </div>
                )}
              </motion.div>

              {/* Title */}
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-5xl lg:text-7xl font-black text-white leading-tight"
                style={{
                  textShadow: '2px 2px 4px rgba(0,0,0,0.8), 0 0 20px rgba(0,0,0,0.5)'
                }}
              >
                {currentItem.title}
              </motion.h1>

              {/* Metadata */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="flex items-center space-x-6 text-gray-300"
              >
                {currentItem.year && (
                  <span className="text-lg font-semibold">{currentItem.year}</span>
                )}
                {currentItem.imdbRating && (
                  <div className="flex items-center space-x-2">
                    <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    <span className="text-lg font-bold text-white">
                      {formatRating(currentItem.imdbRating)}
                    </span>
                  </div>
                )}
                <div className="flex items-center space-x-1">
                  <TrendingUp className="w-5 h-5 text-green-400" />
                  <span className="text-green-400 font-semibold">Trending</span>
                </div>
              </motion.div>

              {/* Description */}
              {currentItem.description && (
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                  className="text-lg text-gray-300 leading-relaxed max-w-2xl"
                  style={{
                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
                  }}
                >
                  {truncateText(currentItem.description, 200)}
                </motion.p>
              )}

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.2 }}
                className="flex items-center space-x-4"
              >
                <Link
                  href={`/watch/${currentItem.type}/${currentItem.imdbId}`}
                  className="group relative px-8 py-4 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-red-500/25"
                >
                  <div className="flex items-center space-x-3">
                    <Play className="w-6 h-6 fill-current" />
                    <span className="text-lg">Watch Now</span>
                  </div>
                  <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </Link>

                <Link
                  href={`/watch/${currentItem.type}/${currentItem.imdbId}`}
                  className="group px-8 py-4 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl border border-white/20 hover:border-white/40 transition-all duration-300 backdrop-blur-sm"
                >
                  <div className="flex items-center space-x-3">
                    <Info className="w-5 h-5" />
                    <span>More Info</span>
                  </div>
                </Link>
              </motion.div>
            </motion.div>

            {/* Enhanced Poster Side */}
            <motion.div
              key={`poster-${currentIndex}`}
              initial={{ opacity: 0, x: 50, rotateY: 15 }}
              animate={{ opacity: 1, x: 0, rotateY: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
              className="hidden lg:flex justify-center items-center"
            >
              <div className="relative group">
                <div className="absolute -inset-4 bg-gradient-to-r from-red-600/20 via-purple-600/20 to-blue-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500" />
                <div className="relative">
                  <Image
                    src={getImageUrl(currentItem.posterUrl)}
                    alt={currentItem.title}
                    width={400}
                    height={600}
                    className="rounded-2xl shadow-2xl transform group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent rounded-2xl" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      {items.length > 1 && (
        <>
          {/* Previous/Next Buttons */}
          <button
            onClick={prevItem}
            className="absolute left-6 top-1/2 -translate-y-1/2 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 z-10"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          <button
            onClick={nextItem}
            className="absolute right-6 top-1/2 -translate-y-1/2 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 z-10"
          >
            <ChevronRight className="w-6 h-6" />
          </button>

          {/* Dots Indicator */}
          <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3 z-10">
            {items.map((_, index) => (
              <button
                key={index}
                onClick={() => navigateToItem(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-all duration-300",
                  index === currentIndex
                    ? "bg-white scale-125 shadow-lg"
                    : "bg-white/40 hover:bg-white/60"
                )}
              />
            ))}
          </div>
        </>
      )}

      {/* Auto-play Indicator */}
      <div className="absolute top-6 right-6 flex items-center space-x-3 z-10">
        <button
          onClick={() => setIsAutoPlaying(!isAutoPlaying)}
          className="p-2 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm transition-all duration-300"
        >
          {isAutoPlaying ? (
            <div className="w-5 h-5 flex items-center justify-center">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            </div>
          ) : (
            <div className="w-5 h-5 flex items-center justify-center">
              <div className="w-2 h-2 bg-gray-400 rounded-full" />
            </div>
          )}
        </button>
      </div>
    </div>
  );
};

export default EnhancedHeroSection;
