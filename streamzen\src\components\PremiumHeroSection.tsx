'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Play, Search, Star, ChevronLeft, ChevronRight, Film, Tv,
  Calendar, Clock, TrendingUp, Fire, Zap
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface HeroItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series';
}

interface PremiumHeroSectionProps {
  items: HeroItem[];
}

const PremiumHeroSection: React.FC<PremiumHeroSectionProps> = ({ items }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % items.length);
    }, 6000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, items.length]);

  const currentItem = items[currentIndex];
  const watchHref = currentItem?.type === 'movie'
    ? `/watch/movie/${currentItem.imdbId}`
    : `/watch/series/${currentItem.imdbId}`;

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 15000);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 15000);
  };

  if (!currentItem || items.length === 0) {
    return (
      <div className="relative h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Sparkles className="w-16 h-16 text-white/30 mx-auto mb-4 animate-pulse" />
          <h2 className="text-2xl font-bold text-white/50">Preparing your cinematic experience...</h2>
        </div>
      </div>
    );
  }

  return (
    <section className="relative min-h-screen overflow-hidden bg-black">
      {/* Dynamic Background with Multiple Layers */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          className="absolute inset-0"
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        >
          <Image
            src={getImageUrl(currentItem.posterUrl)}
            alt={currentItem.title}
            fill
            className="object-cover object-center"
            priority
            sizes="100vw"
          />

          {/* Advanced Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-r from-black via-black/70 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-br from-red-900/20 via-transparent to-black/40" />
        </motion.div>
      </AnimatePresence>

      {/* Floating Stats Panel */}
      <motion.div
        className="absolute top-8 right-8 z-20"
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 1, duration: 0.8 }}
      >
        <div className="bg-black/40 backdrop-blur-xl rounded-2xl border border-white/10 p-6 space-y-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{stats.activeUsers}</div>
            <div className="text-xs text-gray-400 flex items-center justify-center space-x-1">
              <Users className="w-3 h-3" />
              <span>Watching Now</span>
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-red-400">{stats.contentLibrary}</div>
            <div className="text-xs text-gray-400 flex items-center justify-center space-x-1">
              <Film className="w-3 h-3" />
              <span>Content</span>
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-400">{stats.newToday}</div>
            <div className="text-xs text-gray-400 flex items-center justify-center space-x-1">
              <Zap className="w-3 h-3" />
              <span>New Today</span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content Grid */}
      <div className="relative h-full flex items-center min-h-screen">
        <div className="max-w-7xl mx-auto px-6 lg:px-12 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

            {/* Left Content */}
            <div className="space-y-8">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  className="space-y-8"
                >
                  {/* Enhanced Badge Section */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2, duration: 0.6 }}
                    className="flex items-center space-x-4"
                  >
                    <div className="flex items-center space-x-3 px-4 py-2 bg-gradient-to-r from-red-500/20 to-red-600/10 border border-red-500/30 rounded-xl backdrop-blur-sm">
                      {currentItem.type === 'movie' ? (
                        <Film className="w-4 h-4 text-red-400" />
                      ) : (
                        <Tv className="w-4 h-4 text-red-400" />
                      )}
                      <span className="text-red-300 font-semibold text-sm uppercase tracking-wider">
                        {currentItem.type === 'movie' ? 'Featured Movie' : 'Featured Series'}
                      </span>
                    </div>

                    {currentItem.imdbRating && (
                      <div className="flex items-center space-x-2 px-3 py-2 bg-yellow-500/20 border border-yellow-500/30 rounded-xl backdrop-blur-sm">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-yellow-300 font-bold text-sm">
                          {formatRating(currentItem.imdbRating)}
                        </span>
                      </div>
                    )}
                  </motion.div>

                  {/* Dynamic Title */}
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.8 }}
                    className="space-y-4"
                  >
                    <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-white leading-none tracking-tight">
                      {currentItem.title}
                    </h1>
                    {currentItem.year && (
                      <div className="flex items-center space-x-4 text-xl text-gray-300">
                        <Calendar className="w-5 h-5 text-red-400" />
                        <span className="font-semibold">{currentItem.year}</span>
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="text-gray-400">HD Quality</span>
                      </div>
                    )}
                  </motion.div>

                  {/* Enhanced Description */}
                  {currentItem.description && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6, duration: 0.6 }}
                      className="space-y-4"
                    >
                      <p className="text-xl text-gray-300 leading-relaxed max-w-2xl">
                        {truncateText(currentItem.description, 180)}
                      </p>

                      {/* Quick Stats */}
                      <div className="flex items-center space-x-6 text-sm">
                        <div className="flex items-center space-x-2 text-green-400">
                          <Eye className="w-4 h-4" />
                          <span className="font-semibold">{Math.floor(Math.random() * 500 + 100)}K views</span>
                        </div>
                        <div className="flex items-center space-x-2 text-red-400">
                          <Heart className="w-4 h-4" />
                          <span className="font-semibold">{Math.floor(Math.random() * 50 + 10)}K likes</span>
                        </div>
                        <div className="flex items-center space-x-2 text-blue-400">
                          <Download className="w-4 h-4" />
                          <span className="font-semibold">HD Available</span>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Enhanced Action Buttons */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8, duration: 0.6 }}
                    className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6 pt-4"
                  >
                    <Link href={watchHref}>
                      <motion.button
                        className="flex items-center space-x-4 px-8 py-4 bg-gradient-to-r from-red-500 to-red-600 text-white font-bold rounded-2xl shadow-2xl shadow-red-500/30 hover:shadow-red-500/50 transition-all duration-300"
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <div className="p-2 bg-white/20 rounded-full">
                          <Play className="w-6 h-6 fill-current" />
                        </div>
                        <div className="text-left">
                          <div className="text-lg">Watch Now</div>
                          <div className="text-sm opacity-80">Free • HD Quality</div>
                        </div>
                      </motion.button>
                    </Link>

                    <motion.button
                      className="flex items-center space-x-3 px-6 py-4 bg-white/10 text-white font-semibold rounded-2xl border border-white/20 backdrop-blur-sm hover:bg-white/20 transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Info className="w-5 h-5" />
                      <span>More Details</span>
                    </motion.button>

                    <motion.button
                      className="flex items-center space-x-3 px-6 py-4 bg-gray-800/60 text-gray-300 font-semibold rounded-2xl border border-gray-600/50 backdrop-blur-sm hover:bg-gray-700/60 hover:text-white transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Heart className="w-5 h-5" />
                      <span>Add to List</span>
                    </motion.button>
                  </motion.div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Right Content - Featured Poster */}
            <motion.div
              className="hidden lg:block"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  className="relative"
                  initial={{ opacity: 0, scale: 0.8, rotateY: 45 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  exit={{ opacity: 0, scale: 0.8, rotateY: -45 }}
                  transition={{ duration: 1, ease: "easeInOut" }}
                >
                  <div className="relative aspect-[2/3] max-w-md mx-auto">
                    <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 to-red-600/10 rounded-3xl blur-2xl transform rotate-6"></div>
                    <div className="relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl overflow-hidden border border-white/20 shadow-2xl">
                      <Image
                        src={getImageUrl(currentItem.posterUrl)}
                        alt={currentItem.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, 50vw"
                      />

                      {/* Poster Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                      {/* Floating Play Button */}
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center"
                        whileHover={{ scale: 1.1 }}
                      >
                        <Link href={watchHref}>
                          <div className="w-20 h-20 bg-red-500/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-2xl border border-white/20 hover:bg-red-500 transition-all duration-300">
                            <Play className="w-8 h-8 text-white fill-current ml-1" />
                          </div>
                        </Link>
                      </motion.div>

                      {/* Quality Badge */}
                      <div className="absolute top-4 right-4">
                        <div className="px-3 py-1 bg-green-500/90 backdrop-blur-sm rounded-lg border border-white/20">
                          <span className="text-white font-bold text-xs">4K HDR</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Enhanced Navigation Controls */}
      {items.length > 1 && (
        <>
          {/* Previous Button */}
          <motion.button
            onClick={prevSlide}
            className="absolute left-8 top-1/2 -translate-y-1/2 w-16 h-16 bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl flex items-center justify-center text-white hover:bg-black/60 hover:border-red-500/50 transition-all duration-300 z-30 group"
            whileHover={{ scale: 1.1, x: -5 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="w-6 h-6 group-hover:text-red-400 transition-colors duration-300" />
          </motion.button>

          {/* Next Button */}
          <motion.button
            onClick={nextSlide}
            className="absolute right-8 top-1/2 -translate-y-1/2 w-16 h-16 bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl flex items-center justify-center text-white hover:bg-black/60 hover:border-red-500/50 transition-all duration-300 z-30 group"
            whileHover={{ scale: 1.1, x: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronRight className="w-6 h-6 group-hover:text-red-400 transition-colors duration-300" />
          </motion.button>

          {/* Enhanced Slide Indicators */}
          <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-30">
            <div className="flex items-center space-x-3 px-6 py-3 bg-black/40 backdrop-blur-xl rounded-2xl border border-white/20">
              {items.map((item, index) => (
                <motion.button
                  key={index}
                  onClick={() => {
                    setCurrentIndex(index);
                    setIsAutoPlaying(false);
                    setTimeout(() => setIsAutoPlaying(true), 15000);
                  }}
                  className={cn(
                    'relative transition-all duration-300',
                    index === currentIndex
                      ? 'w-8 h-3 bg-red-500 rounded-full'
                      : 'w-3 h-3 bg-gray-500 rounded-full hover:bg-gray-400'
                  )}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {index === currentIndex && (
                    <motion.div
                      className="absolute inset-0 bg-red-400 rounded-full"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </motion.button>
              ))}

              {/* Current/Total Indicator */}
              <div className="ml-4 text-white text-sm font-semibold">
                {currentIndex + 1} / {items.length}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Enhanced Auto-play Progress Bar */}
      {isAutoPlaying && items.length > 1 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/30 z-20">
          <motion.div
            className="h-full bg-gradient-to-r from-red-500 to-red-600 shadow-lg shadow-red-500/50"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 8, ease: "linear" }}
            key={currentIndex}
          />
        </div>
      )}

      {/* Floating Action Panel */}
      <motion.div
        className="absolute bottom-8 right-8 z-20"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2, duration: 0.8 }}
      >
        <div className="bg-black/40 backdrop-blur-xl rounded-2xl border border-white/10 p-4 space-y-3">
          <motion.button
            className="w-12 h-12 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded-xl flex items-center justify-center text-red-400 hover:text-red-300 transition-all duration-300"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Heart className="w-5 h-5" />
          </motion.button>

          <motion.button
            className="w-12 h-12 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded-xl flex items-center justify-center text-blue-400 hover:text-blue-300 transition-all duration-300"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Download className="w-5 h-5" />
          </motion.button>

          <motion.button
            className="w-12 h-12 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 rounded-xl flex items-center justify-center text-green-400 hover:text-green-300 transition-all duration-300"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Award className="w-5 h-5" />
          </motion.button>
        </div>
      </motion.div>

      {/* Ambient Lighting Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
      </div>
    </section>
  );
};

export default PremiumHeroSection;
