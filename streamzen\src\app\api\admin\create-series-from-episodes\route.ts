import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';
import IMDbScraper from '@/lib/scraper';

const imdbScraper = IMDbScraper.getInstance();

export async function GET() {
  return await createSeriesFromEpisodes();
}

export async function POST() {
  return await createSeriesFromEpisodes();
}

async function createSeriesFromEpisodes() {
  try {
    await connectDB();
    
    console.log('🔧 Creating Series from existing Episodes...');
    
    // Get unique IMDb IDs from episodes
    const uniqueImdbIds = await Episode.distinct('imdbId');
    console.log(`📊 Found ${uniqueImdbIds.length} unique series from episodes`);
    
    let created = 0;
    let existing = 0;
    let failed = 0;
    
    // Process in batches of 5 for speed
    const batchSize = 5;
    for (let i = 0; i < uniqueImdbIds.length; i += batchSize) {
      const batch = uniqueImdbIds.slice(i, i + batchSize);
      console.log(`🔄 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(uniqueImdbIds.length/batchSize)}`);
      
      const batchPromises = batch.map(async (imdbId) => {
        try {
          // Check if series already exists
          const existingSeries = await Series.findOne({ imdbId });
          if (existingSeries) {
            existing++;
            return { imdbId, status: 'existing' };
          }
          
          // Get episode data for this series
          const sampleEpisode = await Episode.findOne({ imdbId });
          if (!sampleEpisode) {
            failed++;
            return { imdbId, status: 'no_episode_data' };
          }
          
          try {
            // Try to scrape series data from IMDb
            const seriesData = await imdbScraper.scrapeSeries(imdbId);
            const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;
            const currentYear = new Date().getFullYear();
            
            const series = new Series({
              imdbId,
              title: seriesData.title || sampleEpisode.seriesTitle || 'Unknown Series',
              description: seriesData.description,
              posterUrl: seriesData.posterUrl,
              imdbRating: seriesData.imdbRating,
              startYear: seriesData.startYear || currentYear,
              endYear: seriesData.endYear,
              genres: seriesData.genres || sampleEpisode.genres || [],
              cast: seriesData.cast || [],
              director: seriesData.director,
              language: seriesData.language,
              country: seriesData.country,
              runtime: seriesData.runtime,
              embedUrl,
              type: 'series'
            });
            
            await series.save();
            created++;
            console.log(`✅ Created: ${series.title}`);
            return { imdbId, status: 'created', title: series.title };
            
          } catch (scrapeError) {
            // Create minimal series if scraping fails
            const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;
            const currentYear = new Date().getFullYear();
            
            const series = new Series({
              imdbId,
              title: sampleEpisode.seriesTitle || 'Unknown Series',
              startYear: currentYear,
              embedUrl,
              type: 'series',
              genres: sampleEpisode.genres || []
            });
            
            await series.save();
            created++;
            console.log(`✅ Created minimal: ${series.title}`);
            return { imdbId, status: 'created_minimal', title: series.title };
          }
          
        } catch (error) {
          failed++;
          console.error(`❌ Error processing ${imdbId}:`, error);
          return { imdbId, status: 'failed', error: error.message };
        }
      });
      
      await Promise.all(batchPromises);
    }
    
    console.log(`🎉 Series creation complete: ${created} created, ${existing} existing, ${failed} failed`);
    
    return NextResponse.json({
      success: true,
      message: 'Series creation from episodes completed',
      stats: {
        totalEpisodeImdbIds: uniqueImdbIds.length,
        seriesCreated: created,
        seriesExisting: existing,
        seriesFailed: failed
      }
    });
    
  } catch (error) {
    console.error('❌ Error creating series from episodes:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create series from episodes',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
