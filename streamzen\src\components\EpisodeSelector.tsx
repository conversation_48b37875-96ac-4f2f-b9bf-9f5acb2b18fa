'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Episode } from '@/types';
import { Play, Clock, Calendar, ChevronLeft, ChevronRight, Film, Tv, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface EpisodeSelectorProps {
  seriesId: string;
  episodes: Episode[];
  currentSeason: number;
  currentEpisode: number;
}

export default function EpisodeSelector({
  seriesId,
  episodes,
  currentSeason,
  currentEpisode
}: EpisodeSelectorProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedSeason, setSelectedSeason] = useState(currentSeason);
  const [hoveredEpisode, setHoveredEpisode] = useState<number | null>(null);

  // Update selectedSeason when currentSeason prop changes
  useEffect(() => {
    setSelectedSeason(currentSeason);
  }, [currentSeason]);

  // Group episodes by season
  const episodesBySeason = episodes.reduce((acc, episode) => {
    if (!acc[episode.season]) {
      acc[episode.season] = [];
    }
    acc[episode.season].push(episode);
    return acc;
  }, {} as Record<number, Episode[]>);

  // Get available seasons
  const availableSeasons = Object.keys(episodesBySeason)
    .map(Number)
    .sort((a, b) => a - b);

  // Get episodes for selected season
  const currentSeasonEpisodes = episodesBySeason[selectedSeason] || [];

  const handleSeasonChange = (season: number) => {
    setSelectedSeason(season);
    // Navigate to first episode of the selected season
    const firstEpisode = episodesBySeason[season]?.[0];
    if (firstEpisode) {
      router.push(`/watch/series/${seriesId}?season=${season}&episode=${firstEpisode.episode}`);
    }
  };

  const handleEpisodeChange = (episode: number) => {
    router.push(`/watch/series/${seriesId}?season=${selectedSeason}&episode=${episode}`);
  };

  return (
    <div className="relative">
      {/* Modern Container with Red Accent */}
      <div className="bg-gradient-to-br from-gray-900/95 via-black/90 to-gray-800/95 backdrop-blur-xl rounded-3xl border border-red-500/20 shadow-2xl overflow-hidden">

        {/* Header Section */}
        <div className="relative p-8 bg-gradient-to-r from-red-600/10 via-transparent to-red-600/5 border-b border-gray-700/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-red-500/20 rounded-2xl border border-red-500/30">
                <Tv className="w-6 h-6 text-red-400" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">Episodes</h3>
                <p className="text-gray-400 text-sm">Choose your episode</p>
              </div>
            </div>

            {/* Episode Stats */}
            <div className="flex items-center space-x-6 text-sm">
              <div className="text-center">
                <div className="text-red-400 font-bold text-lg">{currentSeasonEpisodes.length}</div>
                <div className="text-gray-500">Episodes</div>
              </div>
              <div className="text-center">
                <div className="text-red-400 font-bold text-lg">{availableSeasons.length}</div>
                <div className="text-gray-500">Seasons</div>
              </div>
            </div>
          </div>
        </div>

        <div className="p-8">
          {/* Season Selector */}
          {availableSeasons.length > 1 && (
            <div className="mb-8">
              <label className="block text-sm font-semibold text-gray-300 mb-4 flex items-center space-x-2">
                <Film className="w-4 h-4 text-red-400" />
                <span>Select Season</span>
              </label>

              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                {availableSeasons.map((season) => {
                  const isSelected = season === selectedSeason;
                  const episodeCount = episodesBySeason[season]?.length || 0;

                  return (
                    <motion.button
                      key={season}
                      onClick={() => handleSeasonChange(season)}
                      className={cn(
                        'relative p-4 rounded-xl border transition-all duration-300 text-center group',
                        isSelected
                          ? 'bg-gradient-to-br from-red-500/20 to-red-600/10 border-red-500/50 text-white shadow-lg shadow-red-500/20'
                          : 'bg-gray-800/40 border-gray-600/50 text-gray-300 hover:bg-gray-700/50 hover:border-red-500/30 hover:text-white'
                      )}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="font-bold text-lg">S{season}</div>
                      <div className="text-xs opacity-75">{episodeCount} eps</div>

                      {isSelected && (
                        <motion.div
                          className="absolute inset-0 rounded-xl border-2 border-red-500/30"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3 }}
                        />
                      )}
                    </motion.button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Episodes Grid */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-semibold text-white flex items-center space-x-2">
                <Play className="w-5 h-5 text-red-400" />
                <span>Season {selectedSeason} Episodes</span>
              </h4>
              <div className="text-sm text-gray-400">
                {currentSeasonEpisodes.length} episodes available
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <AnimatePresence>
                {currentSeasonEpisodes
                  .sort((a, b) => a.episode - b.episode)
                  .map((episode, index) => {
                    const isActive = episode.season === currentSeason && episode.episode === currentEpisode && selectedSeason === currentSeason;
                    const isHovered = hoveredEpisode === episode.episode;

                    return (
                      <motion.button
                        key={`${episode.season}-${episode.episode}`}
                        onClick={() => handleEpisodeChange(episode.episode)}
                        onMouseEnter={() => setHoveredEpisode(episode.episode)}
                        onMouseLeave={() => setHoveredEpisode(null)}
                        className={cn(
                          'relative p-5 rounded-2xl border transition-all duration-300 text-left group overflow-hidden',
                          isActive
                            ? 'bg-gradient-to-br from-red-500/20 to-red-600/10 border-red-500/50 text-white shadow-lg shadow-red-500/20'
                            : 'bg-gray-800/40 border-gray-600/30 text-gray-300 hover:bg-gray-700/50 hover:border-red-500/30'
                        )}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {/* Background Pattern */}
                        <div className="absolute inset-0 opacity-5">
                          <div className="absolute top-0 right-0 w-20 h-20 bg-red-500 rounded-full -translate-y-10 translate-x-10" />
                          <div className="absolute bottom-0 left-0 w-16 h-16 bg-red-600 rounded-full translate-y-8 -translate-x-8" />
                        </div>

                        {/* Episode Number Badge */}
                        <div className="flex items-start justify-between mb-3">
                          <div className={cn(
                            'px-3 py-1 rounded-lg text-xs font-bold',
                            isActive
                              ? 'bg-red-500/30 text-red-200 border border-red-500/40'
                              : 'bg-gray-700/50 text-gray-400 border border-gray-600/50'
                          )}>
                            EP {episode.episode}
                          </div>

                          {isActive && (
                            <motion.div
                              className="p-1 bg-red-500 rounded-full"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Play className="w-3 h-3 text-white fill-current" />
                            </motion.div>
                          )}
                        </div>

                        {/* Episode Title */}
                        {episode.episodeTitle && (
                          <div className={cn(
                            'font-semibold text-sm mb-2 line-clamp-2 leading-tight',
                            isActive ? 'text-white' : 'text-gray-200 group-hover:text-white'
                          )}>
                            {episode.episodeTitle}
                          </div>
                        )}

                        {/* Episode Meta */}
                        <div className="flex items-center space-x-4 text-xs">
                          {episode.runtime && (
                            <div className={cn(
                              'flex items-center space-x-1',
                              isActive ? 'text-red-200' : 'text-gray-500 group-hover:text-gray-400'
                            )}>
                              <Clock className="w-3 h-3" />
                              <span>{episode.runtime}</span>
                            </div>
                          )}

                          {episode.airDate && (
                            <div className={cn(
                              'flex items-center space-x-1',
                              isActive ? 'text-red-200' : 'text-gray-500 group-hover:text-gray-400'
                            )}>
                              <Calendar className="w-3 h-3" />
                              <span>{new Date(episode.airDate).getFullYear()}</span>
                            </div>
                          )}
                        </div>

                        {/* Hover Play Button */}
                        <AnimatePresence>
                          {isHovered && !isActive && (
                            <motion.div
                              className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <div className="p-3 bg-red-500 rounded-full shadow-lg">
                                <Play className="w-4 h-4 text-white fill-current" />
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        {/* Active Glow Effect */}
                        {isActive && (
                          <motion.div
                            className="absolute inset-0 rounded-2xl border-2 border-red-500/30"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3 }}
                          />
                        )}
                      </motion.button>
                    );
                  })}
              </AnimatePresence>
            </div>
          </div>

          {/* Navigation Controls - Only show when viewing current season */}
          {selectedSeason === currentSeason && (
            <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-700/30">
              {/* Previous Episode Button */}
              <motion.button
                onClick={() => {
                  const prevEpisode = currentSeasonEpisodes.find(ep => ep.episode === currentEpisode - 1);
                  if (prevEpisode) {
                    handleEpisodeChange(prevEpisode.episode);
                  }
                }}
                disabled={!currentSeasonEpisodes.find(ep => ep.episode === currentEpisode - 1)}
                className={cn(
                  'flex items-center space-x-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300',
                  'disabled:opacity-40 disabled:cursor-not-allowed',
                  'bg-gray-800/50 border border-gray-600/50 text-gray-300',
                  'hover:bg-gray-700/50 hover:border-red-500/30 hover:text-white',
                  'disabled:hover:bg-gray-800/50 disabled:hover:border-gray-600/50 disabled:hover:text-gray-300'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ChevronLeft className="w-5 h-5" />
                <span>Previous</span>
              </motion.button>

              {/* Current Episode Info */}
              <div className="text-center">
                <div className="text-white font-semibold">Episode {currentEpisode}</div>
                <div className="text-gray-400 text-sm">of {currentSeasonEpisodes.length} in Season {selectedSeason}</div>
              </div>

              {/* Next Episode Button */}
              <motion.button
                onClick={() => {
                  const nextEpisode = currentSeasonEpisodes.find(ep => ep.episode === currentEpisode + 1);
                  if (nextEpisode) {
                    handleEpisodeChange(nextEpisode.episode);
                  }
                }}
                disabled={!currentSeasonEpisodes.find(ep => ep.episode === currentEpisode + 1)}
                className={cn(
                  'flex items-center space-x-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300',
                  'disabled:opacity-40 disabled:cursor-not-allowed',
                  'bg-gradient-to-r from-red-500/20 to-red-600/10 border border-red-500/50 text-white shadow-lg shadow-red-500/20',
                  'hover:from-red-500/30 hover:to-red-600/20 hover:border-red-500/70',
                  'disabled:hover:from-red-500/20 disabled:hover:to-red-600/10 disabled:hover:border-red-500/50'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span>Next</span>
                <ChevronRight className="w-5 h-5" />
              </motion.button>
            </div>
          )}

          {/* Season Navigation Info - Show when viewing different season */}
          {selectedSeason !== currentSeason && (
            <div className="mt-8 pt-6 border-t border-gray-700/30 text-center">
              <div className="text-gray-400 text-sm">
                You're browsing Season {selectedSeason}.
                <button
                  onClick={() => router.push(`/watch/series/${seriesId}?season=${currentSeason}&episode=${currentEpisode}`)}
                  className="ml-2 text-red-400 hover:text-red-300 underline transition-colors"
                >
                  Return to current episode (S{currentSeason}E{currentEpisode})
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
