import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * Debug endpoint to check database state for embedded episodes
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();

    // Check series with episodes - CORRECTED to look in Series collection
    const totalSeries = await Series.countDocuments({});
    const totalSeriesTyped = await Series.countDocuments({ type: 'series' });
    const seriesWithEpisodes = await Series.countDocuments({
      'episodes.0': { $exists: true }
    });
    const seriesWithLatestEpisodes = await Series.countDocuments({
      'episodes.isLatestRelease': true
    });

    // Get sample series with episodes - CORRECTED
    const sampleSeries = await Series.findOne({
      'episodes.0': { $exists: true }
    }).lean();

    // Count total embedded episodes - CORRECTED
    const totalEpisodesResult = await Series.aggregate([
      { $match: { 'episodes.0': { $exists: true } } },
      { $unwind: '$episodes' },
      { $count: 'total' }
    ]);

    // Count latest release episodes - CORRECTED
    const latestEpisodesResult = await Series.aggregate([
      { $match: { 'episodes.isLatestRelease': true } },
      { $unwind: '$episodes' },
      { $match: { 'episodes.isLatestRelease': true } },
      { $count: 'total' }
    ]);

    const totalEpisodes = totalEpisodesResult[0]?.total || 0;
    const latestEpisodes = latestEpisodesResult[0]?.total || 0;

    // Sample episode data - check multiple episodes
    let sampleEpisodes = [];
    if (sampleSeries && sampleSeries.episodes && sampleSeries.episodes.length > 0) {
      sampleEpisodes = sampleSeries.episodes.slice(0, 3).map(ep => ({
        season: ep.season,
        episode: ep.episode,
        episodeTitle: ep.episodeTitle,
        isLatestRelease: ep.isLatestRelease,
        hasIsLatestReleaseField: ep.hasOwnProperty('isLatestRelease'),
        embedUrl: ep.embedUrl,
        createdAt: ep.createdAt
      }));
    }

    // Check if ANY episodes have isLatestRelease field
    const episodeWithLatestFlag = await Series.findOne({
      'episodes.isLatestRelease': { $exists: true }
    }).lean();

    // Check if ANY episodes have isLatestRelease: true
    const episodeWithLatestTrue = await Series.findOne({
      'episodes.isLatestRelease': true
    }).lean();

    const debugInfo = {
      database: {
        totalSeries,
        totalSeriesTyped,
        seriesWithEpisodes,
        seriesWithLatestEpisodes,
        totalEmbeddedEpisodes: totalEpisodes,
        latestReleaseEpisodes: latestEpisodes
      },
      sampleSeries: sampleSeries ? {
        imdbId: sampleSeries.imdbId,
        title: sampleSeries.title,
        episodeCount: sampleSeries.episodes?.length || 0,
        lastEpisodeUpdate: sampleSeries.lastEpisodeUpdate
      } : null,
      sampleEpisodes,
      hasEpisodesWithLatestFlag: !!episodeWithLatestFlag,
      hasEpisodesWithLatestTrue: !!episodeWithLatestTrue,
      issues: {
        noSeries: totalSeries === 0,
        noEpisodes: totalEpisodes === 0,
        noLatestEpisodes: latestEpisodes === 0,
        missingLatestFlag: totalEpisodes > 0 && latestEpisodes === 0
      }
    };

    return NextResponse.json(debugInfo);

  } catch (error) {
    console.error('❌ Debug database state error:', error);
    return NextResponse.json(
      { error: 'Failed to check database state', message: error.message },
      { status: 500 }
    );
  }
}
