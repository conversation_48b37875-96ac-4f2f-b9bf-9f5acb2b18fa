'use client';

import { useEffect } from 'react';

const SyncInitializer: React.FC = () => {
  useEffect(() => {
    // Initialize VidSrc SCHEDULER (not sync) - runs every 3 hours automatically
    const initializeScheduler = async () => {
      try {
        console.log('🕐 Initializing VidSrc Scheduler...');

        // First, initialize the scheduler
        const initResponse = await fetch('/api/admin/init-scheduler', {
          method: 'GET',
        });

        if (initResponse.ok) {
          const result = await initResponse.json();
          console.log('✅ VidSrc Scheduler initialized:', result.data);

          if (result.data.hoursUntilNext !== undefined) {
            console.log(`📅 Next automatic sync in ${result.data.hoursUntilNext} hours`);
            console.log(`🔧 Manual sync available at: /api/admin/sync-vidsrc`);
          }
        } else {
          console.warn('⚠️ VidSrc Scheduler initialization failed:', initResponse.status);
        }
      } catch (error) {
        console.error('❌ Failed to initialize VidSrc Scheduler:', error);
      }
    };

    initializeScheduler();
  }, []);

  return null; // This component doesn't render anything
};

export default SyncInitializer;
