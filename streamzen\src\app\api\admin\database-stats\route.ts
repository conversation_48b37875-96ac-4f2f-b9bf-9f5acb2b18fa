import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

export async function GET() {
  try {
    await connectDB();
    
    // Get basic counts
    const totalSeries = await Series.countDocuments({});
    const totalEpisodes = await Episode.countDocuments({});
    
    // Get unique series in episodes
    const uniqueSeriesInEpisodes = await Episode.distinct('imdbId');
    
    // Get recent episodes (last 24 hours)
    const recentEpisodes = await Episode.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });
    
    // Get sample episodes with creation dates
    const sampleEpisodes = await Episode.find({})
      .sort({ createdAt: -1 })
      .limit(10)
      .select('seriesTitle season episode imdbId createdAt embedUrl')
      .lean();
    
    // Get sample series
    const sampleSeries = await Series.find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title imdbId createdAt type')
      .lean();
    
    // Check for VidSrc-style embed URLs
    const vidsrcEpisodes = await Episode.countDocuments({
      embedUrl: { $regex: /vidsrc\.me/ }
    });
    
    // Get oldest and newest episodes
    const oldestEpisode = await Episode.findOne({}).sort({ createdAt: 1 }).select('seriesTitle createdAt');
    const newestEpisode = await Episode.findOne({}).sort({ createdAt: -1 }).select('seriesTitle createdAt');
    
    return NextResponse.json({
      success: true,
      stats: {
        totalSeries,
        totalEpisodes,
        uniqueSeriesInEpisodes: uniqueSeriesInEpisodes.length,
        recentEpisodes,
        vidsrcEpisodes,
        oldestEpisode,
        newestEpisode
      },
      samples: {
        episodes: sampleEpisodes.map(ep => ({
          ...ep,
          _id: ep._id.toString()
        })),
        series: sampleSeries.map(s => ({
          ...s,
          _id: s._id.toString()
        }))
      }
    });
    
  } catch (error) {
    console.error('❌ Database stats error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get database stats',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
