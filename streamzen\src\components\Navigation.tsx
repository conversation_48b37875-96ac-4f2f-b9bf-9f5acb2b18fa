'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Search, Home, Film, Tv, Calendar, Plus, Play, X, Menu, Sparkles, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { cn } from '@/lib/utils';
import SearchBar from './SearchBar';

const Navigation: React.FC = () => {
  const pathname = usePathname();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { scrollY } = useScroll();

  // Transform scroll position to background opacity
  const backgroundOpacity = useTransform(scrollY, [0, 100], [0.8, 0.95]);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { href: '/', label: 'Home', icon: Home, badge: null },
    { href: '/movies', label: 'Movies', icon: Film, badge: null },
    { href: '/series', label: 'Series', icon: Tv, badge: null },
    { href: '/episodes', label: 'Episodes', icon: Calendar, badge: 'New' },
  ];

  return (
    <motion.nav
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-500",
        isScrolled
          ? "backdrop-blur-2xl bg-black/95 border-b border-white/10 shadow-2xl"
          : "backdrop-blur-xl bg-black/80 border-b border-white/5"
      )}
    >
      <div className="max-w-[2560px] mx-auto px-6 lg:px-12">
        <div className="flex items-center justify-between h-20">
          {/* Enhanced Logo with Animation */}
          <Link href="/" className="flex items-center space-x-4 focus-ring rounded-2xl px-3 py-2 transition-all duration-300 hover:bg-white/5 group">
            <motion.div
              className="relative"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl transition-all duration-300 group-hover:shadow-red-500/25">
                <Play size={24} className="text-white fill-current ml-1" />
              </div>
              <div className="absolute -inset-1 bg-gradient-to-br from-red-500/20 to-red-600/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-300" />

              {/* Floating sparkles */}
              <motion.div
                className="absolute -top-1 -right-1 w-3 h-3"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Sparkles size={12} className="text-yellow-400" />
              </motion.div>
            </motion.div>

            <div className="hidden sm:block">
              <motion.h1
                className="text-white text-2xl font-black tracking-tight"
                whileHover={{ scale: 1.02 }}
              >
                free<span className="text-red-500">Movies</span>WatchNow
              </motion.h1>
              <p className="text-gray-400 text-sm font-medium -mt-1 flex items-center space-x-1">
                <span>Premium Entertainment</span>
                <TrendingUp size={12} className="text-green-400" />
              </p>
            </div>
          </Link>

          {/* Enhanced Navigation Links */}
          <div className="hidden lg:flex items-center space-x-2">
            {navItems.map((item, index) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;

              return (
                <motion.div
                  key={item.href}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className={cn(
                      'relative flex items-center space-x-3 px-6 py-3 rounded-2xl transition-all duration-300 focus-ring group',
                      isActive
                        ? 'bg-gradient-to-r from-red-500/20 to-red-600/20 text-white shadow-lg border border-red-500/30 backdrop-blur-sm'
                        : 'text-gray-300 hover:text-white hover:bg-white/10'
                    )}
                  >
                    <Icon size={20} className={cn(
                      'transition-all duration-300',
                      isActive ? 'text-red-400' : 'group-hover:text-white'
                    )} />
                    <span className={cn(
                      'text-base font-semibold tracking-wide transition-all duration-300',
                      isActive ? 'text-white' : 'group-hover:text-white'
                    )}>
                      {item.label}
                    </span>

                    {/* Badge for new features */}
                    {item.badge && (
                      <motion.div
                        className="px-2 py-0.5 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-bold rounded-full"
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        {item.badge}
                      </motion.div>
                    )}

                    {/* Enhanced Active Indicator */}
                    {isActive && (
                      <motion.div
                        className="absolute bottom-0 left-1/2 -translate-x-1/2 w-1 h-1 bg-red-500 rounded-full shadow-lg shadow-red-500/50"
                        layoutId="activeIndicator"
                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                      />
                    )}

                    {/* Hover glow effect */}
                    <div className={cn(
                      'absolute inset-0 rounded-2xl transition-all duration-300 pointer-events-none',
                      isActive
                        ? 'shadow-[inset_0_0_20px_rgba(239,68,68,0.1)]'
                        : 'group-hover:shadow-[inset_0_0_20px_rgba(255,255,255,0.05)]'
                    )} />
                  </Link>
                </motion.div>
              );
            })}
          </div>

          {/* Ultra Premium Actions */}
          <div className="flex items-center space-x-4">
            {/* Premium Search Button */}
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group"
            >
              {isSearchOpen ? (
                <X size={24} className="group-hover:scale-110 transition-transform duration-300" />
              ) : (
                <Search size={24} className="group-hover:scale-110 transition-transform duration-300" />
              )}
            </button>

            {/* Apple TV + Netflix Style Request Button */}
            <Link
              href="/request"
              className="hidden sm:flex items-center space-x-3 px-8 py-4 bg-red-600 rounded-2xl text-white hover:bg-red-700 transition-all duration-300 focus-ring font-semibold shadow-2xl hover:scale-105"
            >
              <Plus size={20} />
              <span className="text-lg">Request</span>
            </Link>

            {/* Premium Mobile Menu Button */}
            <button className="lg:hidden p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group">
              <div className="w-6 h-6 flex flex-col justify-center space-y-1.5">
                <div className="w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4" />
                <div className="w-full h-0.5 bg-current rounded" />
                <div className="w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4 group-hover:ml-auto" />
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Premium Mobile Navigation */}
      <div className="lg:hidden border-t border-white/5 glass">
        <div className="flex items-center justify-around py-4 px-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex flex-col items-center space-y-2 px-4 py-3 rounded-xl transition-all duration-300 focus-ring',
                  isActive
                    ? 'text-white bg-white/15'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                )}
              >
                <Icon size={20} />
                <span className="text-xs font-medium">{item.label}</span>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Enhanced Search Overlay */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="absolute top-full left-0 right-0 backdrop-blur-2xl bg-black/95 border-b border-white/10 shadow-2xl z-40"
          >
            <div className="max-w-[2560px] mx-auto px-6 lg:px-12 py-8">
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.1, duration: 0.3 }}
                className="max-w-2xl mx-auto"
              >
                <SearchBar
                  placeholder="Search movies, series, episodes..."
                  className="w-full"
                  onSearch={() => setIsSearchOpen(false)}
                />
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navigation;
