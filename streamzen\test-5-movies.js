const XLSX = require('xlsx');
const path = require('path');
const mongoose = require('mongoose');
const axios = require('axios');
const cheerio = require('cheerio');

// Test scraper for just 5 movies
class TestScraper {
  constructor() {
    this.processedCount = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.startTime = Date.now();
    this.results = {
      movies: [],
      series: [],
      errors: []
    };
  }

  async initialize() {
    console.log('🧪 Initializing Test Scraper for 5 movies...');

    // Load environment variables
    require('dotenv').config({ path: '.env.local' });

    // Connect to MongoDB
    if (!mongoose.connection.readyState) {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
      console.log('🔗 Connecting to MongoDB...');
      await mongoose.connect(mongoUri);
      console.log('✅ Connected to MongoDB');
    }

    // Define schemas inline
    const movieSchema = new mongoose.Schema({
      imdbId: { type: String, required: true, unique: true },
      title: { type: String, required: true },
      year: { type: Number, required: true },
      description: String,
      genres: [String],
      cast: [String],
      director: String,
      language: String,
      country: String,
      imdbRating: Number,
      imdbVotes: String,
      posterUrl: String,
      embedUrl: String,
      vidsrcUrl: String,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    const seriesSchema = new mongoose.Schema({
      imdbId: { type: String, required: true, unique: true },
      title: { type: String, required: true },
      startYear: { type: Number, required: true },
      endYear: Number,
      description: String,
      genres: [String],
      cast: [String],
      creator: String,
      language: String,
      country: String,
      imdbRating: Number,
      imdbVotes: String,
      posterUrl: String,
      embedUrl: String,
      vidsrcUrl: String,
      totalSeasons: Number,
      status: String,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    // Create models
    this.Movie = mongoose.models.Movie || mongoose.model('Movie', movieSchema);
    this.Series = mongoose.models.Series || mongoose.model('Series', seriesSchema);
  }

  async loadSampleIds() {
    console.log('📊 Loading 5 sample IMDb IDs from Excel...');
    
    const excelPath = path.join(__dirname, 'imdb_movie_ids_final.xlsx');
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    
    const allImdbIds = data.map(row => row.TitleID).filter(id => id && id.startsWith('tt'));
    
    // Take first 5 for testing
    const sampleIds = allImdbIds.slice(0, 5);
    
    console.log(`✅ Selected ${sampleIds.length} sample IDs for testing:`);
    sampleIds.forEach((id, index) => {
      console.log(`${index + 1}. ${id}`);
    });
    
    return sampleIds;
  }

  getRandomUserAgent() {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  }

  async fetchPage(imdbId) {
    const url = `https://www.imdb.com/title/${imdbId}/`;
    const headers = {
      'User-Agent': this.getRandomUserAgent(),
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
    };

    const response = await axios.get(url, { headers, timeout: 30000 });
    return cheerio.load(response.data);
  }

  extractBasicInfo($) {
    const titleElement = $('h1[data-testid="hero__pageTitle"] span[data-testid="hero__primary-text"]');
    const title = titleElement.text().trim();
    
    if (!title) {
      throw new Error('Could not extract title from IMDb page');
    }

    const yearElement = $('ul.ipc-inline-list a[href*="/releaseinfo/"]');
    const yearText = yearElement.text().trim();
    const year = parseInt(yearText) || new Date().getFullYear();

    const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();
    const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');
    
    return { title, year, type: isMovie ? 'movie' : 'series' };
  }

  extractGenres($) {
    const genres = [];

    // Updated genre selectors based on current IMDb structure (same as working scraper)
    const genreSelectors = [
      // Current IMDb hero section genres (most common)
      '[data-testid="genres"] .ipc-chip .ipc-chip__text',
      '[data-testid="genres"] .ipc-chip__text',
      '[data-testid="genres"] a .ipc-chip__text',

      // Hero section alternative formats
      '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',
      '.GenresAndPlot__GenreChip .ipc-chip__text',

      // Storyline section (your provided structure - keep as fallback)
      'li[data-testid="storyline-genres"] .ipc-metadata-list-item__list-content-item',
      'li[data-testid="storyline-genres"] a[href*="genres="]',

      // General genre link selectors (broader search)
      'a[href*="/search/title/?genres="] span',
      'a[href*="genres="] span',
      'a[href*="genres="]',

      // Schema.org microdata
      '[itemprop="genre"]',
      'span[itemprop="genre"]',

      // Fallback selectors
      '.see-more.inline.canwrap a[href*="genres="]',
      '.titlePageSprite.star-box-giga-star + div a[href*="genres="]',

      // Very broad fallback - any link with genre in URL
      'a[href*="explore=genres"]'
    ];

    console.log(`🔍 Starting genre extraction for IMDb page...`);

    for (let i = 0; i < genreSelectors.length; i++) {
      const selector = genreSelectors[i];
      const elements = $(selector);
      console.log(`🔍 Selector ${i + 1}/${genreSelectors.length}: "${selector}" found ${elements.length} elements`);

      if (elements.length > 0) {
        elements.each((_, element) => {
          const genre = $(element).text().trim();
          console.log(`📝 Found genre text: "${genre}"`);

          // Clean up genre text and validate
          if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {
            // Skip common non-genre text
            const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];
            if (!skipTexts.includes(genre)) {
              genres.push(genre);
              console.log(`✅ Added genre: "${genre}"`);
            }
          }
        });

        if (genres.length > 0) {
          console.log(`✅ Successfully extracted ${genres.length} genres: [${genres.join(', ')}]`);
          break; // Use the first selector that finds results
        }
      }
    }

    if (genres.length === 0) {
      console.log('⚠️ No genres found with any selector');
      // Debug: Let's see what's actually in the storyline section
      const storylineSection = $('li[data-testid="storyline-genres"]');
      if (storylineSection.length > 0) {
        console.log('📋 Storyline section HTML:', storylineSection.html());
      } else {
        console.log('❌ No storyline-genres section found');
      }
    }

    return genres;
  }

  extractCast($) {
    const cast = [];
    const castSelectors = [
      'section[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
      '.cast_list .primary_photo + td a'
    ];

    for (const selector of castSelectors) {
      const elements = $(selector);
      if (elements.length > 0) {
        elements.each((_, element) => {
          const actorName = $(element).text().trim();
          if (actorName && !cast.includes(actorName) && cast.length < 10) {
            cast.push(actorName);
          }
        });
        break;
      }
    }
    return cast;
  }

  extractIMDbRating($) {
    const ratingElement = $('div[data-testid="hero-rating-bar__aggregate-rating__score"] span');
    const rating = parseFloat(ratingElement.text().trim()) || undefined;
    
    const votesElement = $('div.sc-d541859f-3');
    const votes = votesElement.text().trim() || undefined;
    
    return { rating, votes };
  }

  extractDescription($) {
    const selectors = [
      'span[data-testid="plot-xl"]',
      'span[data-testid="plot-l"]',
      'span[data-testid="plot"]'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const text = element.text().trim();
      if (text && text.length > 10) return text;
    }
    return undefined;
  }

  extractRating($) {
    const ratingElement = $('ul.ipc-inline-list a[href*="/parentalguide/"]');
    return ratingElement.text().trim() || undefined;
  }

  extractRuntime($) {
    const runtimeElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < runtimeElements.length; i++) {
      const text = $(runtimeElements[i]).text().trim();
      if (text.includes('h') || text.includes('min')) {
        return text;
      }
    }
    return undefined;
  }

  extractDirector($) {
    const directorSelectors = [
      'li[data-testid="title-pc-principal-credit"]:contains("Director") .ipc-metadata-list-item__list-content-item',
      'li[data-testid="title-pc-principal-credit"]:contains("Directors") .ipc-metadata-list-item__list-content-item',
      'a[href*="/name/"][href*="ref_=tt_ov_dr"]',
      '.credit_summary_item:contains("Director") a'
    ];

    for (const selector of directorSelectors) {
      const element = $(selector).first();
      const director = element.text().trim();
      if (director) return director;
    }
    return undefined;
  }

  extractCreator($) {
    const creatorSelectors = [
      'li[data-testid="title-pc-principal-credit"]:contains("Creator") .ipc-metadata-list-item__list-content-item',
      'li[data-testid="title-pc-principal-credit"]:contains("Creators") .ipc-metadata-list-item__list-content-item',
      '.credit_summary_item:contains("Creator") a',
      '.credit_summary_item:contains("Created by") a'
    ];

    for (const selector of creatorSelectors) {
      const element = $(selector).first();
      const creator = element.text().trim();
      if (creator) return creator;
    }
    return undefined;
  }

  extractLanguage($) {
    const languageSelectors = [
      'li[data-testid="title-details-languages"] .ipc-metadata-list-item__list-content-item',
      'div[data-testid="title-details-section"] li:contains("Language") .ipc-metadata-list-item__list-content-item',
      'a[href*="primary_language="]',
      '.txt-block:contains("Language") a'
    ];

    for (const selector of languageSelectors) {
      const element = $(selector).first();
      const language = element.text().trim();
      if (language) return language;
    }
    return undefined;
  }

  extractCountry($) {
    const countrySelectors = [
      'li[data-testid="title-details-origin"] .ipc-metadata-list-item__list-content-item',
      'div[data-testid="title-details-section"] li:contains("Country") .ipc-metadata-list-item__list-content-item',
      'a[href*="country_of_origin="]',
      '.txt-block:contains("Country") a'
    ];

    for (const selector of countrySelectors) {
      const element = $(selector).first();
      const country = element.text().trim();
      if (country) return country;
    }
    return undefined;
  }

  extractPosterUrl($) {
    const selectors = [
      'div[data-testid="hero-media__poster"] img',
      '.ipc-image[data-testid="hero-media__poster"]',
      '.poster img',
      '.ipc-media img',
      'img[class*="poster"]',
      'a[class*="ipc-lockup-overlay"] img'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const src = element.attr('src');
      if (src && src.includes('media-amazon.com')) {
        return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1000_.').replace(/\._.*?\./, '._V1_FMjpg_UX1000_.');
      }
    }
    return undefined;
  }

  generateVidSrcUrl(imdbId, type) {
    if (type === 'movie') {
      return `https://vidsrc.me/embed/movie?imdb=${imdbId}`;
    } else {
      return `https://vidsrc.me/embed/tv?imdb=${imdbId}&season=1&episode=1`;
    }
  }

  async scrapeContent(imdbId) {
    try {
      console.log(`🔍 Scraping ${imdbId}...`);

      const $ = await this.fetchPage(imdbId);
      const basicInfo = this.extractBasicInfo($);
      const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);

      const commonData = {
        imdbId,
        title: basicInfo.title,
        year: basicInfo.year,
        rating: this.extractRating($), // MPAA rating
        runtime: this.extractRuntime($),
        imdbRating,
        imdbVotes,
        description: this.extractDescription($),
        genres: this.extractGenres($),
        cast: this.extractCast($),
        director: basicInfo.type === 'movie' ? this.extractDirector($) : undefined,
        creator: basicInfo.type === 'series' ? this.extractCreator($) : undefined,
        language: this.extractLanguage($),
        country: this.extractCountry($),
        posterUrl: this.extractPosterUrl($),
        embedUrl: this.generateVidSrcUrl(imdbId, basicInfo.type),
        vidsrcUrl: this.generateVidSrcUrl(imdbId, basicInfo.type),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Add type-specific fields
      if (basicInfo.type === 'movie') {
        commonData.releaseDate = new Date(basicInfo.year, 0, 1);
      } else {
        commonData.startYear = basicInfo.year;
        commonData.totalSeasons = 1; // Default
        commonData.status = 'ongoing'; // Default
      }

      console.log(`✅ ${imdbId} identified as ${basicInfo.type.toUpperCase()}: "${basicInfo.title}" (${basicInfo.year})`);
      console.log(`   Rating: ${commonData.rating || 'N/A'} | Runtime: ${commonData.runtime || 'N/A'}`);
      console.log(`   IMDb Rating: ${commonData.imdbRating || 'N/A'} | Language: ${commonData.language || 'N/A'}`);
      console.log(`   Genres: ${commonData.genres.join(', ') || 'N/A'}`);
      console.log(`   Cast: ${commonData.cast.slice(0, 3).join(', ') || 'N/A'}`);
      console.log(`   Director/Creator: ${commonData.director || commonData.creator || 'N/A'}`);
      console.log(`   VidSrc URL: ${commonData.vidsrcUrl}`);

      return {
        success: true,
        type: basicInfo.type,
        data: commonData
      };
    } catch (error) {
      console.error(`❌ Error scraping ${imdbId}:`, error.message);
      return {
        success: false,
        error: error.message,
        imdbId
      };
    }
  }

  async run() {
    try {
      await this.initialize();
      
      const sampleIds = await this.loadSampleIds();
      
      console.log(`\\n🚀 Starting test scraping of ${sampleIds.length} sample items...\\n`);
      
      // Process each ID sequentially for testing
      for (let i = 0; i < sampleIds.length; i++) {
        const imdbId = sampleIds[i];
        console.log(`\\n[${i + 1}/${sampleIds.length}] Processing ${imdbId}...`);
        
        const result = await this.scrapeContent(imdbId);
        
        this.processedCount++;
        
        if (result.success) {
          this.successCount++;
          if (result.type === 'movie') {
            this.results.movies.push(result.data);
            
            // Save to database
            await this.Movie.findOneAndUpdate(
              { imdbId },
              result.data,
              { upsert: true, new: true }
            );
            console.log(`💾 Saved movie to database: ${result.data.title}`);
          } else {
            this.results.series.push(result.data);
            
            // Save to database
            await this.Series.findOneAndUpdate(
              { imdbId },
              result.data,
              { upsert: true, new: true }
            );
            console.log(`💾 Saved series to database: ${result.data.title}`);
          }
        } else {
          this.errorCount++;
          this.results.errors.push(result);
        }
        
        // Add delay between requests
        if (i < sampleIds.length - 1) {
          console.log('⏳ Waiting 3 seconds before next request...');
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
      
      const totalTime = (Date.now() - this.startTime) / 1000;
      
      // Generate report
      console.log('\\n🎯 TEST SCRAPING REPORT');
      console.log('========================');
      console.log(`📊 Total Processed: ${this.processedCount}`);
      console.log(`✅ Successful: ${this.successCount}`);
      console.log(`❌ Errors: ${this.errorCount}`);
      console.log(`🎬 Movies Found: ${this.results.movies.length}`);
      console.log(`📺 Series Found: ${this.results.series.length}`);
      console.log(`⏱️  Total Time: ${Math.round(totalTime)}s`);
      console.log('========================\\n');
      
      if (this.results.errors.length > 0) {
        console.log('❌ Errors:');
        this.results.errors.forEach(error => {
          console.log(`${error.imdbId}: ${error.error}`);
        });
      }
      
      console.log('✅ Test completed successfully!');
      console.log('🚀 Ready to run full parallel scraper on all 18,950 items!');
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      await mongoose.disconnect();
    }
  }
}

// Run the test
async function main() {
  const scraper = new TestScraper();
  await scraper.run();
  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = TestScraper;
