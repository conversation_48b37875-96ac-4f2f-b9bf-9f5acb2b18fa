'use client';

import React from 'react';
import SearchBar from './SearchBar';

interface ClientSearchBarProps {
  placeholder?: string;
  className?: string;
  showSuggestions?: boolean;
}

const ClientSearchBar: React.FC<ClientSearchBarProps> = ({
  placeholder = "Search movies, series, episodes...",
  className = "",
  showSuggestions = true
}) => {
  return (
    <SearchBar
      placeholder={placeholder}
      className={className}
      showSuggestions={showSuggestions}
    />
  );
};

export default ClientSearchBar;
