{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Series.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\n// Episode interface for embedded episodes within series\nexport interface IEpisode {\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  description?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  posterUrl?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl: string;\n  vidsrcTmdbUrl?: string;\n  isLatestRelease?: boolean; // ✅ CRITICAL: VidSrc latest release flag\n  createdAt?: Date;\n  updatedAt?: Date;\n}\n\nexport interface ISeries extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string; // MPAA rating\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string; // 'ongoing', 'ended', 'cancelled'\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection\n  episodes: IEpisode[];\n  episodeCount?: number; // Cache for quick access\n  lastEpisodeUpdate?: Date; // Track when episodes were last synced\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst SeriesSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: {\n    type: String,\n    required: true\n  },\n  startYear: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  endYear: { \n    type: Number,\n    index: true \n  },\n  rating: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  creator: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  totalSeasons: Number,\n  status: { \n    type: String,\n    enum: ['ongoing', 'ended', 'cancelled'],\n    index: true \n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection\n  episodes: [{\n    season: { type: Number, required: true },\n    episode: { type: Number, required: true },\n    episodeTitle: String,\n    description: String,\n    airDate: Date,\n    runtime: { type: String, default: '45 min' },\n    imdbRating: Number,\n    posterUrl: String,\n    embedUrl: { type: String, required: true },\n    embedUrlTmdb: String,\n    vidsrcUrl: { type: String, required: true },\n    vidsrcTmdbUrl: String,\n    isLatestRelease: { type: Boolean, default: false, index: true }, // ✅ CRITICAL: VidSrc latest release flag\n    createdAt: { type: Date, default: Date.now },\n    updatedAt: { type: Date, default: Date.now }\n  }],\n  episodeCount: { type: Number, default: 0 }, // Cache for quick access\n  lastEpisodeUpdate: Date // Track when episodes were last synced\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nSeriesSchema.index({ startYear: -1, imdbRating: -1 });\nSeriesSchema.index({ genres: 1, startYear: -1 });\nSeriesSchema.index({ status: 1, startYear: -1 });\n// Removed text index to avoid language override issues\nSeriesSchema.index({ title: 1 });\nSeriesSchema.index({ language: 1, country: 1 });\n// 🆕 EPISODE-SPECIFIC INDEXES for embedded episodes\nSeriesSchema.index({ 'episodes.season': 1, 'episodes.episode': 1 });\nSeriesSchema.index({ 'episodes.isLatestRelease': 1 }); // ✅ CRITICAL: For VidSrc latest episodes queries\nSeriesSchema.index({ lastEpisodeUpdate: -1 }); // For sync tracking\nSeriesSchema.index({ episodeCount: -1 }); // For quick episode count queries\n\nexport default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAsDA,MAAM,eAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;IACR,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,SAAS;IACT,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,cAAc;IACd,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAS;SAAY;QACvC,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,oEAAoE;IACpE,UAAU;QAAC;YACT,QAAQ;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACvC,SAAS;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACxC,cAAc;YACd,aAAa;YACb,SAAS;YACT,SAAS;gBAAE,MAAM;gBAAQ,SAAS;YAAS;YAC3C,YAAY;YACZ,WAAW;YACX,UAAU;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACzC,cAAc;YACd,WAAW;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YAC1C,eAAe;YACf,iBAAiB;gBAAE,MAAM;gBAAS,SAAS;gBAAO,OAAO;YAAK;YAC9D,WAAW;gBAAE,MAAM;gBAAM,SAAS,KAAK,GAAG;YAAC;YAC3C,WAAW;gBAAE,MAAM;gBAAM,SAAS,KAAK,GAAG;YAAC;QAC7C;KAAE;IACF,cAAc;QAAE,MAAM;QAAQ,SAAS;IAAE;IACzC,mBAAmB,KAAK,uCAAuC;AACjE,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,aAAa,KAAK,CAAC;IAAE,WAAW,CAAC;IAAG,YAAY,CAAC;AAAE;AACnD,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,uDAAuD;AACvD,aAAa,KAAK,CAAC;IAAE,OAAO;AAAE;AAC9B,aAAa,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;AAC7C,oDAAoD;AACpD,aAAa,KAAK,CAAC;IAAE,mBAAmB;IAAG,oBAAoB;AAAE;AACjE,aAAa,KAAK,CAAC;IAAE,4BAA4B;AAAE,IAAI,iDAAiD;AACxG,aAAa,KAAK,CAAC;IAAE,mBAAmB,CAAC;AAAE,IAAI,oBAAoB;AACnE,aAAa,KAAK,CAAC;IAAE,cAAc,CAAC;AAAE,IAAI,kCAAkC;uCAE7D,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Episode.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IEpisode extends Document {\n  imdbId: string; // Series IMDb ID\n  tmdbId?: string; // Series TMDB ID\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  genres?: string[]; // Genres inherited from series\n  language?: string; // Language inherited from series\n  country?: string; // Country inherited from series\n  posterUrl?: string; // Poster inherited from series\n  isLatestRelease?: boolean; // Track if episode is from VidSrc latest\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst EpisodeSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  seriesTitle: { \n    type: String, \n    required: true,\n    index: true \n  },\n  season: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episode: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episodeTitle: String,\n  airDate: { \n    type: Date,\n    index: true \n  },\n  runtime: String,\n  imdbRating: Number,\n  description: String,\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  },\n  genres: [{\n    type: String,\n    index: true\n  }],\n  language: {\n    type: String,\n    index: true\n  },\n  country: {\n    type: String,\n    index: true\n  },\n  posterUrl: String,\n  isLatestRelease: {\n    type: Boolean,\n    default: false,\n    index: true\n  }\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nEpisodeSchema.index({ imdbId: 1, season: 1, episode: 1 }, { unique: true });\nEpisodeSchema.index({ airDate: -1 });\nEpisodeSchema.index({ seriesTitle: 1, season: 1, episode: 1 });\nEpisodeSchema.index({ createdAt: -1 }); // For latest episodes\n\nexport default mongoose.models.Episode || mongoose.model<IEpisode>('Episode', EpisodeSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA2BA,MAAM,gBAAwB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACvC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,cAAc;IACd,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,SAAS;IACT,YAAY;IACZ,aAAa;IACb,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,iBAAiB;QACf,MAAM;QACN,SAAS;QACT,OAAO;IACT;AACF,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,cAAc,KAAK,CAAC;IAAE,QAAQ;IAAG,QAAQ;IAAG,SAAS;AAAE,GAAG;IAAE,QAAQ;AAAK;AACzE,cAAc,KAAK,CAAC;IAAE,SAAS,CAAC;AAAE;AAClC,cAAc,KAAK,CAAC;IAAE,aAAa;IAAG,QAAQ;IAAG,SAAS;AAAE;AAC5D,cAAc,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE,IAAI,sBAAsB;uCAE/C,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAW,WAAW", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/vidsrc.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst VIDSRC_BASE_URL = process.env.VIDSRC_BASE_URL || 'https://vidsrc.xyz';\n\n// Multiple streaming sources for better reliability\nexport const STREAMING_SOURCES = {\n  vidsrc_xyz: {\n    name: 'VidSrc XYZ',\n    baseUrl: 'https://vidsrc.xyz',\n    quality: 'HD',\n    priority: 1\n  },\n  autoembed: {\n    name: 'AutoEmbed',\n    baseUrl: 'https://player.autoembed.cc',\n    quality: 'Premium HD',\n    priority: 2\n  },\n  vidsrc_icu: {\n    name: 'VidSrc ICU',\n    baseUrl: 'https://vidsrc.icu',\n    quality: 'HD',\n    priority: 3\n  },\n  vidsrc_cc_v2: {\n    name: 'VidSrc CC v2',\n    baseUrl: 'https://vidsrc.cc/v2',\n    quality: 'HD',\n    priority: 4\n  },\n  vidsrc_cc_v3: {\n    name: 'VidSrc CC v3',\n    baseUrl: 'https://vidsrc.cc/v3',\n    quality: 'HD',\n    priority: 5\n  }\n};\n\nexport interface VidSrcMovieData {\n  imdb_id: string;\n  tmdb_id?: string;\n  title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\nexport interface VidSrcSeriesData {\n  imdb_id: string;\n  tmdb_id?: string;\n  show_title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n}\n\nexport interface VidSrcEpisodeData {\n  imdb_id: string;\n  tmdb_id?: string;\n  show_title: string;\n  season: string;\n  episode: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\nexport interface VidSrcLatestResponse<T> {\n  result: T[];\n}\n\nclass VidSrcAPI {\n  private static instance: VidSrcAPI;\n\n  static getInstance(): VidSrcAPI {\n    if (!VidSrcAPI.instance) {\n      VidSrcAPI.instance = new VidSrcAPI();\n    }\n    return VidSrcAPI.instance;\n  }\n\n  /**\n   * Generate movie embed URLs for all sources\n   */\n  generateAllMovieEmbedUrls(imdbId: string, tmdbId?: string): Array<{\n    source: string;\n    name: string;\n    url: string;\n    quality: string;\n    priority: number;\n  }> {\n    const cleanImdbId = imdbId.replace('tt', '');\n    const urls = [];\n\n    // VidSrc XYZ\n    urls.push({\n      source: 'vidsrc_xyz',\n      name: STREAMING_SOURCES.vidsrc_xyz.name,\n      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/movie?imdb=${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_xyz.quality,\n      priority: STREAMING_SOURCES.vidsrc_xyz.priority\n    });\n\n    // AutoEmbed\n    urls.push({\n      source: 'autoembed',\n      name: STREAMING_SOURCES.autoembed.name,\n      url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.autoembed.quality,\n      priority: STREAMING_SOURCES.autoembed.priority\n    });\n\n    // VidSrc ICU\n    urls.push({\n      source: 'vidsrc_icu',\n      name: STREAMING_SOURCES.vidsrc_icu.name,\n      url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_icu.quality,\n      priority: STREAMING_SOURCES.vidsrc_icu.priority\n    });\n\n    // VidSrc CC v2\n    urls.push({\n      source: 'vidsrc_cc_v2',\n      name: STREAMING_SOURCES.vidsrc_cc_v2.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority\n    });\n\n    // VidSrc CC v3\n    urls.push({\n      source: 'vidsrc_cc_v3',\n      name: STREAMING_SOURCES.vidsrc_cc_v3.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority\n    });\n\n    return urls.sort((a, b) => a.priority - b.priority);\n  }\n\n  /**\n   * Generate movie embed URL (legacy method for backward compatibility)\n   */\n  generateMovieEmbedUrl(imdbId: string, options?: {\n    tmdbId?: string;\n    subUrl?: string;\n    dsLang?: string;\n    autoplay?: boolean;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/movie`;\n    const params = new URLSearchParams();\n\n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n\n    if (options?.subUrl) {\n      params.append('sub_url', encodeURIComponent(options.subUrl));\n    }\n\n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n\n    if (options?.autoplay !== undefined) {\n      params.append('autoplay', options.autoplay ? '1' : '0');\n    }\n\n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Generate episode embed URLs for all sources\n   */\n  generateAllEpisodeEmbedUrls(imdbId: string, season: number, episode: number, tmdbId?: string): Array<{\n    source: string;\n    name: string;\n    url: string;\n    quality: string;\n    priority: number;\n  }> {\n    const cleanImdbId = imdbId.replace('tt', '');\n    const urls = [];\n\n    // VidSrc XYZ\n    urls.push({\n      source: 'vidsrc_xyz',\n      name: STREAMING_SOURCES.vidsrc_xyz.name,\n      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/tv?imdb=${imdbId}&season=${season}&episode=${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_xyz.quality,\n      priority: STREAMING_SOURCES.vidsrc_xyz.priority\n    });\n\n    // AutoEmbed\n    urls.push({\n      source: 'autoembed',\n      name: STREAMING_SOURCES.autoembed.name,\n      url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.autoembed.quality,\n      priority: STREAMING_SOURCES.autoembed.priority\n    });\n\n    // VidSrc ICU\n    urls.push({\n      source: 'vidsrc_icu',\n      name: STREAMING_SOURCES.vidsrc_icu.name,\n      url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_icu.quality,\n      priority: STREAMING_SOURCES.vidsrc_icu.priority\n    });\n\n    // VidSrc CC v2\n    urls.push({\n      source: 'vidsrc_cc_v2',\n      name: STREAMING_SOURCES.vidsrc_cc_v2.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority\n    });\n\n    // VidSrc CC v3\n    urls.push({\n      source: 'vidsrc_cc_v3',\n      name: STREAMING_SOURCES.vidsrc_cc_v3.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority\n    });\n\n    return urls.sort((a, b) => a.priority - b.priority);\n  }\n\n  /**\n   * Generate series embed URL (legacy method for backward compatibility)\n   */\n  generateSeriesEmbedUrl(imdbId: string, options?: {\n    tmdbId?: string;\n    dsLang?: string;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;\n    const params = new URLSearchParams();\n\n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n\n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n\n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Generate episode embed URL\n   */\n  generateEpisodeEmbedUrl(imdbId: string, season: number, episode: number, options?: {\n    tmdbId?: string;\n    subUrl?: string;\n    dsLang?: string;\n    autoplay?: boolean;\n    autonext?: boolean;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;\n    const params = new URLSearchParams();\n    \n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n    \n    params.append('season', season.toString());\n    params.append('episode', episode.toString());\n    \n    if (options?.subUrl) {\n      params.append('sub_url', encodeURIComponent(options.subUrl));\n    }\n    \n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n    \n    if (options?.autoplay !== undefined) {\n      params.append('autoplay', options.autoplay ? '1' : '0');\n    }\n    \n    if (options?.autonext !== undefined) {\n      params.append('autonext', options.autonext ? '1' : '0');\n    }\n    \n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Fetch latest movies from VidSrc\n   */\n  async getLatestMovies(page: number = 1): Promise<VidSrcMovieData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/movies/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcMovieData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest movies from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Fetch latest TV shows from VidSrc\n   */\n  async getLatestSeries(page: number = 1): Promise<VidSrcSeriesData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/tvshows/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcSeriesData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest series from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Fetch latest episodes from VidSrc\n   */\n  async getLatestEpisodes(page: number = 1, retries: number = 3): Promise<VidSrcEpisodeData[]> {\n    const url = `${VIDSRC_BASE_URL}/episodes/latest/page-${page}.json`;\n\n    for (let attempt = 1; attempt <= retries; attempt++) {\n      try {\n        console.log(`🔄 Fetching VidSrc episodes page ${page} (attempt ${attempt}/${retries})...`);\n        const response = await axios.get<VidSrcLatestResponse<VidSrcEpisodeData>>(url, {\n          timeout: 10000, // 10 second timeout\n          headers: {\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            'Accept': 'application/json',\n          }\n        });\n\n        const episodes = response.data.result || [];\n        console.log(`✅ Page ${page}: Found ${episodes.length} episodes`);\n        return episodes;\n\n      } catch (error: any) {\n        const isLastAttempt = attempt === retries;\n        const errorMsg = error.response?.status\n          ? `HTTP ${error.response.status} ${error.response.statusText}`\n          : error.message;\n\n        if (isLastAttempt) {\n          console.error(`❌ Failed to fetch page ${page} after ${retries} attempts: ${errorMsg}`);\n          return [];\n        } else {\n          console.warn(`⚠️ Attempt ${attempt} failed for page ${page}: ${errorMsg}. Retrying...`);\n          // Wait before retry (exponential backoff)\n          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Max 5 seconds\n          await new Promise(resolve => setTimeout(resolve, delay));\n        }\n      }\n    }\n\n    return [];\n  }\n\n  /**\n   * SIMPLE: Get episodes for a specific series from VidSrc (for embed URLs only)\n   * This is now used only to get streaming links, not for episode discovery\n   */\n  async getSeriesEpisodes(imdbId: string): Promise<Array<{\n    season: number;\n    episode: number;\n    embed_url: string;\n    embed_url_tmdb?: string;\n  }>> {\n    try {\n      console.log(`🔍 ADVANCED: Comprehensive episode search for series: ${imdbId}`);\n\n      const allEpisodes = new Map<string, {\n        season: number;\n        episode: number;\n        embed_url: string;\n        embed_url_tmdb?: string;\n      }>();\n\n      // Strategy 1: Search through ALL latest episodes (not just 15 pages)\n      console.log(`📡 Strategy 1: Scanning latest episodes across all pages...`);\n      let foundEpisodesInLatest = 0;\n      for (let page = 1; page <= 50; page++) { // Increased to 50 pages for comprehensive search\n        try {\n          const episodes = await this.getLatestEpisodes(page);\n          if (episodes.length === 0) break; // No more episodes\n\n          const seriesEpisodes = episodes.filter(episode => episode.imdb_id === imdbId);\n\n          seriesEpisodes.forEach(episode => {\n            const key = `S${episode.season}E${episode.episode}`;\n            if (!allEpisodes.has(key)) {\n              allEpisodes.set(key, {\n                season: episode.season,\n                episode: episode.episode,\n                embed_url: episode.embed_url,\n                embed_url_tmdb: episode.embed_url_tmdb\n              });\n              foundEpisodesInLatest++;\n            }\n          });\n\n          if (seriesEpisodes.length > 0) {\n            console.log(`📺 Page ${page}: Found ${seriesEpisodes.length} episodes`);\n          }\n\n          // If no episodes found in last 10 pages, likely reached the end\n          if (page > 10 && seriesEpisodes.length === 0) {\n            let emptyPages = 0;\n            for (let checkPage = page - 9; checkPage <= page; checkPage++) {\n              const checkEpisodes = await this.getLatestEpisodes(checkPage);\n              if (checkEpisodes.filter(ep => ep.imdb_id === imdbId).length === 0) {\n                emptyPages++;\n              }\n            }\n            if (emptyPages >= 8) break; // Stop if 8/10 recent pages are empty\n          }\n        } catch (error) {\n          console.error(`Error fetching episodes page ${page}:`, error);\n        }\n      }\n\n      console.log(`✅ Strategy 1 Complete: Found ${foundEpisodesInLatest} episodes in latest pages`);\n\n      // Strategy 2: Search through series-specific pages (if available)\n      console.log(`📡 Strategy 2: Searching series-specific endpoints...`);\n      let foundEpisodesInSeries = 0;\n      for (let page = 1; page <= 20; page++) {\n        try {\n          const seriesEpisodes = await this.getLatestSeries(page);\n          const matchingSeries = seriesEpisodes.filter(series => series.imdb_id === imdbId);\n\n          if (matchingSeries.length > 0) {\n            console.log(`📺 Series page ${page}: Found matching series, checking for episode data`);\n            // If series data includes episode information, extract it\n            // This is a placeholder for potential series-specific episode data\n          }\n        } catch (error) {\n          // Series endpoint might not exist, continue\n        }\n      }\n\n      // Strategy 3: Systematic season-by-season verification\n      console.log(`📡 Strategy 3: Systematic season verification...`);\n      const episodesBySeason = new Map<number, number[]>();\n\n      // Group found episodes by season\n      allEpisodes.forEach((episode, key) => {\n        if (!episodesBySeason.has(episode.season)) {\n          episodesBySeason.set(episode.season, []);\n        }\n        episodesBySeason.get(episode.season)!.push(episode.episode);\n      });\n\n      // Analyze each season for gaps and missing episodes\n      for (const [season, episodes] of episodesBySeason) {\n        episodes.sort((a, b) => a - b);\n        const minEp = Math.min(...episodes);\n        const maxEp = Math.max(...episodes);\n        const missingEpisodes: number[] = [];\n\n        for (let ep = 1; ep <= maxEp; ep++) {\n          if (!episodes.includes(ep)) {\n            missingEpisodes.push(ep);\n          }\n        }\n\n        if (missingEpisodes.length > 0) {\n          console.log(`⚠️ Season ${season}: Missing episodes ${missingEpisodes.join(', ')} (Have: ${episodes.join(', ')})`);\n\n          // Strategy 3a: Search for missing episodes specifically\n          console.log(`🔍 Searching for missing episodes in Season ${season}...`);\n          // This would involve more targeted searches if VidSrc had episode-specific endpoints\n        }\n      }\n\n      // Convert Map back to array and sort\n      const uniqueEpisodes = Array.from(allEpisodes.values()).sort((a, b) => {\n        if (a.season !== b.season) return a.season - b.season;\n        return a.episode - b.episode;\n      });\n\n      // Final analysis\n      const totalSeasons = episodesBySeason.size;\n      const totalEpisodes = uniqueEpisodes.length;\n      const seasonRanges = Array.from(episodesBySeason.keys()).sort((a, b) => a - b);\n      const minSeason = seasonRanges[0] || 0;\n      const maxSeason = seasonRanges[seasonRanges.length - 1] || 0;\n\n      console.log(`🎯 COMPREHENSIVE SEARCH COMPLETE:`);\n      console.log(`   📺 Total Episodes Found: ${totalEpisodes}`);\n      console.log(`   🗂️ Seasons Found: ${totalSeasons} (S${minSeason}-S${maxSeason})`);\n      console.log(`   📊 Episodes per Season:`);\n\n      episodesBySeason.forEach((episodes, season) => {\n        episodes.sort((a, b) => a - b);\n        const gaps = [];\n        const maxEp = Math.max(...episodes);\n        for (let ep = 1; ep <= maxEp; ep++) {\n          if (!episodes.includes(ep)) gaps.push(ep);\n        }\n        console.log(`      S${season}: ${episodes.length} episodes (${episodes.join(', ')})${gaps.length > 0 ? ` - Missing: ${gaps.join(', ')}` : ' - Complete'}`);\n      });\n\n      return uniqueEpisodes;\n\n    } catch (error) {\n      console.error(`Error in comprehensive episode search for series ${imdbId}:`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Sync latest content from VidSrc to our database\n   */\n  async syncLatestContent(pages: number = 5): Promise<{\n    movies: VidSrcMovieData[];\n    series: VidSrcSeriesData[];\n    episodes: VidSrcEpisodeData[];\n  }> {\n    const movies: VidSrcMovieData[] = [];\n    const series: VidSrcSeriesData[] = [];\n    const episodes: VidSrcEpisodeData[] = [];\n\n    // Fetch multiple pages in parallel\n    const moviePromises = Array.from({ length: pages }, (_, i) => this.getLatestMovies(i + 1));\n    const seriesPromises = Array.from({ length: pages }, (_, i) => this.getLatestSeries(i + 1));\n    const episodePromises = Array.from({ length: pages }, (_, i) => this.getLatestEpisodes(i + 1));\n\n    try {\n      const [movieResults, seriesResults, episodeResults] = await Promise.all([\n        Promise.all(moviePromises),\n        Promise.all(seriesPromises),\n        Promise.all(episodePromises)\n      ]);\n\n      // Flatten results\n      movieResults.forEach(pageMovies => movies.push(...pageMovies));\n      seriesResults.forEach(pageSeries => series.push(...pageSeries));\n      episodeResults.forEach(pageEpisodes => episodes.push(...pageEpisodes));\n\n      return { movies, series, episodes };\n    } catch (error) {\n      console.error('Error syncing latest content from VidSrc:', error);\n      return { movies, series, episodes };\n    }\n  }\n}\n\nexport default VidSrcAPI;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB,QAAQ,GAAG,CAAC,eAAe,IAAI;AAGhD,MAAM,oBAAoB;IAC/B,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;AACF;AAkCA,MAAM;IACJ,OAAe,SAAoB;IAEnC,OAAO,cAAyB;QAC9B,IAAI,CAAC,UAAU,QAAQ,EAAE;YACvB,UAAU,QAAQ,GAAG,IAAI;QAC3B;QACA,OAAO,UAAU,QAAQ;IAC3B;IAEA;;GAEC,GACD,0BAA0B,MAAc,EAAE,MAAe,EAMtD;QACD,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM;QACzC,MAAM,OAAO,EAAE;QAEf,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,kBAAkB,EAAE,QAAQ;YACzE,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,YAAY;QACZ,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,SAAS,CAAC,IAAI;YACtC,KAAK,GAAG,kBAAkB,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACnE,SAAS,kBAAkB,SAAS,CAAC,OAAO;YAC5C,UAAU,kBAAkB,SAAS,CAAC,QAAQ;QAChD;QAEA,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACpE,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACtE,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACtE,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACpD;IAEA;;GAEC,GACD,sBAAsB,MAAc,EAAE,OAKrC,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,YAAY,CAAC;QAChD,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,mBAAmB,QAAQ,MAAM;QAC5D;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,4BAA4B,MAAc,EAAE,MAAc,EAAE,OAAe,EAAE,MAAe,EAMzF;QACD,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM;QACzC,MAAM,OAAO,EAAE;QAEf,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,SAAS;YAC1G,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,YAAY;QACZ,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,SAAS,CAAC,IAAI;YACtC,KAAK,GAAG,kBAAkB,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACrF,SAAS,kBAAkB,SAAS,CAAC,OAAO;YAC5C,UAAU,kBAAkB,SAAS,CAAC,QAAQ;QAChD;QAEA,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACtF,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACxF,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACxF,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACpD;IAEA;;GAEC,GACD,uBAAuB,MAAc,EAAE,OAGtC,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,SAAS,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,wBAAwB,MAAc,EAAE,MAAc,EAAE,OAAe,EAAE,OAMxE,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,SAAS,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,OAAO,MAAM,CAAC,UAAU,OAAO,QAAQ;QACvC,OAAO,MAAM,CAAC,WAAW,QAAQ,QAAQ;QAEzC,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,mBAAmB,QAAQ,MAAM;QAC5D;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,CAAC,EAA8B;QAClE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,oBAAoB,EAAE,KAAK,KAAK,CAAC;YAChE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAwC;YACxE,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,CAAC,EAA+B;QACnE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,qBAAqB,EAAE,KAAK,KAAK,CAAC;YACjE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAyC;YACzE,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,OAAe,CAAC,EAAE,UAAkB,CAAC,EAAgC;QAC3F,MAAM,MAAM,GAAG,gBAAgB,sBAAsB,EAAE,KAAK,KAAK,CAAC;QAElE,IAAK,IAAI,UAAU,GAAG,WAAW,SAAS,UAAW;YACnD,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,KAAK,UAAU,EAAE,QAAQ,CAAC,EAAE,QAAQ,IAAI,CAAC;gBACzF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAA0C,KAAK;oBAC7E,SAAS;oBACT,SAAS;wBACP,cAAc;wBACd,UAAU;oBACZ;gBACF;gBAEA,MAAM,WAAW,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;gBAC3C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;gBAC/D,OAAO;YAET,EAAE,OAAO,OAAY;gBACnB,MAAM,gBAAgB,YAAY;gBAClC,MAAM,WAAW,MAAM,QAAQ,EAAE,SAC7B,CAAC,KAAK,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE,GAC5D,MAAM,OAAO;gBAEjB,IAAI,eAAe;oBACjB,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,KAAK,OAAO,EAAE,QAAQ,WAAW,EAAE,UAAU;oBACrF,OAAO,EAAE;gBACX,OAAO;oBACL,QAAQ,IAAI,CAAC,CAAC,WAAW,EAAE,QAAQ,iBAAiB,EAAE,KAAK,EAAE,EAAE,SAAS,aAAa,CAAC;oBACtF,0CAA0C;oBAC1C,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,UAAU,IAAI,OAAO,gBAAgB;oBAC/E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;QACF;QAEA,OAAO,EAAE;IACX;IAEA;;;GAGC,GACD,MAAM,kBAAkB,MAAc,EAKlC;QACF,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sDAAsD,EAAE,QAAQ;YAE7E,MAAM,cAAc,IAAI;YAOxB,qEAAqE;YACrE,QAAQ,GAAG,CAAC,CAAC,2DAA2D,CAAC;YACzE,IAAI,wBAAwB;YAC5B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;gBACrC,IAAI;oBACF,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC;oBAC9C,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO,mBAAmB;oBAErD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;oBAEtE,eAAe,OAAO,CAAC,CAAA;wBACrB,MAAM,MAAM,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;wBACnD,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM;4BACzB,YAAY,GAAG,CAAC,KAAK;gCACnB,QAAQ,QAAQ,MAAM;gCACtB,SAAS,QAAQ,OAAO;gCACxB,WAAW,QAAQ,SAAS;gCAC5B,gBAAgB,QAAQ,cAAc;4BACxC;4BACA;wBACF;oBACF;oBAEA,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE,eAAe,MAAM,CAAC,SAAS,CAAC;oBACxE;oBAEA,gEAAgE;oBAChE,IAAI,OAAO,MAAM,eAAe,MAAM,KAAK,GAAG;wBAC5C,IAAI,aAAa;wBACjB,IAAK,IAAI,YAAY,OAAO,GAAG,aAAa,MAAM,YAAa;4BAC7D,MAAM,gBAAgB,MAAM,IAAI,CAAC,iBAAiB,CAAC;4BACnD,IAAI,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,QAAQ,MAAM,KAAK,GAAG;gCAClE;4BACF;wBACF;wBACA,IAAI,cAAc,GAAG,OAAO,sCAAsC;oBACpE;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC,EAAE;gBACzD;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,sBAAsB,yBAAyB,CAAC;YAE5F,kEAAkE;YAClE,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC;YACnE,IAAI,wBAAwB;YAC5B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;gBACrC,IAAI;oBACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,eAAe,CAAC;oBAClD,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,SAAU,OAAO,OAAO,KAAK;oBAE1E,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,kDAAkD,CAAC;oBACtF,0DAA0D;oBAC1D,mEAAmE;oBACrE;gBACF,EAAE,OAAO,OAAO;gBACd,4CAA4C;gBAC9C;YACF;YAEA,uDAAuD;YACvD,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC;YAC9D,MAAM,mBAAmB,IAAI;YAE7B,iCAAiC;YACjC,YAAY,OAAO,CAAC,CAAC,SAAS;gBAC5B,IAAI,CAAC,iBAAiB,GAAG,CAAC,QAAQ,MAAM,GAAG;oBACzC,iBAAiB,GAAG,CAAC,QAAQ,MAAM,EAAE,EAAE;gBACzC;gBACA,iBAAiB,GAAG,CAAC,QAAQ,MAAM,EAAG,IAAI,CAAC,QAAQ,OAAO;YAC5D;YAEA,oDAAoD;YACpD,KAAK,MAAM,CAAC,QAAQ,SAAS,IAAI,iBAAkB;gBACjD,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;gBAC5B,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,MAAM,kBAA4B,EAAE;gBAEpC,IAAK,IAAI,KAAK,GAAG,MAAM,OAAO,KAAM;oBAClC,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK;wBAC1B,gBAAgB,IAAI,CAAC;oBACvB;gBACF;gBAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,mBAAmB,EAAE,gBAAgB,IAAI,CAAC,MAAM,QAAQ,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC;oBAEhH,wDAAwD;oBACxD,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,OAAO,GAAG,CAAC;gBACtE,qFAAqF;gBACvF;YACF;YAEA,qCAAqC;YACrC,MAAM,iBAAiB,MAAM,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG;gBAC/D,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;gBACrD,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO;YAC9B;YAEA,iBAAiB;YACjB,MAAM,eAAe,iBAAiB,IAAI;YAC1C,MAAM,gBAAgB,eAAe,MAAM;YAC3C,MAAM,eAAe,MAAM,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YAC5E,MAAM,YAAY,YAAY,CAAC,EAAE,IAAI;YACrC,MAAM,YAAY,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,IAAI;YAE3D,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC;YAC/C,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,eAAe;YAC1D,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,aAAa,GAAG,EAAE,UAAU,EAAE,EAAE,UAAU,CAAC,CAAC;YACjF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,CAAC;YAExC,iBAAiB,OAAO,CAAC,CAAC,UAAU;gBAClC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;gBAC5B,MAAM,OAAO,EAAE;gBACf,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,IAAK,IAAI,KAAK,GAAG,MAAM,OAAO,KAAM;oBAClC,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC;gBACxC;gBACA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,CAAC,OAAO,GAAG,eAAe;YAC3J;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,OAAO,CAAC,CAAC,EAAE;YAC7E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,QAAgB,CAAC,EAItC;QACD,MAAM,SAA4B,EAAE;QACpC,MAAM,SAA6B,EAAE;QACrC,MAAM,WAAgC,EAAE;QAExC,mCAAmC;QACnC,MAAM,gBAAgB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI;QACvF,MAAM,iBAAiB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI;QACxF,MAAM,kBAAkB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI;QAE3F,IAAI;YACF,MAAM,CAAC,cAAc,eAAe,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtE,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;aACb;YAED,kBAAkB;YAClB,aAAa,OAAO,CAAC,CAAA,aAAc,OAAO,IAAI,IAAI;YAClD,cAAc,OAAO,CAAC,CAAA,aAAc,OAAO,IAAI,IAAI;YACnD,eAAe,OAAO,CAAC,CAAA,eAAgB,SAAS,IAAI,IAAI;YAExD,OAAO;gBAAE;gBAAQ;gBAAQ;YAAS;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE;gBAAQ;gBAAQ;YAAS;QACpC;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/scraper.ts"], "sourcesContent": ["import axios from 'axios';\nimport * as cheerio from 'cheerio';\n\nexport interface ScrapedMovieData {\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  backdropUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n}\n\nexport interface ScrapedSeriesData {\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  backdropUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n}\n\nclass IMDbScraper {\n  private static instance: IMDbScraper;\n  private requestCount = 0;\n  private lastRequestTime = 0;\n  private readonly RATE_LIMIT = 30; // requests per minute\n  private readonly MIN_DELAY = 2000; // 2 seconds between requests\n\n  static getInstance(): IMDbScraper {\n    if (!IMDbScraper.instance) {\n      IMDbScraper.instance = new IMDbScraper();\n    }\n    return IMDbScraper.instance;\n  }\n\n  private async rateLimit(): Promise<void> {\n    const now = Date.now();\n    const timeSinceLastRequest = now - this.lastRequestTime;\n\n    if (timeSinceLastRequest < this.MIN_DELAY) {\n      const delay = this.MIN_DELAY - timeSinceLastRequest;\n      // Add random jitter to avoid detection patterns\n      const jitter = Math.random() * 1000; // 0-1000ms random delay\n      await new Promise(resolve => setTimeout(resolve, delay + jitter));\n    }\n\n    this.lastRequestTime = Date.now();\n    this.requestCount++;\n  }\n\n  private getRandomUserAgent(): string {\n    const userAgents = [\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',\n      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'\n    ];\n    return userAgents[Math.floor(Math.random() * userAgents.length)];\n  }\n\n  private async fetchPage(imdbId: string): Promise<cheerio.CheerioAPI> {\n    await this.rateLimit();\n\n    const url = `https://www.imdb.com/title/${imdbId}/`;\n    const headers = {\n      'User-Agent': this.getRandomUserAgent(),\n      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n      'Accept-Language': 'en-US,en;q=0.5',\n      'Accept-Encoding': 'gzip, deflate, br',\n      'Connection': 'keep-alive',\n      'Upgrade-Insecure-Requests': '1',\n      'Sec-Fetch-Dest': 'document',\n      'Sec-Fetch-Mode': 'navigate',\n      'Sec-Fetch-Site': 'none',\n      'Cache-Control': 'max-age=0',\n    };\n\n    try {\n      const response = await axios.get(url, { headers, timeout: 30000 });\n      return cheerio.load(response.data);\n    } catch (error) {\n      console.error(`Error fetching IMDb page for ${imdbId}:`, error);\n      throw new Error(`Failed to fetch IMDb page: ${error.message}`);\n    }\n  }\n\n  private extractBasicInfo($: cheerio.CheerioAPI): { title: string; year: number; type: 'movie' | 'series' } {\n    const titleElement = $('h1[data-testid=\"hero__pageTitle\"] span[data-testid=\"hero__primary-text\"]');\n    const title = titleElement.text().trim();\n    \n    if (!title) {\n      throw new Error('Could not extract title from IMDb page');\n    }\n\n    // Extract year from the release info\n    const yearElement = $('ul.ipc-inline-list a[href*=\"/releaseinfo/\"]');\n    const yearText = yearElement.text().trim();\n    const year = parseInt(yearText) || new Date().getFullYear();\n\n    // Determine if it's a movie or series\n    const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();\n    const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');\n    \n    return {\n      title,\n      year,\n      type: isMovie ? 'movie' : 'series'\n    };\n  }\n\n  private extractRating($: cheerio.CheerioAPI): string | undefined {\n    const ratingElement = $('ul.ipc-inline-list a[href*=\"/parentalguide/\"]');\n    return ratingElement.text().trim() || undefined;\n  }\n\n  private extractRuntime($: cheerio.CheerioAPI): string | undefined {\n    const runtimeElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < runtimeElements.length; i++) {\n      const text = $(runtimeElements[i]).text().trim();\n      if (text.includes('h') || text.includes('min')) {\n        return text;\n      }\n    }\n    return undefined;\n  }\n\n  private extractIMDbRating($: cheerio.CheerioAPI): { rating?: number; votes?: string } {\n    const ratingElement = $('div[data-testid=\"hero-rating-bar__aggregate-rating__score\"] span');\n    const rating = parseFloat(ratingElement.text().trim()) || undefined;\n    \n    const votesElement = $('div.sc-d541859f-3');\n    const votes = votesElement.text().trim() || undefined;\n    \n    return { rating, votes };\n  }\n\n  private extractPopularity($: cheerio.CheerioAPI): { popularity?: number; delta?: number } {\n    const popularityElement = $('div[data-testid=\"hero-rating-bar__popularity__score\"]');\n    const popularity = parseInt(popularityElement.text().trim()) || undefined;\n    \n    const deltaElement = $('div[data-testid=\"hero-rating-bar__popularity__delta\"]');\n    const deltaText = deltaElement.text().trim();\n    const delta = deltaText ? parseInt(deltaText.replace(/[^\\d-]/g, '')) : undefined;\n    \n    return { popularity, delta };\n  }\n\n  private extractPosterUrl($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for poster image\n    const selectors = [\n      'div[data-testid=\"hero-media__poster\"] img',\n      '.ipc-image[data-testid=\"hero-media__poster\"]',\n      '.poster img',\n      '.ipc-media img',\n      'img[class*=\"poster\"]',\n      'a[class*=\"ipc-lockup-overlay\"] img'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      const src = element.attr('src');\n      if (src && src.includes('media-amazon.com')) {\n        // Clean up the URL to get high quality image\n        return src.replace(/\\._.*?_\\./, '._V1_FMjpg_UX1000_.').replace(/\\._.*?\\./, '._V1_FMjpg_UX1000_.');\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractBackdropUrl($: cheerio.CheerioAPI): string | undefined {\n    // Try to extract backdrop/hero image\n    const selectors = [\n      '.hero-media__slate-overlay img',\n      '.slate img',\n      '.hero__background img',\n      'div[data-testid=\"hero-media\"] img'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      const src = element.attr('src');\n      if (src && src.includes('media-amazon.com')) {\n        return src.replace(/\\._.*?_\\./, '._V1_FMjpg_UX1920_.');\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractTrailerInfo($: cheerio.CheerioAPI): { url?: string; runtime?: string; likes?: string } {\n    const trailerElement = $('a[data-testid=\"video-player-slate-overlay\"]');\n    const url = trailerElement.attr('href') || undefined;\n    \n    const runtimeElement = $('span[data-testid=\"video-player-slate-runtime\"]');\n    const runtime = runtimeElement.text().trim() || undefined;\n    \n    const likesElement = $('span.ipc-reaction-summary__label');\n    const likes = likesElement.text().trim() || undefined;\n    \n    return { url, runtime, likes };\n  }\n\n  private extractDescription($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for description\n    const selectors = [\n      'div[data-testid=\"hero-media__slate\"] img',\n      'span[data-testid=\"plot-xl\"]',\n      'span[data-testid=\"plot-l\"]',\n      'span[data-testid=\"plot\"]'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      if (selector.includes('img')) {\n        const alt = element.attr('alt');\n        if (alt && alt.length > 20) return alt;\n      } else {\n        const text = element.text().trim();\n        if (text && text.length > 10) return text;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractGenres($: cheerio.CheerioAPI): string[] {\n    const genres: string[] = [];\n\n    // Updated genre selectors based on current IMDb structure\n    const genreSelectors = [\n      // Current IMDb hero section genres (most common)\n      '[data-testid=\"genres\"] .ipc-chip .ipc-chip__text',\n      '[data-testid=\"genres\"] .ipc-chip__text',\n      '[data-testid=\"genres\"] a .ipc-chip__text',\n\n      // Hero section alternative formats\n      '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',\n      '.GenresAndPlot__GenreChip .ipc-chip__text',\n\n      // Storyline section (your provided structure - keep as fallback)\n      'li[data-testid=\"storyline-genres\"] .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"storyline-genres\"] a[href*=\"genres=\"]',\n\n      // General genre link selectors (broader search)\n      'a[href*=\"/search/title/?genres=\"] span',\n      'a[href*=\"genres=\"] span',\n      'a[href*=\"genres=\"]',\n\n      // Schema.org microdata\n      '[itemprop=\"genre\"]',\n      'span[itemprop=\"genre\"]',\n\n      // Fallback selectors\n      '.see-more.inline.canwrap a[href*=\"genres=\"]',\n      '.titlePageSprite.star-box-giga-star + div a[href*=\"genres=\"]',\n\n      // Very broad fallback - any link with genre in URL\n      'a[href*=\"explore=genres\"]'\n    ];\n\n    console.log(`🔍 Starting genre extraction...`);\n\n    for (let i = 0; i < genreSelectors.length; i++) {\n      const selector = genreSelectors[i];\n      const elements = $(selector);\n      console.log(`🔍 Selector ${i + 1}/${genreSelectors.length}: \"${selector}\" found ${elements.length} elements`);\n\n      if (elements.length > 0) {\n        elements.each((_, element) => {\n          const genre = $(element).text().trim();\n          console.log(`📝 Found genre text: \"${genre}\"`);\n\n          // Clean up genre text and validate\n          if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {\n            // Skip common non-genre text\n            const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];\n            if (!skipTexts.includes(genre)) {\n              genres.push(genre);\n              console.log(`✅ Added genre: \"${genre}\"`);\n            }\n          }\n        });\n\n        if (genres.length > 0) {\n          console.log(`✅ Successfully extracted ${genres.length} genres: [${genres.join(', ')}]`);\n          break; // Use the first selector that finds results\n        }\n      }\n    }\n\n    if (genres.length === 0) {\n      console.log('⚠️ No genres found with any selector');\n      // Debug: Let's see what's actually in the storyline section\n      const storylineSection = $('li[data-testid=\"storyline-genres\"]');\n      if (storylineSection.length > 0) {\n        console.log('📋 Storyline section HTML:', storylineSection.html());\n      } else {\n        console.log('❌ No storyline-genres section found');\n      }\n    }\n\n    return genres;\n  }\n\n  private extractCast($: cheerio.CheerioAPI): string[] {\n    const cast: string[] = [];\n\n    // Try multiple selectors for cast\n    const castSelectors = [\n      'section[data-testid=\"title-cast\"] a[data-testid=\"title-cast-item__actor\"]',\n      '.cast_list .primary_photo + td a',\n      '.titleCast .primary_photo + td a',\n      'div[data-testid=\"title-cast-item\"] a[href*=\"/name/\"]'\n    ];\n\n    for (const selector of castSelectors) {\n      const elements = $(selector);\n      if (elements.length > 0) {\n        elements.each((_, element) => {\n          const actorName = $(element).text().trim();\n          if (actorName && !cast.includes(actorName) && cast.length < 10) { // Limit to top 10 cast members\n            cast.push(actorName);\n          }\n        });\n        break; // Use the first selector that finds results\n      }\n    }\n\n    return cast;\n  }\n\n  private extractDirector($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for director\n    const directorSelectors = [\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Director\") .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Directors\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"/name/\"][href*=\"ref_=tt_ov_dr\"]',\n      '.credit_summary_item:contains(\"Director\") a'\n    ];\n\n    for (const selector of directorSelectors) {\n      const element = $(selector).first();\n      const director = element.text().trim();\n      if (director) {\n        return director;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractCreator($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for creator (for TV series)\n    const creatorSelectors = [\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Creator\") .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Creators\") .ipc-metadata-list-item__list-content-item',\n      '.credit_summary_item:contains(\"Creator\") a',\n      '.credit_summary_item:contains(\"Created by\") a'\n    ];\n\n    for (const selector of creatorSelectors) {\n      const element = $(selector).first();\n      const creator = element.text().trim();\n      if (creator) {\n        return creator;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractLanguage($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for language\n    const languageSelectors = [\n      'li[data-testid=\"title-details-languages\"] .ipc-metadata-list-item__list-content-item',\n      'div[data-testid=\"title-details-section\"] li:contains(\"Language\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"primary_language=\"]',\n      '.txt-block:contains(\"Language\") a'\n    ];\n\n    for (const selector of languageSelectors) {\n      const element = $(selector).first();\n      const language = element.text().trim();\n      if (language) {\n        return language;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractCountry($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for country\n    const countrySelectors = [\n      'li[data-testid=\"title-details-origin\"] .ipc-metadata-list-item__list-content-item',\n      'div[data-testid=\"title-details-section\"] li:contains(\"Country\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"country_of_origin=\"]',\n      '.txt-block:contains(\"Country\") a'\n    ];\n\n    for (const selector of countrySelectors) {\n      const element = $(selector).first();\n      const country = element.text().trim();\n      if (country) {\n        return country;\n      }\n    }\n\n    return undefined;\n  }\n\n  async scrapeMovie(imdbId: string): Promise<ScrapedMovieData> {\n    const $ = await this.fetchPage(imdbId);\n    const basicInfo = this.extractBasicInfo($);\n\n    if (basicInfo.type !== 'movie') {\n      throw new Error('IMDb ID does not correspond to a movie');\n    }\n\n    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);\n    const { popularity, delta: popularityDelta } = this.extractPopularity($);\n    const { url: trailerUrl, runtime: trailerRuntime, likes: trailerLikes } = this.extractTrailerInfo($);\n\n    return {\n      title: basicInfo.title,\n      year: basicInfo.year,\n      rating: this.extractRating($),\n      runtime: this.extractRuntime($),\n      imdbRating,\n      imdbVotes,\n      popularity,\n      popularityDelta,\n      posterUrl: this.extractPosterUrl($),\n      backdropUrl: this.extractBackdropUrl($),\n      trailerUrl,\n      trailerRuntime,\n      trailerLikes,\n      description: this.extractDescription($),\n      genres: this.extractGenres($),\n      director: this.extractDirector($),\n      cast: this.extractCast($),\n      language: this.extractLanguage($),\n      country: this.extractCountry($),\n    };\n  }\n\n  async scrapeSeries(imdbId: string): Promise<ScrapedSeriesData> {\n    const $ = await this.fetchPage(imdbId);\n    const basicInfo = this.extractBasicInfo($);\n\n    if (basicInfo.type !== 'series') {\n      throw new Error('IMDb ID does not correspond to a TV series');\n    }\n\n    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);\n    const { popularity, delta: popularityDelta } = this.extractPopularity($);\n    const { url: trailerUrl } = this.extractTrailerInfo($);\n\n    return {\n      title: basicInfo.title,\n      startYear: basicInfo.year,\n      endYear: this.extractEndYear($),\n      rating: this.extractRating($),\n      imdbRating,\n      imdbVotes,\n      popularity,\n      popularityDelta,\n      posterUrl: this.extractPosterUrl($),\n      backdropUrl: this.extractBackdropUrl($),\n      trailerUrl,\n      description: this.extractDescription($),\n      genres: this.extractGenres($),\n      creator: this.extractCreator($),\n      cast: this.extractCast($),\n      language: this.extractLanguage($),\n      country: this.extractCountry($),\n      totalSeasons: this.extractTotalSeasons($),\n      status: this.extractSeriesStatus($),\n    };\n  }\n\n  private extractEndYear($: CheerioAPI): number | undefined {\n    // Try to extract end year from series info\n    const yearElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < yearElements.length; i++) {\n      const text = $(yearElements[i]).text().trim();\n      const yearMatch = text.match(/(\\d{4})–(\\d{4})/);\n      if (yearMatch) {\n        return parseInt(yearMatch[2]);\n      }\n    }\n    return undefined;\n  }\n\n  private extractTotalSeasons($: CheerioAPI): number | undefined {\n    // Try to extract total seasons from series info\n    const seasonElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < seasonElements.length; i++) {\n      const text = $(seasonElements[i]).text().trim();\n      const seasonMatch = text.match(/(\\d+)\\s+seasons?/i);\n      if (seasonMatch) {\n        return parseInt(seasonMatch[1]);\n      }\n    }\n    return 1; // Default to 1 season\n  }\n\n  private extractSeriesStatus($: CheerioAPI): string {\n    // Try to determine if series is ongoing or ended\n    const yearElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < yearElements.length; i++) {\n      const text = $(yearElements[i]).text().trim();\n      if (text.includes('–') && !text.match(/\\d{4}–\\d{4}/)) {\n        return 'ongoing';\n      } else if (text.match(/\\d{4}–\\d{4}/)) {\n        return 'ended';\n      }\n    }\n    return 'ongoing'; // Default to ongoing\n  }\n}\n\nexport default IMDbScraper;\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AA8CA,MAAM;IACJ,OAAe,SAAsB;IAC7B,eAAe,EAAE;IACjB,kBAAkB,EAAE;IACX,aAAa,GAAG;IAChB,YAAY,KAAK;IAElC,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,MAAc,YAA2B;QACvC,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,uBAAuB,MAAM,IAAI,CAAC,eAAe;QAEvD,IAAI,uBAAuB,IAAI,CAAC,SAAS,EAAE;YACzC,MAAM,QAAQ,IAAI,CAAC,SAAS,GAAG;YAC/B,gDAAgD;YAChD,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,wBAAwB;YAC7D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ;QAC3D;QAEA,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG;QAC/B,IAAI,CAAC,YAAY;IACnB;IAEQ,qBAA6B;QACnC,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA;SACD;QACD,OAAO,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;IAClE;IAEA,MAAc,UAAU,MAAc,EAA+B;QACnE,MAAM,IAAI,CAAC,SAAS;QAEpB,MAAM,MAAM,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;QACnD,MAAM,UAAU;YACd,cAAc,IAAI,CAAC,kBAAkB;YACrC,UAAU;YACV,mBAAmB;YACnB,mBAAmB;YACnB,cAAc;YACd,6BAA6B;YAC7B,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,iBAAiB;QACnB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBAAE;gBAAS,SAAS;YAAM;YAChE,OAAO,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EAAE;YACzD,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM,OAAO,EAAE;QAC/D;IACF;IAEQ,iBAAiB,CAAqB,EAA6D;QACzG,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI;QAEtC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,MAAM,cAAc,EAAE;QACtB,MAAM,WAAW,YAAY,IAAI,GAAG,IAAI;QACxC,MAAM,OAAO,SAAS,aAAa,IAAI,OAAO,WAAW;QAEzD,sCAAsC;QACtC,MAAM,iBAAiB,EAAE,yBAAyB,IAAI,GAAG,WAAW;QACpE,MAAM,UAAU,CAAC,eAAe,QAAQ,CAAC,gBAAgB,CAAC,eAAe,QAAQ,CAAC;QAElF,OAAO;YACL;YACA;YACA,MAAM,UAAU,UAAU;QAC5B;IACF;IAEQ,cAAc,CAAqB,EAAsB;QAC/D,MAAM,gBAAgB,EAAE;QACxB,OAAO,cAAc,IAAI,GAAG,IAAI,MAAM;IACxC;IAEQ,eAAe,CAAqB,EAAsB;QAChE,MAAM,kBAAkB,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,MAAM,OAAO,EAAE,eAAe,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC9C,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ;gBAC9C,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,CAAqB,EAAuC;QACpF,MAAM,gBAAgB,EAAE;QACxB,MAAM,SAAS,WAAW,cAAc,IAAI,GAAG,IAAI,OAAO;QAE1D,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI,MAAM;QAE5C,OAAO;YAAE;YAAQ;QAAM;IACzB;IAEQ,kBAAkB,CAAqB,EAA2C;QACxF,MAAM,oBAAoB,EAAE;QAC5B,MAAM,aAAa,SAAS,kBAAkB,IAAI,GAAG,IAAI,OAAO;QAEhE,MAAM,eAAe,EAAE;QACvB,MAAM,YAAY,aAAa,IAAI,GAAG,IAAI;QAC1C,MAAM,QAAQ,YAAY,SAAS,UAAU,OAAO,CAAC,WAAW,OAAO;QAEvE,OAAO;YAAE;YAAY;QAAM;IAC7B;IAEQ,iBAAiB,CAAqB,EAAsB;QAClE,0CAA0C;QAC1C,MAAM,YAAY;YAChB;YACA;YACA;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,MAAM,MAAM,QAAQ,IAAI,CAAC;YACzB,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB;gBAC3C,6CAA6C;gBAC7C,OAAO,IAAI,OAAO,CAAC,aAAa,uBAAuB,OAAO,CAAC,YAAY;YAC7E;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,CAAqB,EAAsB;QACpE,qCAAqC;QACrC,MAAM,YAAY;YAChB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,MAAM,MAAM,QAAQ,IAAI,CAAC;YACzB,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB;gBAC3C,OAAO,IAAI,OAAO,CAAC,aAAa;YAClC;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,CAAqB,EAAsD;QACpG,MAAM,iBAAiB,EAAE;QACzB,MAAM,MAAM,eAAe,IAAI,CAAC,WAAW;QAE3C,MAAM,iBAAiB,EAAE;QACzB,MAAM,UAAU,eAAe,IAAI,GAAG,IAAI,MAAM;QAEhD,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI,MAAM;QAE5C,OAAO;YAAE;YAAK;YAAS;QAAM;IAC/B;IAEQ,mBAAmB,CAAqB,EAAsB;QACpE,yCAAyC;QACzC,MAAM,YAAY;YAChB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,IAAI,SAAS,QAAQ,CAAC,QAAQ;gBAC5B,MAAM,MAAM,QAAQ,IAAI,CAAC;gBACzB,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,OAAO;YACrC,OAAO;gBACL,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI;gBAChC,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI,OAAO;YACvC;QACF;QAEA,OAAO;IACT;IAEQ,cAAc,CAAqB,EAAY;QACrD,MAAM,SAAmB,EAAE;QAE3B,0DAA0D;QAC1D,MAAM,iBAAiB;YACrB,iDAAiD;YACjD;YACA;YACA;YAEA,mCAAmC;YACnC;YACA;YAEA,iEAAiE;YACjE;YACA;YAEA,gDAAgD;YAChD;YACA;YACA;YAEA,uBAAuB;YACvB;YACA;YAEA,qBAAqB;YACrB;YACA;YAEA,mDAAmD;YACnD;SACD;QAED,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC;QAE7C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,WAAW,cAAc,CAAC,EAAE;YAClC,MAAM,WAAW,EAAE;YACnB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,eAAe,MAAM,CAAC,GAAG,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;YAE5G,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,MAAM,QAAQ,EAAE,SAAS,IAAI,GAAG,IAAI;oBACpC,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;oBAE7C,mCAAmC;oBACnC,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,QAAQ,CAAC,QAAQ;wBAC7E,6BAA6B;wBAC7B,MAAM,YAAY;4BAAC;4BAAU;4BAAS;4BAAW;4BAAQ;4BAAO;yBAAM;wBACtE,IAAI,CAAC,UAAU,QAAQ,CAAC,QAAQ;4BAC9B,OAAO,IAAI,CAAC;4BACZ,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;wBACzC;oBACF;gBACF;gBAEA,IAAI,OAAO,MAAM,GAAG,GAAG;oBACrB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;oBACtF,OAAO,4CAA4C;gBACrD;YACF;QACF;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,QAAQ,GAAG,CAAC;YACZ,4DAA4D;YAC5D,MAAM,mBAAmB,EAAE;YAC3B,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,QAAQ,GAAG,CAAC,8BAA8B,iBAAiB,IAAI;YACjE,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,OAAO;IACT;IAEQ,YAAY,CAAqB,EAAY;QACnD,MAAM,OAAiB,EAAE;QAEzB,kCAAkC;QAClC,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,cAAe;YACpC,MAAM,WAAW,EAAE;YACnB,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,MAAM,YAAY,EAAE,SAAS,IAAI,GAAG,IAAI;oBACxC,IAAI,aAAa,CAAC,KAAK,QAAQ,CAAC,cAAc,KAAK,MAAM,GAAG,IAAI;wBAC9D,KAAK,IAAI,CAAC;oBACZ;gBACF;gBACA,OAAO,4CAA4C;YACrD;QACF;QAEA,OAAO;IACT;IAEQ,gBAAgB,CAAqB,EAAsB;QACjE,sCAAsC;QACtC,MAAM,oBAAoB;YACxB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,kBAAmB;YACxC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,WAAW,QAAQ,IAAI,GAAG,IAAI;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,eAAe,CAAqB,EAAsB;QAChE,qDAAqD;QACrD,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI;YACnC,IAAI,SAAS;gBACX,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,gBAAgB,CAAqB,EAAsB;QACjE,sCAAsC;QACtC,MAAM,oBAAoB;YACxB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,kBAAmB;YACxC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,WAAW,QAAQ,IAAI,GAAG,IAAI;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,eAAe,CAAqB,EAAsB;QAChE,qCAAqC;QACrC,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI;YACnC,IAAI,SAAS;gBACX,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,YAAY,MAAc,EAA6B;QAC3D,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;QAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAExC,IAAI,UAAU,IAAI,KAAK,SAAS;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,QAAQ,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxE,MAAM,EAAE,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtE,MAAM,EAAE,KAAK,UAAU,EAAE,SAAS,cAAc,EAAE,OAAO,YAAY,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElG,OAAO;YACL,OAAO,UAAU,KAAK;YACtB,MAAM,UAAU,IAAI;YACpB,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B;YACA;YACA;YACA;YACA,WAAW,IAAI,CAAC,gBAAgB,CAAC;YACjC,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC;YACA;YACA;YACA,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,SAAS,IAAI,CAAC,cAAc,CAAC;QAC/B;IACF;IAEA,MAAM,aAAa,MAAc,EAA8B;QAC7D,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;QAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAExC,IAAI,UAAU,IAAI,KAAK,UAAU;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,QAAQ,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxE,MAAM,EAAE,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtE,MAAM,EAAE,KAAK,UAAU,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAEpD,OAAO;YACL,OAAO,UAAU,KAAK;YACtB,WAAW,UAAU,IAAI;YACzB,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B;YACA;YACA;YACA;YACA,WAAW,IAAI,CAAC,gBAAgB,CAAC;YACjC,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC;YACA,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,cAAc,IAAI,CAAC,mBAAmB,CAAC;YACvC,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACnC;IACF;IAEQ,eAAe,CAAa,EAAsB;QACxD,2CAA2C;QAC3C,MAAM,eAAe,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,OAAO,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC3C,MAAM,YAAY,KAAK,KAAK,CAAC;YAC7B,IAAI,WAAW;gBACb,OAAO,SAAS,SAAS,CAAC,EAAE;YAC9B;QACF;QACA,OAAO;IACT;IAEQ,oBAAoB,CAAa,EAAsB;QAC7D,gDAAgD;QAChD,MAAM,iBAAiB,EAAE;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC7C,MAAM,cAAc,KAAK,KAAK,CAAC;YAC/B,IAAI,aAAa;gBACf,OAAO,SAAS,WAAW,CAAC,EAAE;YAChC;QACF;QACA,OAAO,GAAG,sBAAsB;IAClC;IAEQ,oBAAoB,CAAa,EAAU;QACjD,iDAAiD;QACjD,MAAM,eAAe,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,OAAO,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC3C,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,gBAAgB;gBACpD,OAAO;YACT,OAAO,IAAI,KAAK,KAAK,CAAC,gBAAgB;gBACpC,OAAO;YACT;QACF;QACA,OAAO,WAAW,qBAAqB;IACzC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/sync/vidsrc-episodes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Series from '@/models/Series';\nimport Episode from '@/models/Episode'; // ✅ ADDED: Episode collection for cleanup\nimport VidSrcAPI from '@/lib/vidsrc';\nimport IMDbScraper from '@/lib/scraper';\n\nconst vidsrc = VidSrcAPI.getInstance();\nconst imdbScraper = IMDbScraper.getInstance();\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n\n    console.log('🚀 Starting COMPREHENSIVE VidSrc Episodes Sync with Embedded Architecture');\n    console.log('📋 New Architecture: Episodes embedded in Series collection with isLatestRelease flag');\n    const startTime = Date.now();\n\n    // STEP 1: Get current VidSrc latest episodes\n    console.log('📊 STEP 1: Fetching current VidSrc latest episodes...');\n    \n    // **RATE-LIMITED BATCH FETCH** - Fetch pages in smaller batches to avoid 503 errors\n    console.log('📄 Fetching VidSrc episodes pages 1-15 in rate-limited batches...');\n\n    const allEpisodes = [];\n    const batchSize = 3; // Fetch 3 pages at a time to avoid overwhelming the API\n    const delayBetweenBatches = 2000; // 2 second delay between batches\n\n    for (let i = 1; i <= 15; i += batchSize) {\n      const batchEnd = Math.min(i + batchSize - 1, 15);\n      console.log(`📦 Fetching batch: pages ${i}-${batchEnd}...`);\n\n      const batchPromises = [];\n      for (let page = i; page <= batchEnd; page++) {\n        batchPromises.push(\n          vidsrc.getLatestEpisodes(page).catch(error => {\n            console.error(`❌ Error fetching page ${page}:`, error.message || error);\n            return []; // Return empty array on error\n          })\n        );\n      }\n\n      // Wait for current batch to complete\n      const batchResults = await Promise.all(batchPromises);\n      const batchEpisodes = batchResults.flat();\n      allEpisodes.push(...batchEpisodes);\n\n      console.log(`✅ Batch ${i}-${batchEnd}: Found ${batchEpisodes.length} episodes`);\n\n      // Add delay between batches (except for the last batch)\n      if (batchEnd < 15) {\n        console.log(`⏳ Waiting ${delayBetweenBatches/1000}s before next batch...`);\n        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));\n      }\n    }\n    \n    console.log(`✅ Rate-limited fetch complete: ${allEpisodes.length} episodes from 15 pages`);\n\n    if (allEpisodes.length === 0) {\n      return NextResponse.json({\n        success: false,\n        message: 'No episodes found from VidSrc API - all pages may have failed due to rate limiting or server issues',\n        recommendation: 'Try again later or check VidSrc API status'\n      });\n    }\n\n    // STEP 2: Identify episodes to remove (cleanup) - Now working with embedded episodes\n    console.log('🧹 STEP 2: Cleaning up outdated VidSrc episodes from Series collection...');\n\n    // Get current VidSrc IMDb IDs and episode identifiers\n    const currentVidSrcEpisodes = new Set();\n    allEpisodes.forEach(ep => {\n      const episodeKey = `${ep.imdb_id}-S${ep.season}E${ep.episode}`;\n      currentVidSrcEpisodes.add(episodeKey);\n    });\n\n    // CLEANUP: Remove isLatestRelease flag from episodes no longer in VidSrc\n    console.log('🧹 Removing isLatestRelease flag from outdated episodes...');\n\n    // Update all series to remove isLatestRelease flag from episodes not in current VidSrc\n    const seriesWithEpisodes = await Series.find({\n      'episodes.isLatestRelease': true\n    });\n\n    let cleanupCount = 0;\n    for (const series of seriesWithEpisodes) {\n      let episodesUpdated = false;\n\n      series.episodes.forEach(episode => {\n        if (episode.isLatestRelease) {\n          const episodeKey = `${series.imdbId}-S${episode.season}E${episode.episode}`;\n          if (!currentVidSrcEpisodes.has(episodeKey)) {\n            episode.isLatestRelease = false; // Remove latest release flag\n            episodesUpdated = true;\n            cleanupCount++;\n          }\n        }\n      });\n\n      if (episodesUpdated) {\n        await series.save();\n      }\n    }\n\n    console.log(`📊 Episodes cleanup analysis:`);\n    console.log(`   Current VidSrc episodes: ${currentVidSrcEpisodes.size}`);\n    console.log(`   Episodes cleaned up: ${cleanupCount}`);\n\n    // CLEANUP: Remove episodes from Episode collection that are no longer in VidSrc latest\n    console.log('🧹 Cleaning up Episode collection...');\n\n    const episodeCleanupResult = await Episode.deleteMany({\n      $expr: {\n        $not: {\n          $in: [\n            { $concat: ['$imdbId', '-S', { $toString: '$season' }, 'E', { $toString: '$episode' }] },\n            Array.from(currentVidSrcEpisodes)\n          ]\n        }\n      }\n    });\n\n    console.log(`📊 Episode collection cleanup: ${episodeCleanupResult.deletedCount} episodes removed`);\n\n    // STEP 3: Process new/updated episodes\n    console.log('🔄 STEP 3: Processing new/updated episodes...');\n\n    // Get unique IMDb IDs and log sample data\n    const uniqueImdbIds = [...new Set(allEpisodes.map(ep => ep.imdb_id))];\n    console.log(`🎬 Found ${uniqueImdbIds.length} unique series`);\n\n    // Debug: Show sample VidSrc episode data\n    if (allEpisodes.length > 0) {\n      console.log('📝 Sample VidSrc episode data:', allEpisodes.slice(0, 2).map(ep => ({\n        imdb_id: ep.imdb_id,\n        show_title: ep.show_title,\n        season: ep.season,\n        episode: ep.episode,\n        title: ep.title\n      })));\n    }\n    \n    // **PARALLEL SERIES PROCESSING** - Process series in batches\n    const seriesBatchSize = 10; // Process 10 series at a time\n    const processedSeries = new Map();\n    \n    for (let i = 0; i < uniqueImdbIds.length; i += seriesBatchSize) {\n      const batch = uniqueImdbIds.slice(i, i + seriesBatchSize);\n      console.log(`🔄 Processing series batch ${Math.floor(i/seriesBatchSize) + 1}/${Math.ceil(uniqueImdbIds.length/seriesBatchSize)}`);\n      \n      const batchPromises = batch.map(async (imdbId) => {\n        try {\n          // Check if series exists\n          let series = await Series.findOne({ imdbId });\n          \n          if (!series) {\n            console.log(`🆕 Creating series: ${imdbId}`);\n            \n            // Find episode data for this series\n            const episodeData = allEpisodes.find(ep => ep.imdb_id === imdbId);\n            \n            try {\n              // Scrape series data\n              const seriesData = await imdbScraper.scrapeSeries(imdbId);\n              const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;\n              const currentYear = new Date().getFullYear();\n              \n              series = new Series({\n                imdbId,\n                title: seriesData.title || episodeData?.show_title || 'Unknown Series',\n                description: seriesData.description,\n                posterUrl: seriesData.posterUrl,\n                imdbRating: seriesData.imdbRating,\n                startYear: seriesData.startYear || currentYear,\n                endYear: seriesData.endYear,\n                genres: seriesData.genres || [],\n                cast: seriesData.cast || [],\n                director: seriesData.director,\n                language: seriesData.language,\n                country: seriesData.country,\n                runtime: seriesData.runtime,\n                embedUrl,\n                type: 'series',\n                episodes: [], // ✅ Initialize empty episodes array\n                episodeCount: 0,\n                lastEpisodeUpdate: new Date()\n              });\n              \n              await series.save();\n              console.log(`✅ Created: ${series.title}`);\n            } catch (error) {\n              // Create minimal series if scraping fails\n              const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;\n              const currentYear = new Date().getFullYear();\n              \n              series = new Series({\n                imdbId,\n                title: episodeData?.show_title || 'Unknown Series',\n                startYear: currentYear,\n                embedUrl,\n                type: 'series',\n                genres: [],\n                episodes: [], // ✅ Initialize empty episodes array\n                episodeCount: 0,\n                lastEpisodeUpdate: new Date()\n              });\n              \n              await series.save();\n              console.log(`✅ Created minimal: ${series.title}`);\n            }\n          }\n          \n          // Now process ALL episodes for this series - USING EMBEDDED EPISODES\n          const seriesEpisodes = allEpisodes.filter(ep => ep.imdb_id === imdbId);\n          console.log(`📺 Processing ${seriesEpisodes.length} VidSrc episodes for ${series.title}`);\n\n          let episodesUpdated = false;\n\n          // First, clear isLatestRelease flag from all episodes in this series\n          series.episodes.forEach(episode => {\n            if (episode.isLatestRelease) {\n              episode.isLatestRelease = false;\n              episodesUpdated = true;\n            }\n          });\n\n          // Process all VidSrc episodes for this series and mark them as latest releases\n          for (const episodeData of seriesEpisodes) {\n            // Generate episode embed URL\n            const episodeEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}&season=${episodeData.season}&episode=${episodeData.episode}`;\n\n            // Check if episode already exists in embedded episodes array\n            const existingEpisodeIndex = series.episodes.findIndex(ep =>\n              ep.season === parseInt(episodeData.season) && ep.episode === parseInt(episodeData.episode)\n            );\n\n            if (existingEpisodeIndex === -1) {\n              // Add new embedded episode and mark as VidSrc latest release\n              const newEpisode = {\n                season: parseInt(episodeData.season),\n                episode: parseInt(episodeData.episode),\n                episodeTitle: episodeData.title || `Episode ${episodeData.episode}`,\n                description: episodeData.description || `${series.title} - Season ${episodeData.season}, Episode ${episodeData.episode}`,\n                airDate: episodeData.air_date ? new Date(episodeData.air_date) : new Date(),\n                runtime: episodeData.runtime || '45 min',\n                embedUrl: episodeEmbedUrl,\n                embedUrlTmdb: episodeData.embed_url_tmdb || '',\n                vidsrcUrl: episodeEmbedUrl,\n                vidsrcTmdbUrl: episodeData.embed_url_tmdb || '',\n                isLatestRelease: true, // ✅ Mark as VidSrc latest release\n                createdAt: new Date(),\n                updatedAt: new Date()\n              };\n\n              series.episodes.push(newEpisode);\n              episodesUpdated = true;\n              console.log(`✅ Added NEW VidSrc latest episode: S${episodeData.season}E${episodeData.episode} - ${episodeData.title || 'Episode ' + episodeData.episode}`);\n            } else {\n              // Update existing embedded episode and mark as VidSrc latest release\n              const existingEpisode = series.episodes[existingEpisodeIndex];\n\n              // Update episode data with VidSrc information\n              existingEpisode.embedUrl = episodeEmbedUrl;\n              existingEpisode.vidsrcUrl = episodeEmbedUrl;\n              existingEpisode.embedUrlTmdb = episodeData.embed_url_tmdb || existingEpisode.embedUrlTmdb || '';\n              existingEpisode.vidsrcTmdbUrl = episodeData.embed_url_tmdb || existingEpisode.vidsrcTmdbUrl || '';\n              existingEpisode.isLatestRelease = true; // ✅ Mark as VidSrc latest release\n              existingEpisode.updatedAt = new Date();\n\n              // Update title if VidSrc has a better one\n              if (episodeData.title && episodeData.title !== `Episode ${episodeData.episode}`) {\n                existingEpisode.episodeTitle = episodeData.title;\n              }\n\n              episodesUpdated = true;\n              console.log(`🔄 Updated existing VidSrc latest episode: S${episodeData.season}E${episodeData.episode} - ${existingEpisode.episodeTitle}`);\n            }\n          }\n\n          // Log summary of VidSrc latest episodes processed\n          if (seriesEpisodes.length > 0) {\n            console.log(`✅ Processed ${seriesEpisodes.length} VidSrc latest episodes for ${series.title}`);\n          } else {\n            console.log(`⚠️ No VidSrc episodes found for ${series.title}`);\n          }\n\n          // Save series with updated embedded episodes\n          if (episodesUpdated) {\n            // CRITICAL FIX: After processing all episodes for this series,\n            // ensure only the ACTUAL latest episode (by season/episode number) has isLatestRelease = true\n            const latestEpisodes = series.episodes.filter(ep => ep.isLatestRelease);\n\n            if (latestEpisodes.length > 1) {\n              // Sort by season and episode to find the actual latest\n              const sortedLatest = latestEpisodes.sort((a, b) => {\n                if (a.season !== b.season) return b.season - a.season;\n                return b.episode - a.episode;\n              });\n\n              // Remove isLatestRelease flag from all except the actual latest\n              series.episodes.forEach(episode => {\n                if (episode.isLatestRelease) {\n                  episode.isLatestRelease = false;\n                }\n              });\n\n              // Set only the actual latest episode as latest\n              const actualLatest = sortedLatest[0];\n              const actualLatestIndex = series.episodes.findIndex(ep =>\n                ep.season === actualLatest.season && ep.episode === actualLatest.episode\n              );\n\n              if (actualLatestIndex >= 0) {\n                series.episodes[actualLatestIndex].isLatestRelease = true;\n                console.log(`🎯 Set ONLY S${actualLatest.season}E${actualLatest.episode} as latest for ${series.title} (was ${latestEpisodes.length} episodes)`);\n              }\n            }\n\n            series.episodeCount = series.episodes.length;\n            series.lastEpisodeUpdate = new Date();\n            await series.save();\n          }\n\n          processedSeries.set(imdbId, series);\n          return { imdbId, success: true };\n        } catch (error) {\n          console.error(`❌ Error processing series ${imdbId}:`, error);\n          return { imdbId, success: false, error: error.message };\n        }\n      });\n      \n      await Promise.all(batchPromises);\n    }\n    \n    console.log(`✅ Series processing complete: ${processedSeries.size} series processed`);\n    \n    // Update sync timestamp\n    const syncTime = new Date();\n    \n    const endTime = Date.now();\n    const duration = (endTime - startTime) / 1000;\n    \n    console.log(`🎉 VidSrc Episodes Sync Complete in ${duration}s`);\n    \n    return NextResponse.json({\n      success: true,\n      message: `Sync completed successfully`,\n      stats: {\n        totalEpisodes: allEpisodes.length,\n        uniqueSeries: uniqueImdbIds.length,\n        processedSeries: processedSeries.size,\n        duration: `${duration}s`,\n        syncTime: syncTime.toISOString()\n      }\n    });\n    \n  } catch (error) {\n    console.error('❌ VidSrc Episodes Sync Error:', error);\n    return NextResponse.json(\n      { \n        success: false,\n        error: 'Sync failed',\n        message: error.message\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// GET endpoint to check last sync status\nexport async function GET() {\n  try {\n    await connectDB();\n    \n    const totalSeries = await Series.countDocuments({ type: 'series' });\n    const totalEpisodes = await Episode.countDocuments({});\n    \n    return NextResponse.json({\n      success: true,\n      stats: {\n        totalSeries,\n        totalEpisodes,\n        lastSync: 'Check logs for last sync time'\n      }\n    });\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Failed to get sync status' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA,sNAAwC,0CAA0C;AAClF;AACA;;;;;;;AAEA,MAAM,SAAS,sHAAA,CAAA,UAAS,CAAC,WAAW;AACpC,MAAM,cAAc,uHAAA,CAAA,UAAW,CAAC,WAAW;AAEpC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,MAAM,YAAY,KAAK,GAAG;QAE1B,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;QAEZ,oFAAoF;QACpF,QAAQ,GAAG,CAAC;QAEZ,MAAM,cAAc,EAAE;QACtB,MAAM,YAAY,GAAG,wDAAwD;QAC7E,MAAM,sBAAsB,MAAM,iCAAiC;QAEnE,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,KAAK,UAAW;YACvC,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,YAAY,GAAG;YAC7C,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC;YAE1D,MAAM,gBAAgB,EAAE;YACxB,IAAK,IAAI,OAAO,GAAG,QAAQ,UAAU,OAAQ;gBAC3C,cAAc,IAAI,CAChB,OAAO,iBAAiB,CAAC,MAAM,KAAK,CAAC,CAAA;oBACnC,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,OAAO,IAAI;oBACjE,OAAO,EAAE,EAAE,8BAA8B;gBAC3C;YAEJ;YAEA,qCAAqC;YACrC,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;YACvC,MAAM,gBAAgB,aAAa,IAAI;YACvC,YAAY,IAAI,IAAI;YAEpB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,SAAS,QAAQ,EAAE,cAAc,MAAM,CAAC,SAAS,CAAC;YAE9E,wDAAwD;YACxD,IAAI,WAAW,IAAI;gBACjB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,sBAAoB,KAAK,sBAAsB,CAAC;gBACzE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,YAAY,MAAM,CAAC,uBAAuB,CAAC;QAEzF,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,gBAAgB;YAClB;QACF;QAEA,qFAAqF;QACrF,QAAQ,GAAG,CAAC;QAEZ,sDAAsD;QACtD,MAAM,wBAAwB,IAAI;QAClC,YAAY,OAAO,CAAC,CAAA;YAClB,MAAM,aAAa,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE;YAC9D,sBAAsB,GAAG,CAAC;QAC5B;QAEA,yEAAyE;QACzE,QAAQ,GAAG,CAAC;QAEZ,uFAAuF;QACvF,MAAM,qBAAqB,MAAM,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC;YAC3C,4BAA4B;QAC9B;QAEA,IAAI,eAAe;QACnB,KAAK,MAAM,UAAU,mBAAoB;YACvC,IAAI,kBAAkB;YAEtB,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACtB,IAAI,QAAQ,eAAe,EAAE;oBAC3B,MAAM,aAAa,GAAG,OAAO,MAAM,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;oBAC3E,IAAI,CAAC,sBAAsB,GAAG,CAAC,aAAa;wBAC1C,QAAQ,eAAe,GAAG,OAAO,6BAA6B;wBAC9D,kBAAkB;wBAClB;oBACF;gBACF;YACF;YAEA,IAAI,iBAAiB;gBACnB,MAAM,OAAO,IAAI;YACnB;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC;QAC3C,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,sBAAsB,IAAI,EAAE;QACvE,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,cAAc;QAErD,uFAAuF;QACvF,QAAQ,GAAG,CAAC;QAEZ,MAAM,uBAAuB,MAAM,0HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YACpD,OAAO;gBACL,MAAM;oBACJ,KAAK;wBACH;4BAAE,SAAS;gCAAC;gCAAW;gCAAM;oCAAE,WAAW;gCAAU;gCAAG;gCAAK;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBACvF,MAAM,IAAI,CAAC;qBACZ;gBACH;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,qBAAqB,YAAY,CAAC,iBAAiB,CAAC;QAElG,uCAAuC;QACvC,QAAQ,GAAG,CAAC;QAEZ,0CAA0C;QAC1C,MAAM,gBAAgB;eAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,KAAM,GAAG,OAAO;SAAG;QACrE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,cAAc,MAAM,CAAC,cAAc,CAAC;QAE5D,yCAAyC;QACzC,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,GAAG,CAAC,kCAAkC,YAAY,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,KAAM,CAAC;oBAC/E,SAAS,GAAG,OAAO;oBACnB,YAAY,GAAG,UAAU;oBACzB,QAAQ,GAAG,MAAM;oBACjB,SAAS,GAAG,OAAO;oBACnB,OAAO,GAAG,KAAK;gBACjB,CAAC;QACH;QAEA,6DAA6D;QAC7D,MAAM,kBAAkB,IAAI,8BAA8B;QAC1D,MAAM,kBAAkB,IAAI;QAE5B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,gBAAiB;YAC9D,MAAM,QAAQ,cAAc,KAAK,CAAC,GAAG,IAAI;YACzC,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,IAAE,mBAAmB,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,cAAc,MAAM,GAAC,kBAAkB;YAEhI,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO;gBACrC,IAAI;oBACF,yBAAyB;oBACzB,IAAI,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;wBAAE;oBAAO;oBAE3C,IAAI,CAAC,QAAQ;wBACX,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;wBAE3C,oCAAoC;wBACpC,MAAM,cAAc,YAAY,IAAI,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK;wBAE1D,IAAI;4BACF,qBAAqB;4BACrB,MAAM,aAAa,MAAM,YAAY,YAAY,CAAC;4BAClD,MAAM,WAAW,CAAC,gCAAgC,EAAE,QAAQ;4BAC5D,MAAM,cAAc,IAAI,OAAO,WAAW;4BAE1C,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;gCAClB;gCACA,OAAO,WAAW,KAAK,IAAI,aAAa,cAAc;gCACtD,aAAa,WAAW,WAAW;gCACnC,WAAW,WAAW,SAAS;gCAC/B,YAAY,WAAW,UAAU;gCACjC,WAAW,WAAW,SAAS,IAAI;gCACnC,SAAS,WAAW,OAAO;gCAC3B,QAAQ,WAAW,MAAM,IAAI,EAAE;gCAC/B,MAAM,WAAW,IAAI,IAAI,EAAE;gCAC3B,UAAU,WAAW,QAAQ;gCAC7B,UAAU,WAAW,QAAQ;gCAC7B,SAAS,WAAW,OAAO;gCAC3B,SAAS,WAAW,OAAO;gCAC3B;gCACA,MAAM;gCACN,UAAU,EAAE;gCACZ,cAAc;gCACd,mBAAmB,IAAI;4BACzB;4BAEA,MAAM,OAAO,IAAI;4BACjB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,KAAK,EAAE;wBAC1C,EAAE,OAAO,OAAO;4BACd,0CAA0C;4BAC1C,MAAM,WAAW,CAAC,gCAAgC,EAAE,QAAQ;4BAC5D,MAAM,cAAc,IAAI,OAAO,WAAW;4BAE1C,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;gCAClB;gCACA,OAAO,aAAa,cAAc;gCAClC,WAAW;gCACX;gCACA,MAAM;gCACN,QAAQ,EAAE;gCACV,UAAU,EAAE;gCACZ,cAAc;gCACd,mBAAmB,IAAI;4BACzB;4BAEA,MAAM,OAAO,IAAI;4BACjB,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,KAAK,EAAE;wBAClD;oBACF;oBAEA,qEAAqE;oBACrE,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK;oBAC/D,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,MAAM,CAAC,qBAAqB,EAAE,OAAO,KAAK,EAAE;oBAExF,IAAI,kBAAkB;oBAEtB,qEAAqE;oBACrE,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAA;wBACtB,IAAI,QAAQ,eAAe,EAAE;4BAC3B,QAAQ,eAAe,GAAG;4BAC1B,kBAAkB;wBACpB;oBACF;oBAEA,+EAA+E;oBAC/E,KAAK,MAAM,eAAe,eAAgB;wBACxC,6BAA6B;wBAC7B,MAAM,kBAAkB,CAAC,gCAAgC,EAAE,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,SAAS,EAAE,YAAY,OAAO,EAAE;wBAE/H,6DAA6D;wBAC7D,MAAM,uBAAuB,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAA,KACrD,GAAG,MAAM,KAAK,SAAS,YAAY,MAAM,KAAK,GAAG,OAAO,KAAK,SAAS,YAAY,OAAO;wBAG3F,IAAI,yBAAyB,CAAC,GAAG;4BAC/B,6DAA6D;4BAC7D,MAAM,aAAa;gCACjB,QAAQ,SAAS,YAAY,MAAM;gCACnC,SAAS,SAAS,YAAY,OAAO;gCACrC,cAAc,YAAY,KAAK,IAAI,CAAC,QAAQ,EAAE,YAAY,OAAO,EAAE;gCACnE,aAAa,YAAY,WAAW,IAAI,GAAG,OAAO,KAAK,CAAC,UAAU,EAAE,YAAY,MAAM,CAAC,UAAU,EAAE,YAAY,OAAO,EAAE;gCACxH,SAAS,YAAY,QAAQ,GAAG,IAAI,KAAK,YAAY,QAAQ,IAAI,IAAI;gCACrE,SAAS,YAAY,OAAO,IAAI;gCAChC,UAAU;gCACV,cAAc,YAAY,cAAc,IAAI;gCAC5C,WAAW;gCACX,eAAe,YAAY,cAAc,IAAI;gCAC7C,iBAAiB;gCACjB,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;4BAEA,OAAO,QAAQ,CAAC,IAAI,CAAC;4BACrB,kBAAkB;4BAClB,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,GAAG,EAAE,YAAY,KAAK,IAAI,aAAa,YAAY,OAAO,EAAE;wBAC3J,OAAO;4BACL,qEAAqE;4BACrE,MAAM,kBAAkB,OAAO,QAAQ,CAAC,qBAAqB;4BAE7D,8CAA8C;4BAC9C,gBAAgB,QAAQ,GAAG;4BAC3B,gBAAgB,SAAS,GAAG;4BAC5B,gBAAgB,YAAY,GAAG,YAAY,cAAc,IAAI,gBAAgB,YAAY,IAAI;4BAC7F,gBAAgB,aAAa,GAAG,YAAY,cAAc,IAAI,gBAAgB,aAAa,IAAI;4BAC/F,gBAAgB,eAAe,GAAG,MAAM,kCAAkC;4BAC1E,gBAAgB,SAAS,GAAG,IAAI;4BAEhC,0CAA0C;4BAC1C,IAAI,YAAY,KAAK,IAAI,YAAY,KAAK,KAAK,CAAC,QAAQ,EAAE,YAAY,OAAO,EAAE,EAAE;gCAC/E,gBAAgB,YAAY,GAAG,YAAY,KAAK;4BAClD;4BAEA,kBAAkB;4BAClB,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,GAAG,EAAE,gBAAgB,YAAY,EAAE;wBAC1I;oBACF;oBAEA,kDAAkD;oBAClD,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,eAAe,MAAM,CAAC,4BAA4B,EAAE,OAAO,KAAK,EAAE;oBAC/F,OAAO;wBACL,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,OAAO,KAAK,EAAE;oBAC/D;oBAEA,6CAA6C;oBAC7C,IAAI,iBAAiB;wBACnB,+DAA+D;wBAC/D,8FAA8F;wBAC9F,MAAM,iBAAiB,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAA,KAAM,GAAG,eAAe;wBAEtE,IAAI,eAAe,MAAM,GAAG,GAAG;4BAC7B,uDAAuD;4BACvD,MAAM,eAAe,eAAe,IAAI,CAAC,CAAC,GAAG;gCAC3C,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;gCACrD,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO;4BAC9B;4BAEA,gEAAgE;4BAChE,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAA;gCACtB,IAAI,QAAQ,eAAe,EAAE;oCAC3B,QAAQ,eAAe,GAAG;gCAC5B;4BACF;4BAEA,+CAA+C;4BAC/C,MAAM,eAAe,YAAY,CAAC,EAAE;4BACpC,MAAM,oBAAoB,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAA,KAClD,GAAG,MAAM,KAAK,aAAa,MAAM,IAAI,GAAG,OAAO,KAAK,aAAa,OAAO;4BAG1E,IAAI,qBAAqB,GAAG;gCAC1B,OAAO,QAAQ,CAAC,kBAAkB,CAAC,eAAe,GAAG;gCACrD,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,eAAe,MAAM,CAAC,UAAU,CAAC;4BACjJ;wBACF;wBAEA,OAAO,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM;wBAC5C,OAAO,iBAAiB,GAAG,IAAI;wBAC/B,MAAM,OAAO,IAAI;oBACnB;oBAEA,gBAAgB,GAAG,CAAC,QAAQ;oBAC5B,OAAO;wBAAE;wBAAQ,SAAS;oBAAK;gBACjC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC,EAAE;oBACtD,OAAO;wBAAE;wBAAQ,SAAS;wBAAO,OAAO,MAAM,OAAO;oBAAC;gBACxD;YACF;YAEA,MAAM,QAAQ,GAAG,CAAC;QACpB;QAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,gBAAgB,IAAI,CAAC,iBAAiB,CAAC;QAEpF,wBAAwB;QACxB,MAAM,WAAW,IAAI;QAErB,MAAM,UAAU,KAAK,GAAG;QACxB,MAAM,WAAW,CAAC,UAAU,SAAS,IAAI;QAEzC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS,CAAC,2BAA2B,CAAC;YACtC,OAAO;gBACL,eAAe,YAAY,MAAM;gBACjC,cAAc,cAAc,MAAM;gBAClC,iBAAiB,gBAAgB,IAAI;gBACrC,UAAU,GAAG,SAAS,CAAC,CAAC;gBACxB,UAAU,SAAS,WAAW;YAChC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,MAAM,OAAO;QACxB,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,cAAc,MAAM,yHAAA,CAAA,UAAM,CAAC,cAAc,CAAC;YAAE,MAAM;QAAS;QACjE,MAAM,gBAAgB,MAAM,0HAAA,CAAA,UAAO,CAAC,cAAc,CAAC,CAAC;QAEpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;gBACL;gBACA;gBACA,UAAU;YACZ;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}