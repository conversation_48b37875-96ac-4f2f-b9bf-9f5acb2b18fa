import { Metadata } from 'next';
import connectDB from '@/lib/mongodb';
import Episode from '@/models/Episode';
import Series from '@/models/Series';
import ContentGrid from '@/components/ContentGrid';
import FilterSidebar from '@/components/FilterSidebar';
import { SEOGenerator } from '@/lib/seo';

interface EpisodesPageProps {
  searchParams: Promise<{
    genre?: string;
    language?: string;
    country?: string;
    page?: string;
  }>;
}

/**
 * OPTIMIZED Episodes Page - Fixes memory limit error
 * Uses efficient queries instead of complex aggregations
 */
async function getEpisodes(searchParams: any) {
  try {
    console.log('🚀 OPTIMIZED episodes fetch starting...');
    console.log('📊 Search params:', searchParams);

    await connectDB();

    // OPTIMIZATION 1: Build series filter first (smaller dataset)
    const seriesFilter: any = {};
    
    if (searchParams.genre) {
      seriesFilter.genres = { $in: [searchParams.genre] };
      console.log(`🎭 Filtering by genre: ${searchParams.genre}`);
    }
    
    if (searchParams.language) {
      seriesFilter.language = searchParams.language;
      console.log(`🗣️ Filtering by language: ${searchParams.language}`);
    }
    
    if (searchParams.country) {
      seriesFilter.country = searchParams.country;
      console.log(`🌍 Filtering by country: ${searchParams.country}`);
    }

    let episodes = [];

    if (Object.keys(seriesFilter).length > 0) {
      // OPTIMIZATION 2: Get matching series first (much faster)
      console.log('🔍 Finding matching series first...');
      const matchingSeries = await Series.find(seriesFilter, { imdbId: 1, title: 1, posterUrl: 1 }).lean();
      const seriesImdbIds = matchingSeries.map(s => s.imdbId);
      
      console.log(`📊 Found ${matchingSeries.length} matching series`);
      
      if (seriesImdbIds.length > 0) {
        // OPTIMIZATION 3: Simple aggregation for latest episodes
        episodes = await Episode.aggregate([
          { $match: { imdbId: { $in: seriesImdbIds } } },
          { $sort: { imdbId: 1, season: -1, episode: -1 } },
          {
            $group: {
              _id: '$imdbId',
              latestEpisode: { $first: '$$ROOT' }
            }
          },
          { $replaceRoot: { newRoot: '$latestEpisode' } },
          { $sort: { createdAt: -1 } },
          { $limit: 200 } // Smaller limit for better performance
        ], { 
          allowDiskUse: true,
          maxTimeMS: 10000 // Shorter timeout
        });

        // OPTIMIZATION 4: Add series info efficiently
        const seriesMap = new Map();
        matchingSeries.forEach(series => {
          seriesMap.set(series.imdbId, series);
        });
        
        episodes.forEach(episode => {
          const series = seriesMap.get(episode.imdbId);
          if (series) {
            episode.seriesTitle = series.title || episode.seriesTitle;
            episode.posterUrl = episode.posterUrl || series.posterUrl;
          }
        });
      }
    } else {
      // OPTIMIZATION 5: No filters - direct query with limit
      console.log('🔍 No filters - getting latest episodes directly...');
      episodes = await Episode.find({})
        .sort({ createdAt: -1 })
        .limit(200)
        .lean();
    }

    console.log(`✅ OPTIMIZED fetch completed: ${episodes.length} episodes`);
    return episodes;

  } catch (error) {
    console.error('❌ Error fetching episodes:', error);
    return [];
  }
}

async function getFilterOptions() {
  try {
    await connectDB();
    
    // Get filter options from Series collection (more reliable)
    const [genreResults, languageResults, countryResults] = await Promise.all([
      Series.aggregate([
        { $unwind: '$genres' },
        { $group: { _id: '$genres', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 20 }
      ]),
      Series.aggregate([
        { $match: { language: { $exists: true, $ne: null } } },
        { $group: { _id: '$language', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      Series.aggregate([
        { $match: { country: { $exists: true, $ne: null } } },
        { $group: { _id: '$country', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ])
    ]);

    return {
      genres: genreResults.map(g => ({ name: g._id, count: g.count })),
      languages: languageResults.map(l => ({ name: l._id, count: l.count })),
      countries: countryResults.map(c => ({ name: c._id, count: c.count }))
    };
  } catch (error) {
    console.error('Error fetching filter options:', error);
    return { genres: [], languages: [], countries: [] };
  }
}

export async function generateMetadata({ searchParams }: EpisodesPageProps): Promise<Metadata> {
  const params = await searchParams;
  return SEOGenerator.generateEpisodesPageMetadata(params);
}

export default async function EpisodesPageOptimized({ searchParams }: EpisodesPageProps) {
  const params = await searchParams;
  
  // Parallel data fetching
  const [episodes, filterOptions] = await Promise.all([
    getEpisodes(params),
    getFilterOptions()
  ]);

  // Transform episodes for ContentGrid
  const transformedEpisodes = episodes.map(episode => ({
    id: episode._id,
    imdbId: episode.imdbId,
    title: episode.episodeTitle || `Episode ${episode.episode}`,
    year: new Date(episode.airDate || episode.createdAt).getFullYear(),
    posterUrl: episode.posterUrl,
    rating: episode.imdbRating,
    type: 'episode' as const,
    seriesTitle: episode.seriesTitle,
    season: episode.season,
    episode: episode.episode,
    description: episode.description
  }));

  return (
    <div className="min-h-screen bg-black">
      {/* SEO Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SEOGenerator.generateWebsiteSchema(),
            SEOGenerator.generateBreadcrumbSchema([
              { name: 'Home', url: '/' },
              { name: 'Episodes', url: '/episodes' }
            ])
          ])
        }}
      />

      <div className="max-w-[2560px] mx-auto px-8 lg:px-24 py-12">
        <div className="flex gap-8">
          {/* Filter Sidebar */}
          <div className="w-80 flex-shrink-0">
            <FilterSidebar
              genres={filterOptions.genres}
              languages={filterOptions.languages}
              countries={filterOptions.countries}
              currentFilters={params}
              basePath="/episodes"
            />
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">
                Latest Episodes
              </h1>
              <p className="text-gray-400">
                Discover the latest episodes from your favorite TV series
              </p>
            </div>

            <ContentGrid 
              content={transformedEpisodes}
              type="episodes"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
