'use client';

import { useState, useCallback } from 'react';

interface Episode {
  _id: string;
  season: number;
  episode: number;
  episodeTitle?: string;
  airDate?: string;
  runtime?: string;
  imdbRating?: number;
  description?: string;
  posterUrl?: string;
  embedUrl: string;
}

interface EpisodeStats {
  totalFound: number;
  newEpisodesAdded: number;
  existingEpisodesUpdated: number;
  totalEpisodesInDB: number;
  seasonBreakdown: Record<number, {
    episodeCount: number;
    episodes: number[];
    missingEpisodes: number[];
    hasGaps: boolean;
    completionPercentage: number;
    expectedEpisodes?: number;
  }>;
  episodeRange: {
    minSeason: number;
    maxSeason: number;
    minEpisode: number;
    maxEpisode: number;
  };
  duplicatesRemoved: number;
  errors: string[];
  verificationResults: {
    totalSeasonsFound: number;
    totalSeasonsWithGaps: number;
    overallCompletionPercentage: number;
    missingSeasonRanges: string[];
    suspiciousGaps: Array<{
      season: number;
      missingRange: string;
      severity: 'minor' | 'major' | 'critical';
    }>;
  };
}

interface EpisodeCheckResult {
  message: string;
  stats: EpisodeStats;
  episodes: Episode[];
  lastChecked: string;
}

interface UseEpisodeCheckerReturn {
  isChecking: boolean;
  checkEpisodes: (seriesId: string) => Promise<EpisodeCheckResult | null>;
  lastCheckResult: EpisodeCheckResult | null;
  error: string | null;
}

export const useEpisodeChecker = (): UseEpisodeCheckerReturn => {
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheckResult, setLastCheckResult] = useState<EpisodeCheckResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkEpisodes = useCallback(async (seriesId: string): Promise<EpisodeCheckResult | null> => {
    if (isChecking) {
      return null; // Prevent multiple simultaneous checks
    }

    setIsChecking(true);
    setError(null);

    try {
      console.log(`🔍 Checking episodes for series: ${seriesId}`);
      
      const response = await fetch(`/api/series/${seriesId}/episodes/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to check episodes: ${response.statusText}`);
      }

      const result: EpisodeCheckResult = await response.json();
      
      console.log(`✅ Episode check complete:`, result);
      setLastCheckResult(result);
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check episodes';
      console.error('❌ Error checking episodes:', errorMessage);
      setError(errorMessage);
      return null;
    } finally {
      setIsChecking(false);
    }
  }, [isChecking]);

  return {
    isChecking,
    checkEpisodes,
    lastCheckResult,
    error
  };
};
