'use client';

import React, { useState } from 'react';
import { Loader2, Database, CheckCircle, AlertCircle } from 'lucide-react';
import Button from './ui/Button';

const InitializeButton: React.FC = () => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleInitialize = async () => {
    setIsInitializing(true);
    setStatus('loading');
    setMessage('Initializing database with real content...');

    try {
      const response = await fetch('/api/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setStatus('success');
        setMessage(`Database initialized successfully! Added ${data.counts.movies} movies, ${data.counts.series} series, and ${data.counts.episodes} episodes.`);
        
        // Refresh the page after a short delay
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setStatus('error');
        setMessage(data.error || 'Failed to initialize database');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Network error occurred while initializing database');
      console.error('Initialization error:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="animate-spin" size={20} />;
      case 'success':
        return <CheckCircle className="text-green-400" size={20} />;
      case 'error':
        return <AlertCircle className="text-red-400" size={20} />;
      default:
        return <Database size={20} />;
    }
  };

  const getButtonText = () => {
    switch (status) {
      case 'loading':
        return 'Initializing...';
      case 'success':
        return 'Initialized!';
      case 'error':
        return 'Try Again';
      default:
        return 'Initialize Database';
    }
  };

  const getButtonVariant = () => {
    switch (status) {
      case 'success':
        return 'primary' as const;
      case 'error':
        return 'secondary' as const;
      default:
        return 'primary' as const;
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <Button
        onClick={handleInitialize}
        disabled={isInitializing || status === 'success'}
        variant={getButtonVariant()}
        size="lg"
        className="flex items-center space-x-2 w-full justify-center"
      >
        {getStatusIcon()}
        <span>{getButtonText()}</span>
      </Button>
      
      {message && (
        <div className={`mt-4 p-4 rounded-lg ${
          status === 'success'
            ? 'bg-green-900/20 border border-green-500/20 text-green-400'
            : status === 'error'
            ? 'bg-red-900/20 border border-red-500/20 text-red-400'
            : 'bg-gray-800/20 border border-gray-500/20 text-gray-400'
        }`}>
          <p className="text-sm">{message}</p>
        </div>
      )}
      
      {status === 'loading' && (
        <div className="mt-4 text-center">
          <p className="text-gray-400 text-sm">
            This may take a few minutes as we scrape content from IMDb...
          </p>
        </div>
      )}
    </div>
  );
};

export default InitializeButton;
