import VidSrcScheduler from './vidsrcScheduler';

let schedulerInitialized = false;

/**
 * Initialize the VidSrc scheduler once when the app starts
 * This should be called from a layout or middleware
 */
export async function initializeScheduler(): Promise<void> {
  if (schedulerInitialized) {
    return; // Already initialized
  }

  try {
    console.log('🚀 Initializing VidSrc Scheduler...');
    const scheduler = VidSrcScheduler.getInstance();
    await scheduler.initialize();
    schedulerInitialized = true;
    console.log('✅ VidSrc Scheduler initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize VidSrc Scheduler:', error);
    // Don't throw - app should still work even if scheduler fails
  }
}

/**
 * Check if scheduler is initialized
 */
export function isSchedulerInitialized(): boolean {
  return schedulerInitialized;
}
