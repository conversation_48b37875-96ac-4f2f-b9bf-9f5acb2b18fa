import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * Migrate existing episodes to add isLatestRelease field
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();

    console.log('🔄 Migrating existing episodes to add isLatestRelease field...');

    // Use MongoDB updateMany to add isLatestRelease field to all episodes
    const result = await Series.updateMany(
      { 'episodes.0': { $exists: true } },
      { 
        $set: { 
          'episodes.$[].isLatestRelease': false 
        } 
      }
    );

    console.log(`✅ Migration result:`, result);

    // Now set some episodes as latest releases for testing
    const testResult = await Series.updateMany(
      { 'episodes.0': { $exists: true } },
      { 
        $set: { 
          'episodes.0.isLatestRelease': true 
        } 
      },
      { limit: 50 }
    );

    console.log(`✅ Test flags result:`, testResult);

    // Verify the migration worked
    const verifyCount = await Series.countDocuments({
      'episodes.isLatestRelease': true
    });

    const sampleSeries = await Series.findOne({
      'episodes.isLatestRelease': true
    }).lean();

    return NextResponse.json({
      success: true,
      message: 'Migrated episodes to add isLatestRelease field',
      stats: {
        migrationResult: result,
        testResult: testResult,
        verificationCount: verifyCount,
        sampleEpisode: sampleSeries?.episodes?.[0] ? {
          season: sampleSeries.episodes[0].season,
          episode: sampleSeries.episodes[0].episode,
          isLatestRelease: sampleSeries.episodes[0].isLatestRelease,
          hasField: sampleSeries.episodes[0].hasOwnProperty('isLatestRelease')
        } : null
      }
    });

  } catch (error) {
    console.error('❌ Migration error:', error);
    return NextResponse.json(
      { error: 'Failed to migrate episodes', message: error.message },
      { status: 500 }
    );
  }
}
