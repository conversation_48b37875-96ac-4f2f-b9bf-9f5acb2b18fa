import React, { Suspense } from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { apiClient } from '@/lib/api';
import ContentGrid from '@/components/ContentGrid';
import LoadingSpinner from '@/components/LoadingSpinner';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface SearchPageProps {
  searchParams: Promise<{
    q?: string;
    type?: 'all' | 'movies' | 'series' | 'episodes';
    page?: string;
  }>;
}

export async function generateMetadata({ searchParams }: SearchPageProps): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  const { q: query, type = 'all' } = resolvedSearchParams;

  if (!query) {
    return SEOGenerator.generatePageMetadata(
      'Search Movies and TV Series',
      'Search for movies, TV series, and episodes. Find your favorite content to watch online free in HD quality.',
      '/search',
      ['search movies', 'search series', 'find movies', 'find TV shows']
    );
  }

  const typeText = type === 'all' ? 'Content' : type === 'movies' ? 'Movies' : type === 'series' ? 'TV Series' : 'Episodes';
  const title = `Search Results for "${query}" - ${typeText}`;
  const description = `Search results for "${query}" ${typeText.toLowerCase()}. Watch ${query} online free in HD quality on StreamZen.`;

  return SEOGenerator.generatePageMetadata(
    title,
    description,
    `/search?q=${encodeURIComponent(query)}&type=${type}`,
    [query, `${query} ${type}`, `watch ${query}`, `${query} online`]
  );
}

async function performSearch(query: string, type: string = 'all', page: number = 1) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/search?q=${encodeURIComponent(query)}&type=${type}&page=${page}&limit=24`);
    if (!response.ok) throw new Error('Search failed');
    return await response.json();
  } catch (error) {
    console.error('Search error:', error);
    return { results: [], pagination: { page: 1, limit: 24, total: 0, pages: 0 } };
  }
}

  const handleSearch = (query: string) => {
    const params = new URLSearchParams();
    params.set('q', query);
    params.set('type', contentType);
    params.set('page', '1');
    router.push(`/search?${params.toString()}`);
  };

  const handleTypeChange = (type: typeof contentType) => {
    setContentType(type);
    const params = new URLSearchParams();
    params.set('q', currentQuery);
    params.set('type', type);
    params.set('page', '1');
    router.push(`/search?${params.toString()}`);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams();
    params.set('q', currentQuery);
    params.set('type', contentType);
    params.set('page', page.toString());
    router.push(`/search?${params.toString()}`);
  };

  const formatRating = (rating?: number | string) => {
    if (!rating) return 'N/A';
    if (typeof rating === 'number') return rating.toFixed(1);
    if (typeof rating === 'string' && !isNaN(Number(rating))) return Number(rating).toFixed(1);
    return rating; // Return as-is for MPAA ratings like "R", "PG-13"
  };

  const formatDate = (date?: string) => {
    if (!date) return '';
    return new Date(date).getFullYear().toString();
  };

  const getContentLink = (result: SearchResult) => {
    if (result.type === 'movie') {
      return `/watch/movie/${result.imdbId}`;
    } else if (result.type === 'series') {
      return `/watch/series/${result.imdbId}`;
    } else if (result.type === 'episode') {
      return `/watch/series/${result.seriesImdbId}?season=${result.season}&episode=${result.episode}`;
    }
    return '#';
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="glass border-b border-white/5">
        <div className="max-w-[1920px] mx-auto px-8 lg:px-16 py-8">
          <div className="flex items-center space-x-6 mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-red-600 to-red-700 rounded-3xl flex items-center justify-center shadow-2xl">
              <Search className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">Search Results</h1>
              {currentQuery && (
                <p className="text-gray-400 text-lg">
                  {pagination.total} results for "{currentQuery}"
                </p>
              )}
            </div>
          </div>

          {/* Search Bar */}
          <div className="mb-8">
            <SearchBar
              placeholder="Search movies, series, episodes..."
              onSearch={handleSearch}
              className="max-w-3xl"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center space-x-6">
              {/* Content Type Filter */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Filter className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-400 font-medium">Filter:</span>
                </div>
                <div className="flex glass-elevated rounded-2xl p-1.5">
                  {[
                    { key: 'all', label: 'All' },
                    { key: 'movies', label: 'Movies' },
                    { key: 'series', label: 'Series' },
                    { key: 'episodes', label: 'Episodes' }
                  ].map(({ key, label }) => (
                    <button
                      key={key}
                      onClick={() => handleTypeChange(key as typeof contentType)}
                      className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                        contentType === key
                          ? 'bg-gradient-to-r from-red-600 to-red-700 text-white shadow-xl'
                          : 'text-gray-400 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      {label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex glass-elevated rounded-2xl p-1.5">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-3 rounded-xl transition-all duration-300 ${
                  viewMode === 'grid'
                    ? 'bg-white/20 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-3 rounded-xl transition-all duration-300 ${
                  viewMode === 'list'
                    ? 'bg-white/20 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="max-w-[1920px] mx-auto px-8 lg:px-16 py-12">
        {isLoading ? (
          <div className="flex items-center justify-center py-32">
            <div className="text-center">
              <div className="animate-spin w-16 h-16 border-4 border-red-500 border-t-transparent rounded-full mx-auto mb-6"></div>
              <p className="text-gray-400 text-lg">Searching...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-32">
            <div className="w-24 h-24 glass-elevated rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-red-400" />
            </div>
            <h3 className="text-white font-bold text-2xl mb-4">Search Error</h3>
            <p className="text-gray-400 text-lg">{error}</p>
          </div>
        ) : results.length === 0 && currentQuery ? (
          <div className="text-center py-32">
            <div className="w-24 h-24 glass-elevated rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-white font-bold text-2xl mb-4">No Results Found</h3>
            <p className="text-gray-400 text-lg mb-6">
              No results found for "{currentQuery}". Try different keywords or check spelling.
            </p>
            <div className="glass-elevated rounded-2xl p-6 max-w-md mx-auto">
              <p className="text-gray-300 font-medium mb-3">Search tips:</p>
              <ul className="text-gray-400 space-y-2 text-left">
                <li>• Try broader keywords</li>
                <li>• Check for typos</li>
                <li>• Use different content type filters</li>
              </ul>
            </div>
          </div>
        ) : (
          <>
            {/* Results Grid/List */}
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-8">
                {results.map((result) => (
                  <Link
                    key={`${result.type}-${result._id}`}
                    href={getContentLink(result)}
                    className="group"
                  >
                    <div className="glass-elevated rounded-3xl overflow-hidden border border-white/5 hover:border-white/20 transition-all duration-500 hover:scale-105 hover:shadow-2xl card-hover">
                      {/* Poster */}
                      <div className="aspect-[2/3] bg-gray-900 relative overflow-hidden">
                        {(result.posterUrl || result.seriesPoster) ? (
                          <Image
                            src={result.posterUrl || result.seriesPoster || ''}
                            alt={result.title}
                            fill
                            className="object-cover group-hover:scale-110 transition-transform duration-300"
                            unoptimized
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Play className="w-12 h-12 text-gray-600" />
                          </div>
                        )}
                        
                        {/* Type Badge */}
                        <div className="absolute top-3 left-3">
                          <span className={`px-3 py-1.5 text-xs font-bold rounded-xl backdrop-blur-xl ${
                            result.type === 'movie' ? 'bg-blue-600/90 text-white' :
                            result.type === 'series' ? 'bg-green-600/90 text-white' :
                            'bg-purple-600/90 text-white'
                          }`}>
                            {result.type === 'episode' ? 'EP' : result.type.toUpperCase()}
                          </span>
                        </div>

                        {/* Rating */}
                        {result.rating && (
                          <div className="absolute top-3 right-3 glass rounded-xl px-3 py-1.5">
                            <div className="flex items-center space-x-1.5">
                              <Star className="w-3.5 h-3.5 text-yellow-400 fill-current" />
                              <span className="text-white text-xs font-bold">
                                {formatRating(result.rating)}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Info */}
                      <div className="p-4">
                        <h3 className="text-white font-bold text-sm line-clamp-2 mb-2 group-hover:text-red-400 transition-colors">
                          {result.title}
                        </h3>

                        {/* Episode Info */}
                        {result.type === 'episode' && (
                          <p className="text-gray-400 text-xs mb-2">
                            {result.seriesTitle} • S{result.season}E{result.episode}
                          </p>
                        )}

                        {/* Series Info */}
                        {result.type === 'series' && result.totalSeasons && (
                          <p className="text-gray-400 text-xs mb-2">
                            {result.totalSeasons} Season{result.totalSeasons > 1 ? 's' : ''}
                          </p>
                        )}

                        {/* Date */}
                        {(result.releaseDate || result.airDate) && (
                          <div className="flex items-center space-x-1.5 text-gray-500 text-xs">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(result.releaseDate || result.airDate)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              // List View
              <div className="space-y-6">
                {results.map((result) => (
                  <Link
                    key={`${result.type}-${result._id}`}
                    href={getContentLink(result)}
                    className="block group"
                  >
                    <div className="glass-elevated rounded-3xl p-6 border border-white/5 hover:border-white/20 transition-all duration-500 hover:bg-white/5">
                      <div className="flex items-center space-x-6">
                        {/* Poster */}
                        <div className="flex-shrink-0 w-20 h-28 bg-gray-900 rounded-2xl overflow-hidden">
                          {(result.posterUrl || result.seriesPoster) ? (
                            <Image
                              src={result.posterUrl || result.seriesPoster || ''}
                              alt={result.title}
                              width={64}
                              height={80}
                              className="w-full h-full object-cover"
                              unoptimized
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Play className="w-6 h-6 text-gray-600" />
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-white font-bold text-lg truncate group-hover:text-red-400 transition-colors">
                              {result.title}
                            </h3>
                            <span className={`px-3 py-1 text-xs font-bold rounded-xl ${
                              result.type === 'movie' ? 'bg-blue-600/20 text-blue-400 border border-blue-400/30' :
                              result.type === 'series' ? 'bg-green-600/20 text-green-400 border border-green-400/30' :
                              'bg-purple-600/20 text-purple-400 border border-purple-400/30'
                            }`}>
                              {result.type === 'episode' ? 'EP' : result.type.toUpperCase()}
                            </span>
                          </div>

                          {/* Episode specific info */}
                          {result.type === 'episode' && (
                            <p className="text-gray-400 text-sm mb-3">
                              {result.seriesTitle} • Season {result.season}, Episode {result.episode}
                            </p>
                          )}

                          {/* Series specific info */}
                          {result.type === 'series' && result.totalSeasons && (
                            <p className="text-gray-400 text-sm mb-3">
                              {result.totalSeasons} Season{result.totalSeasons > 1 ? 's' : ''}
                            </p>
                          )}

                          {/* Rating and Date */}
                          <div className="flex items-center space-x-6 text-sm text-gray-400">
                            {result.rating && (
                              <div className="flex items-center space-x-2">
                                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                <span className="font-medium">{formatRating(result.rating)}</span>
                              </div>
                            )}
                            {(result.releaseDate || result.airDate) && (
                              <div className="flex items-center space-x-2">
                                <Calendar className="w-4 h-4" />
                                <span>{formatDate(result.releaseDate || result.airDate)}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-center space-x-3 mt-16">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="px-6 py-3 glass-elevated text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/10 transition-all duration-300 font-medium"
                >
                  Previous
                </button>

                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const page = i + Math.max(1, pagination.page - 2);
                  if (page > pagination.pages) return null;

                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-4 py-3 rounded-2xl transition-all duration-300 font-medium ${
                        page === pagination.page
                          ? 'bg-gradient-to-r from-red-600 to-red-700 text-white shadow-xl'
                          : 'glass-elevated text-white hover:bg-white/10'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}

                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                  className="px-6 py-3 glass-elevated text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/10 transition-all duration-300 font-medium"
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

const SearchPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-16 h-16 border-4 border-red-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-400 text-lg">Loading search...</p>
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
};

export default SearchPage;
