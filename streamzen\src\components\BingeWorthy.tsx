'use client';

import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Star, Play, Tv, Clock } from 'lucide-react';
import { motion, useInView } from 'framer-motion';
import { cn, getImageUrl, formatRating } from '@/lib/utils';
import ContentCard from './ContentCard';

interface SeriesItem {
  _id: string;
  imdbId: string;
  title: string;
  startYear?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  totalSeasons?: number;
  status?: string;
}

interface BingeWorthyProps {
  series: SeriesItem[];
  className?: string;
  style?: React.CSSProperties;
}

const BingeWorthy: React.FC<BingeWorthyProps> = ({
  series,
  className,
  style
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const checkScrollButtons = () => {
    if (!scrollRef.current) return;
    const container = scrollRef.current;
    setCanScrollLeft(container.scrollLeft > 0);
    setCanScrollRight(
      container.scrollLeft < container.scrollWidth - container.clientWidth - 1
    );
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, [series]);

  const scroll = (direction: 'left' | 'right') => {
    if (!scrollRef.current) return;
    const container = scrollRef.current;
    const scrollAmount = container.clientWidth * 0.7;
    const targetScroll = direction === 'left' 
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount;

    container.scrollTo({
      left: targetScroll,
      behavior: 'smooth'
    });
  };

  if (series.length === 0) return null;

  return (
    <motion.section
      ref={sectionRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8 }}
      className={cn("relative", className)}
      style={style}
    >
      <div className="max-w-[2560px] mx-auto px-6 lg:px-12">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
              <Tv className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-3xl lg:text-4xl font-black text-white">
                Binge-Worthy Series
              </h2>
              <p className="text-gray-400 text-sm">Perfect for your next marathon</p>
            </div>
          </div>

          <Link
            href="/series"
            className="group px-4 py-2 bg-gradient-to-r from-indigo-500/20 to-purple-600/20 border border-indigo-500/30 text-indigo-400 rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105"
          >
            <div className="flex items-center space-x-2">
              <span className="font-semibold">View All Series</span>
              <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </Link>
        </motion.div>

        {/* Content Carousel */}
        <div className="relative group">
          {/* Navigation Buttons */}
          {canScrollLeft && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          )}

          {canScrollRight && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          )}

          {/* Scrollable Content */}
          <div
            ref={scrollRef}
            className="flex space-x-4 overflow-x-auto scrollbar-hide pb-2"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {series.slice(0, 12).map((item, index) => (
              <motion.div
                key={item._id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                className="flex-shrink-0 w-52 lg:w-56"
              >
                <div className="relative">
                  {/* Binge-worthy badge overlay */}
                  <div className="absolute top-3 left-3 z-20 flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full text-xs font-bold text-white shadow-lg">
                    <Tv className="w-3 h-3" />
                    <span>Binge</span>
                  </div>

                  {/* Bottom accent */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-purple-600 z-20 rounded-b-2xl" />

                  <ContentCard
                    id={item._id}
                    imdbId={item.imdbId}
                    title={item.title}
                    year={item.startYear}
                    posterUrl={item.posterUrl}
                    imdbRating={item.imdbRating}
                    description={item.description}
                    type="series"
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default BingeWorthy;
