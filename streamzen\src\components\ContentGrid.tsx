'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import ContentCard from './ContentCard';
import Button from './ui/Button';
import { cn } from '@/lib/utils';

interface ContentItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  seriesPosterUrl?: string; // For episodes to use their series poster
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series' | 'episode';
  season?: number;
  episode?: number;
  seriesTitle?: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

interface ContentGridProps {
  items: ContentItem[];
  pagination: Pagination;
  basePath: string;
  currentFilters: Record<string, string | undefined>;
}

const ContentGrid: React.FC<ContentGridProps> = ({
  items,
  pagination,
  basePath,
  currentFilters
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const goToPage = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    router.push(`${basePath}?${params.toString()}`);
  };

  const generatePageUrl = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    return `${basePath}?${params.toString()}`;
  };

  const generatePageNumbers = () => {
    const { page, pages } = pagination;
    const pageNumbers: (number | string)[] = [];
    
    if (pages <= 7) {
      // Show all pages if 7 or fewer
      for (let i = 1; i <= pages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Show first page
      pageNumbers.push(1);
      
      if (page > 4) {
        pageNumbers.push('...');
      }
      
      // Show pages around current page
      const start = Math.max(2, page - 2);
      const end = Math.min(pages - 1, page + 2);
      
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i);
      }
      
      if (page < pages - 3) {
        pageNumbers.push('...');
      }
      
      // Show last page
      if (pages > 1) {
        pageNumbers.push(pages);
      }
    }
    
    return pageNumbers;
  };

  if (items.length === 0) {
    return (
      <div className="text-center py-16">
        <h3 className="text-xl font-semibold text-white mb-2">No content found</h3>
        <p className="text-gray-400">Try adjusting your filters or search terms</p>
      </div>
    );
  }

  return (
    <div>
      {/* Results Info */}
      <div className="mb-8">
        <p className="text-gray-400 text-sm">
          Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
          {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
          {pagination.total} results
        </p>
      </div>

      {/* Premium Content Grid - Mobile Optimized */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-3 sm:gap-4 md:gap-6 mb-8 sm:mb-12">
        {items.map((item) => (
          <ContentCard
            key={`${item.type}-${item.id}`}
            id={item.id}
            imdbId={item.imdbId}
            title={item.title}
            year={item.year}
            posterUrl={item.posterUrl}
            seriesPosterUrl={item.seriesPosterUrl}
            imdbRating={item.imdbRating}
            description={item.description}
            type={item.type}
            season={item.season}
            episode={item.episode}
            seriesTitle={item.seriesTitle}
          />
        ))}
      </div>

      {/* SEO-Friendly Pagination */}
      {pagination.pages > 1 && (
        <nav className="flex items-center justify-center space-x-3 py-8" aria-label="Pagination Navigation">
          {/* Previous Button */}
          {pagination.page > 1 ? (
            <Link
              href={generatePageUrl(pagination.page - 1)}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-white bg-gray-800 hover:bg-gray-700 border border-gray-700"
              aria-label={`Go to page ${pagination.page - 1}`}
            >
              <ChevronLeft size={16} />
              <span>Previous</span>
            </Link>
          ) : (
            <span className="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium text-gray-600 cursor-not-allowed">
              <ChevronLeft size={16} />
              <span>Previous</span>
            </span>
          )}

          {/* Page Numbers */}
          <div className="flex items-center space-x-2">
            {generatePageNumbers().map((pageNum, index) => (
              <React.Fragment key={index}>
                {pageNum === '...' ? (
                  <span className="px-3 py-2 text-gray-500" aria-hidden="true">...</span>
                ) : pageNum === pagination.page ? (
                  <span
                    className="px-4 py-2 rounded-lg text-sm font-medium bg-red-600 text-white shadow-lg"
                    aria-current="page"
                    aria-label={`Current page ${pageNum}`}
                  >
                    {pageNum}
                  </span>
                ) : (
                  <Link
                    href={generatePageUrl(pageNum as number)}
                    className="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-gray-300 bg-gray-800 hover:bg-gray-700 border border-gray-700"
                    aria-label={`Go to page ${pageNum}`}
                  >
                    {pageNum}
                  </Link>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Next Button */}
          {pagination.page < pagination.pages ? (
            <Link
              href={generatePageUrl(pagination.page + 1)}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-white bg-gray-800 hover:bg-gray-700 border border-gray-700"
              aria-label={`Go to page ${pagination.page + 1}`}
            >
              <span>Next</span>
              <ChevronRight size={16} />
            </Link>
          ) : (
            <span className="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium text-gray-600 cursor-not-allowed">
              <span>Next</span>
              <ChevronRight size={16} />
            </span>
          )}
        </nav>
      )}
    </div>
  );
};

export default ContentGrid;
