import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import UniversalSyncInitializer from "@/components/UniversalSyncInitializer";
// import { SEOGenerator } from "@/lib/seo";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "freeMoviesWatchNow - Watch Movies and TV Series Online Free",
  description: "Watch the latest movies and TV series online free in HD quality. Discover thousands of movies, series, and episodes from all genres. Stream now on freeMoviesWatchNow.",
  keywords: "watch movies online, free movies, HD movies, TV series online, free episodes, streaming platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased bg-black text-white`}>
        <UniversalSyncInitializer />
        <Navigation />
        <main className="pt-24 page-transition min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
