import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Movie from '@/models/Movie';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test connection
    await connectDB();
    console.log('✅ Database connected successfully');

    // Test collections
    const movieCount = await Movie.countDocuments();
    console.log(`📽️ Movies in database: ${movieCount}`);

    const seriesCount = await Series.countDocuments();
    console.log(`📺 Series in database: ${seriesCount}`);

    const episodeCount = await Episode.countDocuments();
    console.log(`🎬 Episodes in database: ${episodeCount}`);

    // Test a simple query
    const sampleMovie = await Movie.findOne().lean();
    console.log('🎭 Sample movie:', sampleMovie ? sampleMovie.title : 'No movies found');

    const sampleSeries = await Series.findOne().lean();
    console.log('📺 Sample series:', sampleSeries ? sampleSeries.title : 'No series found');

    const sampleEpisode = await Episode.findOne().lean();
    console.log('🎬 Sample episode:', sampleEpisode ? sampleEpisode.title : 'No episodes found');

    return NextResponse.json({
      success: true,
      connection: 'Connected',
      counts: {
        movies: movieCount,
        series: seriesCount,
        episodes: episodeCount
      },
      samples: {
        movie: sampleMovie ? { title: sampleMovie.title, imdbId: sampleMovie.imdbId } : null,
        series: sampleSeries ? { title: sampleSeries.title, imdbId: sampleSeries.imdbId } : null,
        episode: sampleEpisode ? { title: sampleEpisode.title, imdbId: sampleEpisode.imdbId } : null
      }
    });

  } catch (error) {
    console.error('❌ Database test error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
