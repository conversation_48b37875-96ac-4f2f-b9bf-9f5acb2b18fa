import { NextRequest, NextResponse } from 'next/server';
import VidSrcScheduler from '@/lib/vidsrcScheduler';

const scheduler = VidSrcScheduler.getInstance();

/**
 * GET - Check sync status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'status') {
      const status = await scheduler.getSyncStatus();
      return NextResponse.json({
        success: true,
        data: status
      });
    }

    if (action === 'force') {
      const result = await scheduler.forceSync();
      return NextResponse.json(result);
    }

    // Default: return status
    const status = await scheduler.getSyncStatus();
    return NextResponse.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('Error in sync endpoint:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process request',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Force manual sync
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { action } = body;

    if (action === 'force' || !action) {
      console.log('🔧 Manual VidSrc sync requested via API');
      const result = await scheduler.forceSync();
      return NextResponse.json(result);
    }

    if (action === 'status') {
      const status = await scheduler.getSyncStatus();
      return NextResponse.json({
        success: true,
        data: status
      });
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Invalid action',
        message: 'Supported actions: force, status' 
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in sync endpoint:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process request',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
