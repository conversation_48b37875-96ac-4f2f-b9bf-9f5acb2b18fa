const { MongoClient } = require('mongodb');

async function debugVidSrcEpisodes() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('streamzen');
    const seriesCollection = db.collection('series');
    
    console.log('🔍 Debugging VidSrc Latest Episodes Assignment...');
    
    // Find series with multiple latest episodes
    const seriesWithMultipleLatest = await seriesCollection.find({
      'episodes.isLatestRelease': true
    }).limit(10).toArray();
    
    console.log(`📊 Found ${seriesWithMultipleLatest.length} series with latest episodes`);
    
    for (const series of seriesWithMultipleLatest) {
      const latestEpisodes = series.episodes.filter(ep => ep.isLatestRelease);
      
      if (latestEpisodes.length > 1) {
        console.log(`\n📺 Series: ${series.title} (${series.imdbId})`);
        console.log(`   Total episodes: ${series.episodes.length}`);
        console.log(`   Latest episodes marked: ${latestEpisodes.length}`);
        
        // Sort latest episodes by season/episode
        const sortedLatest = latestEpisodes.sort((a, b) => {
          if (a.season !== b.season) return a.season - b.season;
          return a.episode - b.episode;
        });
        
        console.log('   Latest episodes:');
        sortedLatest.forEach(ep => {
          console.log(`     S${ep.season}E${ep.episode} - ${ep.episodeTitle || 'No title'} - Created: ${ep.createdAt}`);
        });
        
        // Find the actual highest episode
        const actualLatest = sortedLatest[sortedLatest.length - 1];
        console.log(`   🎯 Actual latest should be: S${actualLatest.season}E${actualLatest.episode}`);
        
        // Show what current aggregation would pick
        const sortedByCreation = latestEpisodes.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        const currentPick = sortedByCreation[0];
        console.log(`   ❌ Current aggregation picks: S${currentPick.season}E${currentPick.episode} (by creation date)`);
        
        const sortedBySeason = latestEpisodes.sort((a, b) => {
          if (a.season !== b.season) return b.season - a.season;
          return b.episode - a.episode;
        });
        const correctPick = sortedBySeason[0];
        console.log(`   ✅ Fixed aggregation should pick: S${correctPick.season}E${correctPick.episode} (by season/episode)`);
      }
    }
    
    // Test the current aggregation
    console.log('\n🔍 Testing Current Episodes Page Aggregation...');
    
    const currentAggregation = [
      { $match: { 'episodes.isLatestRelease': true } },
      { $unwind: '$episodes' },
      { $match: { 'episodes.isLatestRelease': true } },
      {
        $sort: {
          'episodes.season': -1,
          'episodes.episode': -1,
          'episodes.createdAt': -1
        }
      },
      {
        $group: {
          _id: '$imdbId',
          series: { $first: '$$ROOT' },
          latestEpisode: { $first: '$episodes' }
        }
      },
      {
        $project: {
          _id: '$latestEpisode._id',
          imdbId: '$_id',
          seriesTitle: '$series.title',
          season: '$latestEpisode.season',
          episode: '$latestEpisode.episode',
          episodeTitle: '$latestEpisode.episodeTitle',
          isLatestRelease: '$latestEpisode.isLatestRelease',
          createdAt: '$latestEpisode.createdAt'
        }
      },
      { $sort: { createdAt: -1 } },
      { $limit: 10 }
    ];
    
    const result = await seriesCollection.aggregate(currentAggregation).toArray();
    
    console.log(`📊 Current aggregation returns: ${result.length} episodes`);
    result.forEach(ep => {
      console.log(`   ${ep.seriesTitle}: S${ep.season}E${ep.episode} - ${ep.episodeTitle || 'No title'}`);
    });
    
    // Check if we have series with first episodes showing
    const firstEpisodes = result.filter(ep => ep.season === 1 && ep.episode === 1);
    console.log(`\n⚠️  Episodes showing S1E1: ${firstEpisodes.length}/${result.length}`);
    
    if (firstEpisodes.length > 0) {
      console.log('   Series showing first episodes:');
      firstEpisodes.forEach(ep => {
        console.log(`     ${ep.seriesTitle}: S${ep.season}E${ep.episode}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  } finally {
    await client.close();
  }
}

debugVidSrcEpisodes();
