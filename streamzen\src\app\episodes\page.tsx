import { Suspense } from 'react';
import { Metadata } from 'next';
import ContentGrid from '@/components/ContentGrid';
import ModernFilterSystem from '@/components/ModernFilterSystem';
import LoadingSpinner from '@/components/LoadingSpinner';
import ClientSearchBar from '@/components/ClientSearchBar';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';
import connectDB from '@/lib/mongodb';
import Episode from '@/models/Episode';

interface EpisodesPageProps {
  searchParams: Promise<{
    page?: string;
    genre?: string;
    language?: string;
    country?: string;
    quality?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  }>;
}

export async function generateMetadata({ searchParams }: EpisodesPageProps): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  const { genre, language, country, search, page } = resolvedSearchParams;

  let title = 'Latest Episodes - Watch TV Episodes Online Free';
  let description = 'Watch the latest TV episodes online free in HD quality. Discover new episodes from your favorite series and shows updated daily.';
  let keywords = ['latest episodes', 'watch episodes online', 'free episodes', 'HD episodes', 'TV episodes', 'new episodes'];

  // Dynamic title and description based on filters
  if (search) {
    title = `Search Results for "${search}" - Episodes`;
    description = `Search results for "${search}" episodes. Watch ${search} episodes online free in HD quality on StreamZen.`;
    keywords.push(search, `${search} episodes`, `watch ${search}`);
  } else if (genre) {
    title = `Latest ${genre} Episodes - Watch Online Free`;
    description = `Watch the latest ${genre} episodes online free in HD quality. Discover new ${genre} episodes from your favorite series.`;
    keywords.push(genre, `${genre} episodes`, `latest ${genre} episodes`);
  } else if (language) {
    title = `Latest ${language} Episodes - Watch Online Free`;
    description = `Watch the latest ${language} episodes online free in HD quality. Discover new ${language} episodes from your favorite series.`;
    keywords.push(language, `${language} episodes`, `latest ${language} episodes`);
  } else if (country) {
    title = `Latest ${country} Episodes - Watch Online Free`;
    description = `Watch the latest ${country} episodes online free in HD quality. Discover new ${country} episodes from your favorite series.`;
    keywords.push(country, `${country} episodes`, `latest ${country} episodes`);
  }

  if (page && parseInt(page) > 1) {
    title += ` - Page ${page}`;
    description += ` Browse page ${page} for more episodes.`;
  }

  const path = `/episodes${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`;

  return SEOGenerator.generatePageMetadata(title, description, path, keywords);
}

async function getEpisodes(searchParams: Awaited<EpisodesPageProps['searchParams']>) {
  try {
    await connectDB();

    console.log('🔍 Fetching VidSrc latest episodes from Episode collection...');

    // SIMPLE APPROACH: Episode collection contains ONLY VidSrc latest episodes
    const totalEpisodes = await Episode.countDocuments({});
    console.log(`📊 Database stats: ${totalEpisodes} latest episodes in Episode collection`);

    console.log('🔄 Using Episode collection directly (contains only VidSrc latest episodes)');

    // Build episode match stage for filtering
    const episodeMatchStage: any = {};

    if (searchParams.search) {
      episodeMatchStage.$or = [
        { seriesTitle: { $regex: searchParams.search, $options: 'i' } },
        { episodeTitle: { $regex: searchParams.search, $options: 'i' } },
        { description: { $regex: searchParams.search, $options: 'i' } }
      ];
    }

    if (searchParams.genre) {
      episodeMatchStage.genres = { $in: [searchParams.genre] };
    }

    // Filter by language and country
    if (searchParams.language) {
      episodeMatchStage.language = searchParams.language;
    }

    if (searchParams.country) {
      episodeMatchStage.country = searchParams.country;
    }

    console.log('🔍 Episodes page filters applied:', {
      search: searchParams.search,
      genre: searchParams.genre,
      language: searchParams.language,
      country: searchParams.country,
      episodeMatchStage
    });

    // SIMPLE QUERY: Get episodes directly from Episode collection
    let rawEpisodes;
    try {
      console.log('🔄 Executing simple query on Episode collection...');
      rawEpisodes = await Episode.find(episodeMatchStage)
        .sort({ createdAt: -1 }) // Sort by newest first
        .limit(500) // Limit for performance
        .lean(); // Use lean for better performance

      console.log(`✅ Query completed successfully, returned: ${rawEpisodes.length} episodes`);
    } catch (queryError) {
      console.error('❌ Episode query failed:', queryError);
      rawEpisodes = [];
    }

    // Ensure rawEpisodes is always an array
    if (!Array.isArray(rawEpisodes)) {
      console.warn('⚠️ rawEpisodes is not an array, using empty array');
      rawEpisodes = [];
    }

    // Debug query results
    console.log(`🔍 Episode query results: ${rawEpisodes.length} episodes found`);

    // Simple debug for zero results
    if (rawEpisodes.length === 0) {
      console.log('🚨 ZERO RESULTS - Check Episode collection');
    }

    // Convert MongoDB documents to plain objects (Episode collection already has proper structure)
    const episodes = Array.isArray(rawEpisodes) ? rawEpisodes.map(episode => ({
      _id: episode._id?.toString() || 'unknown',
      imdbId: episode.imdbId || 'unknown',
      seriesTitle: episode.seriesTitle || 'Unknown Series',
      season: episode.season || 1,
      episode: episode.episode || 1,
      episodeTitle: episode.episodeTitle || `Episode ${episode.episode || 1}`,
      description: episode.description || '',
      posterUrl: episode.posterUrl || '', // Episode collection has series poster
      seriesPosterUrl: episode.posterUrl || '', // Use same poster for series poster
      runtime: episode.runtime || '45 min',
      imdbRating: episode.imdbRating || 0,
      airDate: episode.airDate || null,
      embedUrl: episode.embedUrl || '',
      genres: episode.genres || [],
      language: episode.language || '',
      country: episode.country || '',
      quality: episode.quality || '',
      createdAt: episode.createdAt || new Date(),
      updatedAt: episode.updatedAt || new Date()
    })) : [];

    console.log(`✅ Episode collection query complete: ${episodes.length} latest episodes`);

    // Debug: Show sample results with creation dates
    if (episodes.length > 0) {
      console.log('📝 Sample episodes with poster info:', episodes.slice(0, 3).map(ep => ({
        seriesTitle: ep.seriesTitle,
        season: ep.season,
        episode: ep.episode,
        imdbId: ep.imdbId,
        hasPoster: !!ep.posterUrl,
        hasSeriesPoster: !!ep.seriesPosterUrl,
        createdAt: ep.createdAt,
        hasGenres: ep.genres?.length || 0
      })));

      console.log(`📅 Episodes created in last 24 hours: 0`);
      console.log(`🎬 Total unique series in episodes: ${rawEpisodes.length}`);

    } else {
      console.log('⚠️ No episodes found - checking raw episode data...');
      console.log('📝 Raw episodes sample: []');
    }

    // Safety check: ensure episodes is always an array
    const safeEpisodes = Array.isArray(episodes) ? episodes : [];

    console.log(`✅ Returning ${safeEpisodes.length} episodes to page`);

    return {
      data: safeEpisodes,
      pagination: {
        page: 1,
        limit: safeEpisodes.length,
        total: safeEpisodes.length,
        pages: 1
      }
    };

  } catch (error) {
    console.error('Error fetching episodes from database:', error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 0,
        total: 0,
        pages: 0
      }
    };
  }
}

function transformEpisodeToContentItem(episode: any) {
  // Safety checks for episode data
  if (!episode) {
    console.warn('⚠️ Undefined episode data in transform function');
    return {
      id: 'unknown',
      imdbId: 'unknown',
      title: 'Unknown Episode',
      season: 1,
      episode: 1,
      seriesTitle: 'Unknown Series',
      posterUrl: '',
      seriesPosterUrl: '',
      imdbRating: 0,
      description: '',
      type: 'episode' as const
    };
  }

  return {
    id: episode._id || episode.id || 'unknown',
    imdbId: episode.imdbId || 'unknown',
    title: episode.episodeTitle || `Episode ${episode.episode || 1}`,
    season: episode.season || 1,
    episode: episode.episode || 1,
    seriesTitle: episode.seriesTitle || 'Unknown Series',
    posterUrl: episode.posterUrl || '',
    seriesPosterUrl: episode.posterUrl || '', // Always use series poster for episodes
    imdbRating: episode.imdbRating || 0,
    description: episode.description || '',
    type: 'episode' as const
  };
}

export default async function EpisodesPage({ searchParams }: EpisodesPageProps) {
  const resolvedSearchParams = await searchParams;
  const result = await getEpisodes(resolvedSearchParams);

  // Add safety checks for the result
  const episodes = Array.isArray(result?.data) ? result.data : [];
  const pagination = result?.pagination || { page: 1, limit: 0, total: 0, pages: 0 };

  return (
    <div className="min-h-screen bg-black">
      {/* Enhanced Hero Header */}
      <div className="relative bg-black border-b border-gray-800/50 overflow-hidden">
        {/* Premium Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/3 w-96 h-96 bg-orange-900/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-1/2 right-1/3 w-80 h-80 bg-gray-800/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[600px] h-32 bg-gradient-to-t from-gray-900/30 to-transparent blur-xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24">
          <div className="mb-12">
            {/* Premium Title with Gradient Text */}
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-orange-200 to-gray-400 mb-6 tracking-tight leading-none">
                Latest Episodes
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-gray-600 rounded-full mb-8"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8">
              Discover the latest episodes synced daily from VidSrc. Lightning-fast loading with comprehensive series metadata and automatic updates.
            </p>


          </div>

          {/* Enhanced Stats Cards */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                <span className="text-orange-400 font-bold text-lg">{(pagination?.total || 0).toLocaleString()}</span>
                <span className="text-gray-400 text-lg">Episodes Available</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <span className="text-green-400 font-bold text-lg">Fresh Daily</span>
                <span className="text-gray-400 text-lg">New Episodes</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
                <span className="text-blue-400 font-bold text-lg">Auto-Sync</span>
                <span className="text-gray-400 text-lg">Real-time Updates</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="relative">
        {/* Subtle Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/3 right-1/3 w-72 h-72 bg-orange-800/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 left-1/3 w-64 h-64 bg-gray-700/5 rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl lg:text-5xl font-black text-white mb-2">
                  {resolvedSearchParams.search ? 'Search Results' : 'Latest Episodes from VidSrc'}
                </h1>
                {resolvedSearchParams.search ? (
                  <p className="text-gray-400 text-lg">
                    Results for "{resolvedSearchParams.search}" from VidSrc latest episodes
                  </p>
                ) : (
                  <p className="text-gray-400 text-lg">
                    Discover the latest episodes verified on VidSrc streaming platform
                  </p>
                )}
              </div>
              <div className="glass-elevated px-4 py-2 rounded-xl border border-gray-700/50">
                <span className="text-gray-300 text-base font-medium">
                  {(pagination?.total || 0).toLocaleString()} latest episodes from VidSrc
                </span>
              </div>
            </div>

            {/* Modern Filter System */}
            <div className="glass-elevated p-6 rounded-2xl border border-gray-700/50">
              <ModernFilterSystem
                currentFilters={resolvedSearchParams}
                basePath="/episodes"
                contentType="episodes"
              />
            </div>

            {/* Content Grid */}
            <Suspense fallback={<LoadingSpinner />}>
              <ContentGrid
                items={Array.isArray(episodes) ? episodes.map(transformEpisodeToContentItem) : []}
                pagination={pagination}
                basePath="/episodes"
                currentFilters={resolvedSearchParams}
              />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
