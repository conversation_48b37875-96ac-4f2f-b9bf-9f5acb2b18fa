import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

export async function GET() {
  return await runComprehensiveFix();
}

export async function POST() {
  return await runComprehensiveFix();
}

async function runComprehensiveFix() {
  try {
    await connectDB();
    
    console.log('🚀 Starting COMPREHENSIVE FIX - Language/Country/Cleanup');
    const startTime = Date.now();
    
    const results = {
      step1: { name: 'Database Analysis', status: 'pending' },
      step2: { name: 'Episode Backfill', status: 'pending' },
      step3: { name: 'VidSrc Sync with Cleanup', status: 'pending' },
      step4: { name: 'Verification', status: 'pending' }
    };
    
    // STEP 1: Database Analysis
    console.log('📊 STEP 1: Analyzing current database state...');
    try {
      const totalEpisodes = await Episode.countDocuments({});
      const totalSeries = await Series.countDocuments({});
      const episodesWithLanguage = await Episode.countDocuments({ language: { $exists: true, $ne: null, $ne: '' } });
      const episodesWithCountry = await Episode.countDocuments({ country: { $exists: true, $ne: null, $ne: '' } });
      const latestReleaseEpisodes = await Episode.countDocuments({ isLatestRelease: true });
      
      console.log(`📊 Current state:`);
      console.log(`   Total episodes: ${totalEpisodes}`);
      console.log(`   Total series: ${totalSeries}`);
      console.log(`   Episodes with language: ${episodesWithLanguage}`);
      console.log(`   Episodes with country: ${episodesWithCountry}`);
      console.log(`   Latest release episodes: ${latestReleaseEpisodes}`);
      
      results.step1 = {
        name: 'Database Analysis',
        status: 'complete',
        data: { totalEpisodes, totalSeries, episodesWithLanguage, episodesWithCountry, latestReleaseEpisodes }
      };
    } catch (error) {
      results.step1 = { name: 'Database Analysis', status: 'failed', error: error.message };
    }
    
    // STEP 2: Episode Backfill
    console.log('🔄 STEP 2: Running episode backfill...');
    try {
      const backfillResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/admin/backfill-episodes`, {
        method: 'POST'
      });
      
      if (backfillResponse.ok) {
        const backfillResult = await backfillResponse.json();
        console.log(`✅ Backfill completed: ${backfillResult.stats?.episodesUpdated || 0} episodes updated`);
        results.step2 = {
          name: 'Episode Backfill',
          status: 'complete',
          data: backfillResult.stats
        };
      } else {
        throw new Error(`Backfill failed: ${backfillResponse.status}`);
      }
    } catch (error) {
      console.error('❌ Backfill failed:', error.message);
      results.step2 = { name: 'Episode Backfill', status: 'failed', error: error.message };
    }
    
    // STEP 3: VidSrc Sync with Cleanup
    console.log('🔄 STEP 3: Running VidSrc sync with cleanup...');
    try {
      const syncResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/sync/vidsrc-episodes`, {
        method: 'POST'
      });
      
      if (syncResponse.ok) {
        const syncResult = await syncResponse.json();
        console.log(`✅ VidSrc sync completed`);
        results.step3 = {
          name: 'VidSrc Sync with Cleanup',
          status: 'complete',
          data: syncResult.stats
        };
      } else {
        throw new Error(`VidSrc sync failed: ${syncResponse.status}`);
      }
    } catch (error) {
      console.error('❌ VidSrc sync failed:', error.message);
      results.step3 = { name: 'VidSrc Sync with Cleanup', status: 'failed', error: error.message };
    }
    
    // STEP 4: Verification
    console.log('🔍 STEP 4: Verifying fixes...');
    try {
      const finalEpisodes = await Episode.countDocuments({});
      const finalEpisodesWithLanguage = await Episode.countDocuments({ language: { $exists: true, $ne: null, $ne: '' } });
      const finalEpisodesWithCountry = await Episode.countDocuments({ country: { $exists: true, $ne: null, $ne: '' } });
      const finalLatestReleaseEpisodes = await Episode.countDocuments({ isLatestRelease: true });
      
      // Test language filtering
      const englishEpisodes = await Episode.countDocuments({ language: 'English' });
      const koreanEpisodes = await Episode.countDocuments({ language: 'Korean' });
      const urduEpisodes = await Episode.countDocuments({ language: 'Urdu' });
      
      console.log(`📊 Final state:`);
      console.log(`   Total episodes: ${finalEpisodes}`);
      console.log(`   Episodes with language: ${finalEpisodesWithLanguage}`);
      console.log(`   Episodes with country: ${finalEpisodesWithCountry}`);
      console.log(`   Latest release episodes: ${finalLatestReleaseEpisodes}`);
      console.log(`   English episodes: ${englishEpisodes}`);
      console.log(`   Korean episodes: ${koreanEpisodes}`);
      console.log(`   Urdu episodes: ${urduEpisodes}`);
      
      results.step4 = {
        name: 'Verification',
        status: 'complete',
        data: {
          finalEpisodes,
          finalEpisodesWithLanguage,
          finalEpisodesWithCountry,
          finalLatestReleaseEpisodes,
          languageBreakdown: { englishEpisodes, koreanEpisodes, urduEpisodes }
        }
      };
    } catch (error) {
      results.step4 = { name: 'Verification', status: 'failed', error: error.message };
    }
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`🎉 COMPREHENSIVE FIX COMPLETE in ${duration}s`);
    
    // Determine overall success
    const allStepsSuccessful = Object.values(results).every(step => step.status === 'complete');
    
    return NextResponse.json({
      success: allStepsSuccessful,
      message: allStepsSuccessful 
        ? 'All fixes completed successfully!' 
        : 'Some steps failed - check individual step results',
      duration: `${duration}s`,
      results,
      summary: {
        episodeModelUpdated: true,
        backfillCompleted: results.step2.status === 'complete',
        vidsrcSyncCompleted: results.step3.status === 'complete',
        verificationPassed: results.step4.status === 'complete'
      }
    });
    
  } catch (error) {
    console.error('❌ Comprehensive Fix Error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Comprehensive fix failed',
        message: error.message
      },
      { status: 500 }
    );
  }
}
