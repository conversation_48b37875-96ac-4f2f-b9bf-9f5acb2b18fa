import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * Temporary fix: Manually set isLatestRelease flags on some episodes for testing
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();

    console.log('🔧 Manually setting isLatestRelease flags for testing...');

    // Find series with episodes
    const seriesWithEpisodes = await Series.find({
      'episodes.0': { $exists: true }
    }).limit(50);

    let updatedCount = 0;
    let episodeCount = 0;

    for (const series of seriesWithEpisodes) {
      let seriesUpdated = false;
      
      // Set isLatestRelease: true on the latest episode of each series
      if (series.episodes && series.episodes.length > 0) {
        // Sort episodes by season and episode number to get the latest
        const sortedEpisodes = series.episodes.sort((a, b) => {
          if (a.season !== b.season) return b.season - a.season;
          return b.episode - a.episode;
        });

        // Set the latest episode as latest release
        const latestEpisode = sortedEpisodes[0];
        if (!latestEpisode.isLatestRelease) {
          latestEpisode.isLatestRelease = true;
          latestEpisode.updatedAt = new Date();
          seriesUpdated = true;
          episodeCount++;
        }

        // Also set a few more recent episodes as latest releases
        for (let i = 1; i < Math.min(3, sortedEpisodes.length); i++) {
          if (!sortedEpisodes[i].isLatestRelease) {
            sortedEpisodes[i].isLatestRelease = true;
            sortedEpisodes[i].updatedAt = new Date();
            seriesUpdated = true;
            episodeCount++;
          }
        }
      }

      if (seriesUpdated) {
        await series.save();
        updatedCount++;
      }
    }

    console.log(`✅ Updated ${updatedCount} series with ${episodeCount} latest episodes`);

    // Verify the fix worked
    const verifyCount = await Series.countDocuments({
      'episodes.isLatestRelease': true
    });

    return NextResponse.json({
      success: true,
      message: 'Manually set isLatestRelease flags for testing',
      stats: {
        seriesUpdated: updatedCount,
        episodesMarked: episodeCount,
        verificationCount: verifyCount
      }
    });

  } catch (error) {
    console.error('❌ Fix latest flags error:', error);
    return NextResponse.json(
      { error: 'Failed to fix flags', message: error.message },
      { status: 500 }
    );
  }
}
