import { NextRequest, NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    const { searchParams } = new URL(request.url);

    const filters = {
      genre: searchParams.get('genre') || undefined,
      year: searchParams.get('year') ? parseInt(searchParams.get('year')!) : undefined,
      language: searchParams.get('language') || undefined,
      country: searchParams.get('country') || undefined,
      rating: searchParams.get('rating') || undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1,
      limit: Math.min(searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 24, 48), // Max 48 for performance
      search: searchParams.get('search') || undefined,
    };

    console.log(`📺 Series API - Page: ${filters.page}, Limit: ${filters.limit}, Sort: ${filters.sortBy} ${filters.sortOrder}`);

    const result = await contentService.getSeries(filters);

    const duration = Date.now() - startTime;
    console.log(`✅ Series API completed in ${duration}ms - ${result.data.length} items, total: ${result.pagination.total}`);

    // Add cache headers for better performance
    const response = NextResponse.json(result);
    response.headers.set('Cache-Control', 'public, max-age=300'); // 5 minute cache

    return response;

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ Series API error after ${duration}ms:`, error);

    return NextResponse.json(
      {
        error: 'Failed to fetch series',
        message: 'Database query failed. Please try again or use filters to narrow results.',
        duration: `${duration}ms`
      },
      { status: 500 }
    );
  }
}
