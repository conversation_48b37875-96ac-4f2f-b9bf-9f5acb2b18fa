import { Suspense } from 'react';
import { Metadata } from 'next';
import ContentGrid from '@/components/ContentGrid';
import OptimizedFilterSystem from '@/components/OptimizedFilterSystem';
import LoadingSpinner from '@/components/LoadingSpinner';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface MoviesPageProps {
  searchParams: Promise<{
    page?: string;
    genre?: string;
    year?: string;
    language?: string;
    country?: string;
    rating?: string;
    quality?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  }>;
}

// Transform movie to content item for grid display
function transformMovieToContentItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    genres: movie.genres,
    description: movie.description,
    type: 'movie' as const,
    href: `/watch/movie/${movie.imdbId}`,
    runtime: movie.runtime,
    language: movie.language,
    country: movie.country,
    quality: movie.quality
  };
}

async function getOptimizedMoviesData(searchParams: Awaited<MoviesPageProps['searchParams']>) {
  try {
    console.log('🚀 Fetching optimized movies page data...');
    const startTime = Date.now();

    // Build query parameters
    const params = new URLSearchParams();
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    // Single API call to get both content and filters
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/movies/optimized?${params.toString()}`, {
      next: { revalidate: 180 } // Cache for 3 minutes
    });

    if (!response.ok) {
      throw new Error(`Movies API failed: ${response.status}`);
    }

    const data = await response.json();
    const endTime = Date.now();
    
    console.log(`✅ Optimized movies page data loaded in ${endTime - startTime}ms`);
    return data;

  } catch (error) {
    console.error('❌ Error fetching optimized movies data:', error);
    
    // Return empty data structure on error
    return {
      content: {
        data: [],
        pagination: { page: 1, limit: 24, total: 0, pages: 0 }
      },
      filters: {
        genres: [],
        languages: [],
        countries: [],
        years: [],
        ratings: [],
        qualities: []
      },
      meta: {
        currentFilters: {},
        hasActiveFilters: false
      }
    };
  }
}

export async function generateMetadata({ searchParams }: MoviesPageProps): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  
  return SEOGenerator.generateMoviesPageMetadata({
    search: resolvedSearchParams.search,
    genre: resolvedSearchParams.genre,
    year: resolvedSearchParams.year ? parseInt(resolvedSearchParams.year) : undefined,
    language: resolvedSearchParams.language,
    country: resolvedSearchParams.country,
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page) : 1
  });
}

export default async function OptimizedMoviesPage({ searchParams }: MoviesPageProps) {
  const resolvedSearchParams = await searchParams;
  const data = await getOptimizedMoviesData(resolvedSearchParams);
  
  const { content, filters, meta } = data;
  const { data: movies, pagination } = content;

  // Generate structured data for SEO
  const collectionSchema = SchemaGenerator.generateCollectionPageSchema(
    resolvedSearchParams.search ? `Search Results for "${resolvedSearchParams.search}"` : 'Movies',
    'Watch the latest movies online free in HD quality. Discover thousands of movies from all genres.',
    `/movies${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`,
    movies.slice(0, 10).map(movie => ({
      name: `${movie.title} (${movie.year})`,
      url: `/watch/movie/${movie.imdbId}`,
      image: movie.posterUrl
    }))
  );

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            collectionSchema
          ])
        }}
      />

      {/* Hero Section */}
      <div className="relative bg-gradient-to-b from-gray-900 via-black to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-transparent to-black/80" />
        
        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24">
          <div className="mb-12">
            {/* Premium Title with Gradient Text */}
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-red-200 to-gray-400 mb-6 tracking-tight leading-none">
                Movies
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-gray-600 rounded-full mb-8"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8">
              Discover premium movies and watch your favorites in HD. From blockbuster hits to indie gems, find your next cinematic adventure.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative bg-black">
        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl lg:text-5xl font-black text-white mb-2">
                  {resolvedSearchParams.search ? 'Search Results' : 'Movies'}
                </h1>
                {resolvedSearchParams.search && (
                  <p className="text-gray-400 text-lg">
                    Results for "{resolvedSearchParams.search}"
                  </p>
                )}
              </div>
              <div className="glass-elevated px-4 py-2 rounded-xl border border-gray-700/50">
                <span className="text-gray-300 text-base font-medium">
                  {pagination.total.toLocaleString()} movies • Page {pagination.page} of {pagination.pages}
                </span>
              </div>
            </div>

            {/* Optimized Filter System */}
            <div className="glass-elevated p-6 rounded-2xl border border-gray-700/50">
              <OptimizedFilterSystem
                currentFilters={resolvedSearchParams}
                filterOptions={filters}
                basePath="/movies"
                contentType="movies"
              />
            </div>

            {/* Content Grid */}
            <Suspense fallback={<LoadingSpinner />}>
              <ContentGrid
                items={movies.map(transformMovieToContentItem)}
                pagination={pagination}
                basePath="/movies"
                currentFilters={resolvedSearchParams}
              />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
