import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlsXML = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlsXML}
</urlset>`;
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = 50000; // Max URLs per sitemap
    const skip = (page - 1) * limit;

    // Get series with pagination, prioritizing popular and recent content
    const series = await Series.find({})
      .select('imdbId title startYear endYear imdbRating popularity status totalSeasons updatedAt createdAt')
      .sort({ 
        popularity: -1,    // Popular content first
        imdbRating: -1,    // High-rated content
        startYear: -1,     // Recent content
        totalSeasons: -1,  // More seasons = more content
        createdAt: -1      // Newly added content
      })
      .skip(skip)
      .limit(limit)
      .lean();

    const urls: SitemapUrl[] = series.map(show => {
      // Calculate priority based on popularity, rating, and status
      let priority = 0.6; // Base priority
      
      if (show.popularity && show.popularity > 80) priority = 0.9;
      else if (show.popularity && show.popularity > 60) priority = 0.8;
      else if (show.imdbRating && show.imdbRating > 8.0) priority = 0.8;
      else if (show.imdbRating && show.imdbRating > 7.0) priority = 0.7;
      else if (show.status === 'Running') priority = 0.8; // Ongoing series get higher priority
      else if (show.startYear && show.startYear >= new Date().getFullYear() - 2) priority = 0.7;

      // Determine change frequency based on status and recency
      let changefreq: SitemapUrl['changefreq'] = 'monthly';
      if (show.status === 'Running') changefreq = 'weekly'; // Ongoing series change more frequently
      else if (show.startYear && show.startYear >= new Date().getFullYear()) changefreq = 'weekly';
      else if (show.startYear && show.startYear >= new Date().getFullYear() - 1) changefreq = 'monthly';

      return {
        loc: `${BASE_URL}/watch/series/${show.imdbId}`,
        lastmod: show.updatedAt?.toISOString() || show.createdAt?.toISOString() || new Date().toISOString(),
        changefreq,
        priority: Math.round(priority * 10) / 10, // Round to 1 decimal
      };
    });

    const sitemapXML = generateSitemapXML(urls);

    return new NextResponse(sitemapXML, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating series sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
