import connectDB from '../lib/mongodb';
import Movie from '../models/Movie';
import Series from '../models/Series';
import Episode from '../models/Episode';
import VidSrcAPI from '../lib/vidsrc';
import IMDbScraper from '../lib/scraper';

async function initializeDatabase() {
  console.log('🚀 Starting database initialization...');
  
  try {
    await connectDB();
    console.log('✅ Connected to MongoDB');

    const vidsrc = VidSrcAPI.getInstance();
    const scraper = IMDbScraper.getInstance();

    // Clear existing data
    console.log('🗑️ Clearing existing data...');
    await Movie.deleteMany({});
    await Series.deleteMany({});
    await Episode.deleteMany({});

    // Fetch latest content from VidSrc
    console.log('📡 Fetching latest content from VidSrc...');
    const { movies, series, episodes } = await vidsrc.syncLatestContent(3); // Get 3 pages

    console.log(`Found ${movies.length} movies, ${series.length} series, ${episodes.length} episodes`);

    // Process movies
    console.log('🎬 Processing movies...');
    let processedMovies = 0;
    for (const movie of movies.slice(0, 20)) { // Limit to 20 for initial setup
      try {
        console.log(`Processing movie: ${movie.title} (${movie.imdb_id})`);
        
        const movieData = await scraper.scrapeMovie(movie.imdb_id);
        
        await Movie.create({
          imdbId: movie.imdb_id,
          tmdbId: movie.tmdb_id,
          title: movieData.title,
          year: movieData.year,
          rating: movieData.rating,
          runtime: movieData.runtime,
          imdbRating: movieData.imdbRating,
          imdbVotes: movieData.imdbVotes,
          popularity: movieData.popularity,
          popularityDelta: movieData.popularityDelta,
          posterUrl: movieData.posterUrl,
          trailerUrl: movieData.trailerUrl,
          trailerRuntime: movieData.trailerRuntime,
          trailerLikes: movieData.trailerLikes,
          description: movieData.description,
          genres: movieData.genres,
          director: movieData.director,
          cast: movieData.cast,
          language: movieData.language,
          country: movieData.country,
          embedUrl: movie.embed_url,
          embedUrlTmdb: movie.embed_url_tmdb,
          quality: movie.quality
        });
        
        processedMovies++;
        console.log(`✅ Processed movie: ${movieData.title}`);
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 3000));
      } catch (error) {
        console.error(`❌ Error processing movie ${movie.imdb_id}:`, error.message);
      }
    }

    // Process series
    console.log('📺 Processing series...');
    let processedSeries = 0;
    const uniqueSeries = Array.from(
      new Map(series.map(s => [s.imdb_id, s])).values()
    ).slice(0, 10); // Limit to 10 for initial setup

    for (const show of uniqueSeries) {
      try {
        console.log(`Processing series: ${show.show_title} (${show.imdb_id})`);
        
        const seriesData = await scraper.scrapeSeries(show.imdb_id);
        
        await Series.create({
          imdbId: show.imdb_id,
          tmdbId: show.tmdb_id,
          title: seriesData.title,
          startYear: seriesData.startYear,
          endYear: seriesData.endYear,
          rating: seriesData.rating,
          imdbRating: seriesData.imdbRating,
          imdbVotes: seriesData.imdbVotes,
          popularity: seriesData.popularity,
          popularityDelta: seriesData.popularityDelta,
          posterUrl: seriesData.posterUrl,
          trailerUrl: seriesData.trailerUrl,
          description: seriesData.description,
          genres: seriesData.genres,
          creator: seriesData.creator,
          cast: seriesData.cast,
          language: seriesData.language,
          country: seriesData.country,
          totalSeasons: seriesData.totalSeasons,
          status: seriesData.status,
          embedUrl: vidsrc.generateSeriesEmbedUrl(show.imdb_id),
          embedUrlTmdb: show.tmdb_id ? vidsrc.generateSeriesEmbedUrl(show.imdb_id, { tmdbId: show.tmdb_id }) : undefined
        });
        
        processedSeries++;
        console.log(`✅ Processed series: ${seriesData.title}`);
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 3000));
      } catch (error) {
        console.error(`❌ Error processing series ${show.imdb_id}:`, error.message);
      }
    }

    // Process episodes
    console.log('📺 Processing episodes...');
    let processedEpisodes = 0;
    for (const episode of episodes.slice(0, 50)) { // Limit to 50 for initial setup
      try {
        await Episode.create({
          imdbId: episode.imdb_id,
          tmdbId: episode.tmdb_id,
          seriesTitle: episode.show_title,
          season: parseInt(episode.season),
          episode: parseInt(episode.episode),
          embedUrl: episode.embed_url,
          embedUrlTmdb: episode.embed_url_tmdb,
          quality: episode.quality
        });
        
        processedEpisodes++;
        
        if (processedEpisodes % 10 === 0) {
          console.log(`✅ Processed ${processedEpisodes} episodes...`);
        }
      } catch (error) {
        console.error(`❌ Error processing episode ${episode.imdb_id} S${episode.season}E${episode.episode}:`, error.message);
      }
    }

    console.log('🎉 Database initialization completed!');
    console.log(`📊 Summary:`);
    console.log(`   Movies: ${processedMovies}`);
    console.log(`   Series: ${processedSeries}`);
    console.log(`   Episodes: ${processedEpisodes}`);

  } catch (error) {
    console.error('💥 Database initialization failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('✅ Initialization complete');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Initialization failed:', error);
      process.exit(1);
    });
}

export default initializeDatabase;
