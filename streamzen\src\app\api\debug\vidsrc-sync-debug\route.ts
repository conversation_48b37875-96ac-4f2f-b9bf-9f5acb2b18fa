import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import VidSrcAPI from '@/lib/vidsrc';

/**
 * Debug the VidSrc sync process step by step
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();
    
    const vidsrc = VidSrcAPI.getInstance();
    
    console.log('🔍 DEBUG: Starting VidSrc sync debug...');
    
    // Get first page of VidSrc episodes (small sample)
    const vidsrcEpisodes = await vidsrc.getLatestEpisodes(1);
    console.log(`📊 VidSrc returned ${vidsrcEpisodes.length} episodes`);
    
    if (vidsrcEpisodes.length === 0) {
      return NextResponse.json({ error: 'No VidSrc episodes found' });
    }

    // Take first episode for detailed debugging
    const testEpisode = vidsrcEpisodes[0];
    console.log('🎯 Testing with episode:', {
      imdb_id: testEpisode.imdb_id,
      show_title: testEpisode.show_title,
      season: testEpisode.season,
      episode: testEpisode.episode
    });

    // Check if series exists
    let series = await Series.findOne({ imdbId: testEpisode.imdb_id });
    
    if (!series) {
      return NextResponse.json({
        error: 'Test series not found in database',
        imdbId: testEpisode.imdb_id
      });
    }

    console.log(`📺 Found series: ${series.title}`);
    console.log(`📊 Series has ${series.episodes?.length || 0} episodes`);

    // Check if the specific episode exists
    const existingEpisodeIndex = series.episodes.findIndex(ep =>
      ep.season === parseInt(testEpisode.season) && ep.episode === parseInt(testEpisode.episode)
    );

    console.log(`🔍 Episode S${testEpisode.season}E${testEpisode.episode} exists at index: ${existingEpisodeIndex}`);

    if (existingEpisodeIndex >= 0) {
      const existingEpisode = series.episodes[existingEpisodeIndex];
      console.log('📝 Existing episode before update:', {
        season: existingEpisode.season,
        episode: existingEpisode.episode,
        title: existingEpisode.episodeTitle,
        isLatestRelease: existingEpisode.isLatestRelease,
        hasIsLatestReleaseField: existingEpisode.hasOwnProperty('isLatestRelease')
      });

      // Update the episode
      series.episodes[existingEpisodeIndex].isLatestRelease = true;
      series.episodes[existingEpisodeIndex].updatedAt = new Date();
      
      console.log('📝 Episode after update:', {
        isLatestRelease: series.episodes[existingEpisodeIndex].isLatestRelease,
        hasIsLatestReleaseField: series.episodes[existingEpisodeIndex].hasOwnProperty('isLatestRelease')
      });

      // Save the series
      await series.save();
      console.log('💾 Series saved successfully');

      // Verify the save worked
      const verifiedSeries = await Series.findOne({ imdbId: testEpisode.imdb_id });
      const verifiedEpisode = verifiedSeries.episodes[existingEpisodeIndex];
      
      console.log('✅ Verification after save:', {
        isLatestRelease: verifiedEpisode.isLatestRelease,
        hasIsLatestReleaseField: verifiedEpisode.hasOwnProperty('isLatestRelease')
      });

      return NextResponse.json({
        success: true,
        message: 'Debug sync completed',
        details: {
          seriesTitle: series.title,
          episodeIndex: existingEpisodeIndex,
          beforeUpdate: {
            isLatestRelease: existingEpisode.isLatestRelease,
            hasField: existingEpisode.hasOwnProperty('isLatestRelease')
          },
          afterSave: {
            isLatestRelease: verifiedEpisode.isLatestRelease,
            hasField: verifiedEpisode.hasOwnProperty('isLatestRelease')
          }
        }
      });
    } else {
      return NextResponse.json({
        error: 'Episode not found in series',
        details: {
          seriesTitle: series.title,
          lookingFor: `S${testEpisode.season}E${testEpisode.episode}`,
          availableEpisodes: series.episodes.slice(0, 5).map(ep => `S${ep.season}E${ep.episode}`)
        }
      });
    }

  } catch (error) {
    console.error('❌ VidSrc sync debug error:', error);
    return NextResponse.json(
      { error: 'Debug failed', message: error.message },
      { status: 500 }
    );
  }
}
