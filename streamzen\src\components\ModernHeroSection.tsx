'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, Info, Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface HeroItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series';
}

interface ModernHeroSectionProps {
  items: HeroItem[];
}

const ModernHeroSection: React.FC<ModernHeroSectionProps> = ({ items }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % items.length);
    }, 8000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, items.length]);

  const currentItem = items[currentIndex];
  const watchHref = currentItem?.type === 'movie' 
    ? `/watch/movie/${currentItem.imdbId}` 
    : `/watch/series/${currentItem.imdbId}`;

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
    setIsAutoPlaying(false);
  };

  if (!items.length || !currentItem) {
    return (
      <div className="h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-gray-600 border-t-red-500 rounded-full animate-spin mx-auto mb-4" />
          <p className="text-white text-xl">Loading featured content...</p>
        </div>
      </div>
    );
  }

  return (
    <section className="relative h-screen overflow-hidden bg-black">
      {/* Background Images with Smooth Transitions */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          className="absolute inset-0"
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 1, ease: "easeInOut" }}
        >
          <Image
            src={getImageUrl(currentItem.posterUrl)}
            alt={currentItem.title}
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          
          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/50 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40" />
        </motion.div>
      </AnimatePresence>

      {/* Content */}
      <div className="relative h-full flex items-center">
        <div className="max-w-[2560px] mx-auto px-6 lg:px-12 w-full">
          <div className="max-w-2xl">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -30 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="space-y-6"
              >
                {/* Type Badge */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <span className="inline-flex items-center px-4 py-2 bg-red-500/20 border border-red-500/30 rounded-full text-red-400 text-sm font-semibold backdrop-blur-sm">
                    {currentItem.type === 'movie' ? '🎬 Featured Movie' : '📺 Featured Series'}
                  </span>
                </motion.div>

                {/* Title */}
                <motion.h1
                  className="text-5xl lg:text-7xl font-black text-white leading-tight"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  {currentItem.title}
                </motion.h1>

                {/* Meta Info */}
                <motion.div
                  className="flex items-center space-x-6 text-gray-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  {currentItem.year && (
                    <span className="text-lg font-medium">{currentItem.year}</span>
                  )}
                  {currentItem.imdbRating && (
                    <div className="flex items-center space-x-2">
                      <Star size={20} className="text-yellow-400 fill-current" />
                      <span className="text-lg font-semibold">{formatRating(currentItem.imdbRating)}</span>
                    </div>
                  )}
                  <span className="px-3 py-1 bg-white/10 rounded-full text-sm font-medium backdrop-blur-sm">
                    HD
                  </span>
                </motion.div>

                {/* Description */}
                {currentItem.description && (
                  <motion.p
                    className="text-gray-300 text-lg leading-relaxed max-w-xl"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    {truncateText(currentItem.description, 200)}
                  </motion.p>
                )}

                {/* Action Buttons */}
                <motion.div
                  className="flex items-center space-x-4 pt-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <Link
                    href={watchHref}
                    className="flex items-center space-x-3 px-8 py-4 bg-red-500 hover:bg-red-600 text-white font-bold rounded-2xl transition-all duration-300 shadow-lg hover:shadow-red-500/25 hover:scale-105"
                  >
                    <Play size={24} className="fill-current" />
                    <span className="text-lg">Watch Now</span>
                  </Link>
                  
                  <Link
                    href={watchHref}
                    className="flex items-center space-x-3 px-8 py-4 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/20 hover:border-white/30"
                  >
                    <Info size={20} />
                    <span className="text-lg">More Info</span>
                  </Link>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      {items.length > 1 && (
        <>
          {/* Previous Button */}
          <button
            onClick={prevSlide}
            className="absolute left-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/50 hover:bg-black/70 border border-white/20 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 backdrop-blur-sm"
          >
            <ChevronLeft size={24} />
          </button>

          {/* Next Button */}
          <button
            onClick={nextSlide}
            className="absolute right-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/50 hover:bg-black/70 border border-white/20 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 backdrop-blur-sm"
          >
            <ChevronRight size={24} />
          </button>

          {/* Slide Indicators */}
          <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3">
            {items.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentIndex(index);
                  setIsAutoPlaying(false);
                }}
                className={cn(
                  'w-3 h-3 rounded-full transition-all duration-300',
                  index === currentIndex
                    ? 'bg-red-500 scale-125'
                    : 'bg-white/30 hover:bg-white/50'
                )}
              />
            ))}
          </div>
        </>
      )}

      {/* Auto-play Progress Bar */}
      {isAutoPlaying && items.length > 1 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/10">
          <motion.div
            className="h-full bg-red-500"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 8, ease: "linear" }}
            key={currentIndex}
          />
        </div>
      )}
    </section>
  );
};

export default ModernHeroSection;
