import { NextRequest, NextResponse } from 'next/server';
import VidSrcAPI from '@/lib/vidsrc';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * Debug endpoint to check VidSrc episodes and compare with database
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();
    
    const vidsrc = VidSrcAPI.getInstance();
    
    // Get first page of VidSrc episodes
    console.log('🔍 Fetching VidSrc episodes page 1...');
    const vidsrcEpisodes = await vidsrc.getLatestEpisodes(1);
    
    if (vidsrcEpisodes.length === 0) {
      return NextResponse.json({
        error: 'No VidSrc episodes found'
      });
    }

    // Get first 5 VidSrc episodes
    const sampleVidSrcEpisodes = vidsrcEpisodes.slice(0, 5);
    
    // Check if these IMDb IDs exist in our database
    const imdbIds = sampleVidSrcEpisodes.map(ep => ep.imdb_id);
    const existingSeries = await Series.find({
      imdbId: { $in: imdbIds }
    }).lean();

    const existingImdbIds = existingSeries.map(s => s.imdbId);
    
    const results = sampleVidSrcEpisodes.map(ep => ({
      imdb_id: ep.imdb_id,
      show_title: ep.show_title,
      season: ep.season,
      episode: ep.episode,
      existsInDatabase: existingImdbIds.includes(ep.imdb_id),
      seriesTitle: existingSeries.find(s => s.imdbId === ep.imdb_id)?.title || 'Not found'
    }));

    return NextResponse.json({
      totalVidSrcEpisodes: vidsrcEpisodes.length,
      sampleEpisodes: results,
      stats: {
        totalExistingInDb: existingSeries.length,
        totalVidSrcSample: sampleVidSrcEpisodes.length,
        matchingCount: results.filter(r => r.existsInDatabase).length
      }
    });

  } catch (error) {
    console.error('❌ VidSrc check error:', error);
    return NextResponse.json(
      { error: 'Failed to check VidSrc', message: error.message },
      { status: 500 }
    );
  }
}
