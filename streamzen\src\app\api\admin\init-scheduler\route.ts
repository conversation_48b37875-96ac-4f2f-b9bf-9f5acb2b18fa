import { NextRequest, NextResponse } from 'next/server';
import VidSrcScheduler from '@/lib/vidsrcScheduler';

/**
 * Initialize the VidSrc scheduler
 * This endpoint should be called once when the app starts
 */
export async function GET() {
  try {
    console.log('🚀 Initializing VidSrc Scheduler via API...');
    
    const scheduler = VidSrcScheduler.getInstance();
    await scheduler.initialize();
    
    const status = await scheduler.getSyncStatus();
    
    return NextResponse.json({
      success: true,
      message: 'VidSrc Scheduler initialized successfully',
      data: status
    });
    
  } catch (error) {
    console.error('❌ Failed to initialize VidSrc Scheduler:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to initialize scheduler',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  return GET(); // Same logic for POST
}
