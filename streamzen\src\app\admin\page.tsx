import SyncStatusDashboard from '@/components/SyncStatusDashboard';

export default function AdminPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">StreamZen Admin</h1>
          <p className="text-gray-400">Manage content synchronization and system status</p>
        </div>

        <div className="space-y-8">
          <SyncStatusDashboard />
          
          {/* Additional admin features can be added here */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6">
            <h2 className="text-white font-semibold text-xl mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-white font-medium mb-2">Database Status</h3>
                <p className="text-gray-400 text-sm">MongoDB connection active</p>
              </div>
              <div className="bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-white font-medium mb-2">API Health</h3>
                <p className="text-gray-400 text-sm">All endpoints operational</p>
              </div>
              <div className="bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-white font-medium mb-2">Content Stats</h3>
                <p className="text-gray-400 text-sm">Auto-sync enabled</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
