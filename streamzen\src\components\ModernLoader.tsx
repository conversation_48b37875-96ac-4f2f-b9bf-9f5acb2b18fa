'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Play, Sparkles, Film, Tv } from 'lucide-react';

interface ModernLoaderProps {
  message?: string;
  type?: 'default' | 'content' | 'hero';
  className?: string;
}

const ModernLoader: React.FC<ModernLoaderProps> = ({ 
  message = "Loading amazing content...", 
  type = 'default',
  className = ""
}) => {
  if (type === 'hero') {
    return (
      <div className={`h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center ${className}`}>
        <div className="text-center">
          {/* Animated Logo */}
          <motion.div
            className="relative mb-8"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl mx-auto"
              animate={{ 
                rotate: [0, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                rotate: { duration: 3, repeat: Infinity, ease: "linear" },
                scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
              }}
            >
              <Play size={32} className="text-white fill-current ml-1" />
            </motion.div>
            
            {/* Floating sparkles */}
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                style={{
                  left: `${20 + Math.cos(i * 60 * Math.PI / 180) * 40}px`,
                  top: `${20 + Math.sin(i * 60 * Math.PI / 180) * 40}px`,
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3,
                }}
              >
                <Sparkles size={8} />
              </motion.div>
            ))}
          </motion.div>

          {/* Loading Text */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-3xl font-black text-white mb-2">
              free<span className="text-red-500">Movies</span>WatchNow
            </h2>
            <p className="text-gray-400 text-lg">{message}</p>
          </motion.div>

          {/* Loading Bar */}
          <motion.div
            className="w-64 h-1 bg-gray-800 rounded-full overflow-hidden mx-auto mt-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <motion.div
              className="h-full bg-gradient-to-r from-red-500 to-red-600 rounded-full"
              animate={{ x: ['-100%', '100%'] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            />
          </motion.div>
        </div>
      </div>
    );
  }

  if (type === 'content') {
    return (
      <div className={`h-80 bg-gradient-to-br from-gray-900/20 to-gray-800/20 rounded-3xl flex items-center justify-center border border-gray-800/50 backdrop-blur-sm ${className}`}>
        <div className="text-center">
          <motion.div
            className="flex items-center justify-center space-x-2 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Film className="w-8 h-8 text-red-500" />
            </motion.div>
            <motion.div
              animate={{ rotate: -360 }}
              transition={{ duration: 2.5, repeat: Infinity, ease: "linear" }}
            >
              <Tv className="w-8 h-8 text-blue-500" />
            </motion.div>
          </motion.div>
          
          <motion.p
            className="text-gray-400 font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {message}
          </motion.p>
        </div>
      </div>
    );
  }

  // Default loader
  return (
    <div className={`flex items-center justify-center p-8 ${className}`}>
      <div className="text-center">
        <motion.div
          className="w-12 h-12 border-4 border-gray-600 border-t-red-500 rounded-full mx-auto mb-4"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        <p className="text-gray-400 text-sm">{message}</p>
      </div>
    </div>
  );
};

export default ModernLoader;
