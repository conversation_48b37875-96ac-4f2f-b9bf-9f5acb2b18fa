import { NextRequest, NextResponse } from 'next/server';
import VidSrcSyncService from '@/lib/vidsrcSync';

const syncService = VidSrcSyncService.getInstance();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'status') {
      const status = await syncService.getSyncStatus();
      return NextResponse.json({
        success: true,
        data: status
      });
    }

    if (action === 'force') {
      const results = await syncService.forcSync();
      return NextResponse.json({
        success: true,
        message: 'Force sync completed',
        data: results
      });
    }

    // Default: return sync status
    const status = await syncService.getSyncStatus();
    return NextResponse.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('Error in sync API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process sync request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'force') {
      const results = await syncService.forcSync();
      return NextResponse.json({
        success: true,
        message: `Force sync completed: ${results.movies} movies, ${results.series} series, ${results.episodes} episodes`,
        data: results
      });
    }

    // Fallback to old sync method if no action specified
    const results = await syncService.forcSync();
    return NextResponse.json({
      success: true,
      message: `Successfully synced content: ${results.movies} movies, ${results.series} series, ${results.episodes} episodes`,
      counts: results
    });

  } catch (error) {
    console.error('Error in sync POST API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process sync request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
