import axios from 'axios';

const VIDSRC_BASE_URL = process.env.VIDSRC_BASE_URL || 'https://vidsrc.xyz';

// Multiple streaming sources for better reliability
export const STREAMING_SOURCES = {
  vidsrc_xyz: {
    name: 'VidSrc XYZ',
    baseUrl: 'https://vidsrc.xyz',
    quality: 'HD',
    priority: 1
  },
  autoembed: {
    name: 'AutoEmbed',
    baseUrl: 'https://player.autoembed.cc',
    quality: 'Premium HD',
    priority: 2
  },
  vidsrc_icu: {
    name: 'VidSrc ICU',
    baseUrl: 'https://vidsrc.icu',
    quality: 'HD',
    priority: 3
  },
  vidsrc_cc_v2: {
    name: 'VidSrc CC v2',
    baseUrl: 'https://vidsrc.cc/v2',
    quality: 'HD',
    priority: 4
  },
  vidsrc_cc_v3: {
    name: 'VidSrc CC v3',
    baseUrl: 'https://vidsrc.cc/v3',
    quality: 'HD',
    priority: 5
  }
};

export interface VidSrcMovieData {
  imdb_id: string;
  tmdb_id?: string;
  title: string;
  embed_url: string;
  embed_url_tmdb?: string;
  quality?: string;
}

export interface VidSrcSeriesData {
  imdb_id: string;
  tmdb_id?: string;
  show_title: string;
  embed_url: string;
  embed_url_tmdb?: string;
}

export interface VidSrcEpisodeData {
  imdb_id: string;
  tmdb_id?: string;
  show_title: string;
  season: string;
  episode: string;
  embed_url: string;
  embed_url_tmdb?: string;
  quality?: string;
}

export interface VidSrcLatestResponse<T> {
  result: T[];
}

class VidSrcAPI {
  private static instance: VidSrcAPI;

  static getInstance(): VidSrcAPI {
    if (!VidSrcAPI.instance) {
      VidSrcAPI.instance = new VidSrcAPI();
    }
    return VidSrcAPI.instance;
  }

  /**
   * Generate movie embed URLs for all sources
   */
  generateAllMovieEmbedUrls(imdbId: string, tmdbId?: string): Array<{
    source: string;
    name: string;
    url: string;
    quality: string;
    priority: number;
  }> {
    const cleanImdbId = imdbId.replace('tt', '');
    const urls = [];

    // VidSrc XYZ
    urls.push({
      source: 'vidsrc_xyz',
      name: STREAMING_SOURCES.vidsrc_xyz.name,
      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/movie?imdb=${imdbId}`,
      quality: STREAMING_SOURCES.vidsrc_xyz.quality,
      priority: STREAMING_SOURCES.vidsrc_xyz.priority
    });

    // AutoEmbed
    urls.push({
      source: 'autoembed',
      name: STREAMING_SOURCES.autoembed.name,
      url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/movie/${imdbId}`,
      quality: STREAMING_SOURCES.autoembed.quality,
      priority: STREAMING_SOURCES.autoembed.priority
    });

    // VidSrc ICU
    urls.push({
      source: 'vidsrc_icu',
      name: STREAMING_SOURCES.vidsrc_icu.name,
      url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/movie/${imdbId}`,
      quality: STREAMING_SOURCES.vidsrc_icu.quality,
      priority: STREAMING_SOURCES.vidsrc_icu.priority
    });

    // VidSrc CC v2
    urls.push({
      source: 'vidsrc_cc_v2',
      name: STREAMING_SOURCES.vidsrc_cc_v2.name,
      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/movie/${imdbId}`,
      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,
      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority
    });

    // VidSrc CC v3
    urls.push({
      source: 'vidsrc_cc_v3',
      name: STREAMING_SOURCES.vidsrc_cc_v3.name,
      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/movie/${imdbId}`,
      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,
      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority
    });

    return urls.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Generate movie embed URL (legacy method for backward compatibility)
   */
  generateMovieEmbedUrl(imdbId: string, options?: {
    tmdbId?: string;
    subUrl?: string;
    dsLang?: string;
    autoplay?: boolean;
  }): string {
    const baseUrl = `${VIDSRC_BASE_URL}/embed/movie`;
    const params = new URLSearchParams();

    if (options?.tmdbId) {
      params.append('tmdb', options.tmdbId);
    } else {
      params.append('imdb', imdbId);
    }

    if (options?.subUrl) {
      params.append('sub_url', encodeURIComponent(options.subUrl));
    }

    if (options?.dsLang) {
      params.append('ds_lang', options.dsLang);
    }

    if (options?.autoplay !== undefined) {
      params.append('autoplay', options.autoplay ? '1' : '0');
    }

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Generate episode embed URLs for all sources
   */
  generateAllEpisodeEmbedUrls(imdbId: string, season: number, episode: number, tmdbId?: string): Array<{
    source: string;
    name: string;
    url: string;
    quality: string;
    priority: number;
  }> {
    const cleanImdbId = imdbId.replace('tt', '');
    const urls = [];

    // VidSrc XYZ
    urls.push({
      source: 'vidsrc_xyz',
      name: STREAMING_SOURCES.vidsrc_xyz.name,
      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/tv?imdb=${imdbId}&season=${season}&episode=${episode}`,
      quality: STREAMING_SOURCES.vidsrc_xyz.quality,
      priority: STREAMING_SOURCES.vidsrc_xyz.priority
    });

    // AutoEmbed
    urls.push({
      source: 'autoembed',
      name: STREAMING_SOURCES.autoembed.name,
      url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
      quality: STREAMING_SOURCES.autoembed.quality,
      priority: STREAMING_SOURCES.autoembed.priority
    });

    // VidSrc ICU
    urls.push({
      source: 'vidsrc_icu',
      name: STREAMING_SOURCES.vidsrc_icu.name,
      url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
      quality: STREAMING_SOURCES.vidsrc_icu.quality,
      priority: STREAMING_SOURCES.vidsrc_icu.priority
    });

    // VidSrc CC v2
    urls.push({
      source: 'vidsrc_cc_v2',
      name: STREAMING_SOURCES.vidsrc_cc_v2.name,
      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,
      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority
    });

    // VidSrc CC v3
    urls.push({
      source: 'vidsrc_cc_v3',
      name: STREAMING_SOURCES.vidsrc_cc_v3.name,
      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,
      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority
    });

    return urls.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Generate series embed URL (legacy method for backward compatibility)
   */
  generateSeriesEmbedUrl(imdbId: string, options?: {
    tmdbId?: string;
    dsLang?: string;
  }): string {
    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;
    const params = new URLSearchParams();

    if (options?.tmdbId) {
      params.append('tmdb', options.tmdbId);
    } else {
      params.append('imdb', imdbId);
    }

    if (options?.dsLang) {
      params.append('ds_lang', options.dsLang);
    }

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Generate episode embed URL
   */
  generateEpisodeEmbedUrl(imdbId: string, season: number, episode: number, options?: {
    tmdbId?: string;
    subUrl?: string;
    dsLang?: string;
    autoplay?: boolean;
    autonext?: boolean;
  }): string {
    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;
    const params = new URLSearchParams();
    
    if (options?.tmdbId) {
      params.append('tmdb', options.tmdbId);
    } else {
      params.append('imdb', imdbId);
    }
    
    params.append('season', season.toString());
    params.append('episode', episode.toString());
    
    if (options?.subUrl) {
      params.append('sub_url', encodeURIComponent(options.subUrl));
    }
    
    if (options?.dsLang) {
      params.append('ds_lang', options.dsLang);
    }
    
    if (options?.autoplay !== undefined) {
      params.append('autoplay', options.autoplay ? '1' : '0');
    }
    
    if (options?.autonext !== undefined) {
      params.append('autonext', options.autonext ? '1' : '0');
    }
    
    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Fetch latest movies from VidSrc
   */
  async getLatestMovies(page: number = 1): Promise<VidSrcMovieData[]> {
    try {
      const url = `${VIDSRC_BASE_URL}/movies/latest/page-${page}.json`;
      const response = await axios.get<VidSrcLatestResponse<VidSrcMovieData>>(url);
      return response.data.result || [];
    } catch (error) {
      console.error(`Error fetching latest movies from VidSrc (page ${page}):`, error);
      return [];
    }
  }

  /**
   * Fetch latest TV shows from VidSrc
   */
  async getLatestSeries(page: number = 1): Promise<VidSrcSeriesData[]> {
    try {
      const url = `${VIDSRC_BASE_URL}/tvshows/latest/page-${page}.json`;
      const response = await axios.get<VidSrcLatestResponse<VidSrcSeriesData>>(url);
      return response.data.result || [];
    } catch (error) {
      console.error(`Error fetching latest series from VidSrc (page ${page}):`, error);
      return [];
    }
  }

  /**
   * Fetch latest episodes from VidSrc
   */
  async getLatestEpisodes(page: number = 1, retries: number = 3): Promise<VidSrcEpisodeData[]> {
    const url = `${VIDSRC_BASE_URL}/episodes/latest/page-${page}.json`;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔄 Fetching VidSrc episodes page ${page} (attempt ${attempt}/${retries})...`);
        const response = await axios.get<VidSrcLatestResponse<VidSrcEpisodeData>>(url, {
          timeout: 10000, // 10 second timeout
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
          }
        });

        const episodes = response.data.result || [];
        console.log(`✅ Page ${page}: Found ${episodes.length} episodes`);
        return episodes;

      } catch (error: any) {
        const isLastAttempt = attempt === retries;
        const errorMsg = error.response?.status
          ? `HTTP ${error.response.status} ${error.response.statusText}`
          : error.message;

        if (isLastAttempt) {
          console.error(`❌ Failed to fetch page ${page} after ${retries} attempts: ${errorMsg}`);
          return [];
        } else {
          console.warn(`⚠️ Attempt ${attempt} failed for page ${page}: ${errorMsg}. Retrying...`);
          // Wait before retry (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Max 5 seconds
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    return [];
  }

  /**
   * SIMPLE: Get episodes for a specific series from VidSrc (for embed URLs only)
   * This is now used only to get streaming links, not for episode discovery
   */
  async getSeriesEpisodes(imdbId: string): Promise<Array<{
    season: number;
    episode: number;
    embed_url: string;
    embed_url_tmdb?: string;
  }>> {
    try {
      console.log(`🔍 ADVANCED: Comprehensive episode search for series: ${imdbId}`);

      const allEpisodes = new Map<string, {
        season: number;
        episode: number;
        embed_url: string;
        embed_url_tmdb?: string;
      }>();

      // Strategy 1: Search through ALL latest episodes (not just 15 pages)
      console.log(`📡 Strategy 1: Scanning latest episodes across all pages...`);
      let foundEpisodesInLatest = 0;
      for (let page = 1; page <= 50; page++) { // Increased to 50 pages for comprehensive search
        try {
          const episodes = await this.getLatestEpisodes(page);
          if (episodes.length === 0) break; // No more episodes

          const seriesEpisodes = episodes.filter(episode => episode.imdb_id === imdbId);

          seriesEpisodes.forEach(episode => {
            const key = `S${episode.season}E${episode.episode}`;
            if (!allEpisodes.has(key)) {
              allEpisodes.set(key, {
                season: episode.season,
                episode: episode.episode,
                embed_url: episode.embed_url,
                embed_url_tmdb: episode.embed_url_tmdb
              });
              foundEpisodesInLatest++;
            }
          });

          if (seriesEpisodes.length > 0) {
            console.log(`📺 Page ${page}: Found ${seriesEpisodes.length} episodes`);
          }

          // If no episodes found in last 10 pages, likely reached the end
          if (page > 10 && seriesEpisodes.length === 0) {
            let emptyPages = 0;
            for (let checkPage = page - 9; checkPage <= page; checkPage++) {
              const checkEpisodes = await this.getLatestEpisodes(checkPage);
              if (checkEpisodes.filter(ep => ep.imdb_id === imdbId).length === 0) {
                emptyPages++;
              }
            }
            if (emptyPages >= 8) break; // Stop if 8/10 recent pages are empty
          }
        } catch (error) {
          console.error(`Error fetching episodes page ${page}:`, error);
        }
      }

      console.log(`✅ Strategy 1 Complete: Found ${foundEpisodesInLatest} episodes in latest pages`);

      // Strategy 2: Search through series-specific pages (if available)
      console.log(`📡 Strategy 2: Searching series-specific endpoints...`);
      let foundEpisodesInSeries = 0;
      for (let page = 1; page <= 20; page++) {
        try {
          const seriesEpisodes = await this.getLatestSeries(page);
          const matchingSeries = seriesEpisodes.filter(series => series.imdb_id === imdbId);

          if (matchingSeries.length > 0) {
            console.log(`📺 Series page ${page}: Found matching series, checking for episode data`);
            // If series data includes episode information, extract it
            // This is a placeholder for potential series-specific episode data
          }
        } catch (error) {
          // Series endpoint might not exist, continue
        }
      }

      // Strategy 3: Systematic season-by-season verification
      console.log(`📡 Strategy 3: Systematic season verification...`);
      const episodesBySeason = new Map<number, number[]>();

      // Group found episodes by season
      allEpisodes.forEach((episode, key) => {
        if (!episodesBySeason.has(episode.season)) {
          episodesBySeason.set(episode.season, []);
        }
        episodesBySeason.get(episode.season)!.push(episode.episode);
      });

      // Analyze each season for gaps and missing episodes
      for (const [season, episodes] of episodesBySeason) {
        episodes.sort((a, b) => a - b);
        const minEp = Math.min(...episodes);
        const maxEp = Math.max(...episodes);
        const missingEpisodes: number[] = [];

        for (let ep = 1; ep <= maxEp; ep++) {
          if (!episodes.includes(ep)) {
            missingEpisodes.push(ep);
          }
        }

        if (missingEpisodes.length > 0) {
          console.log(`⚠️ Season ${season}: Missing episodes ${missingEpisodes.join(', ')} (Have: ${episodes.join(', ')})`);

          // Strategy 3a: Search for missing episodes specifically
          console.log(`🔍 Searching for missing episodes in Season ${season}...`);
          // This would involve more targeted searches if VidSrc had episode-specific endpoints
        }
      }

      // Convert Map back to array and sort
      const uniqueEpisodes = Array.from(allEpisodes.values()).sort((a, b) => {
        if (a.season !== b.season) return a.season - b.season;
        return a.episode - b.episode;
      });

      // Final analysis
      const totalSeasons = episodesBySeason.size;
      const totalEpisodes = uniqueEpisodes.length;
      const seasonRanges = Array.from(episodesBySeason.keys()).sort((a, b) => a - b);
      const minSeason = seasonRanges[0] || 0;
      const maxSeason = seasonRanges[seasonRanges.length - 1] || 0;

      console.log(`🎯 COMPREHENSIVE SEARCH COMPLETE:`);
      console.log(`   📺 Total Episodes Found: ${totalEpisodes}`);
      console.log(`   🗂️ Seasons Found: ${totalSeasons} (S${minSeason}-S${maxSeason})`);
      console.log(`   📊 Episodes per Season:`);

      episodesBySeason.forEach((episodes, season) => {
        episodes.sort((a, b) => a - b);
        const gaps = [];
        const maxEp = Math.max(...episodes);
        for (let ep = 1; ep <= maxEp; ep++) {
          if (!episodes.includes(ep)) gaps.push(ep);
        }
        console.log(`      S${season}: ${episodes.length} episodes (${episodes.join(', ')})${gaps.length > 0 ? ` - Missing: ${gaps.join(', ')}` : ' - Complete'}`);
      });

      return uniqueEpisodes;

    } catch (error) {
      console.error(`Error in comprehensive episode search for series ${imdbId}:`, error);
      return [];
    }
  }

  /**
   * Sync latest content from VidSrc to our database
   */
  async syncLatestContent(pages: number = 5): Promise<{
    movies: VidSrcMovieData[];
    series: VidSrcSeriesData[];
    episodes: VidSrcEpisodeData[];
  }> {
    const movies: VidSrcMovieData[] = [];
    const series: VidSrcSeriesData[] = [];
    const episodes: VidSrcEpisodeData[] = [];

    // Fetch multiple pages in parallel
    const moviePromises = Array.from({ length: pages }, (_, i) => this.getLatestMovies(i + 1));
    const seriesPromises = Array.from({ length: pages }, (_, i) => this.getLatestSeries(i + 1));
    const episodePromises = Array.from({ length: pages }, (_, i) => this.getLatestEpisodes(i + 1));

    try {
      const [movieResults, seriesResults, episodeResults] = await Promise.all([
        Promise.all(moviePromises),
        Promise.all(seriesPromises),
        Promise.all(episodePromises)
      ]);

      // Flatten results
      movieResults.forEach(pageMovies => movies.push(...pageMovies));
      seriesResults.forEach(pageSeries => series.push(...pageSeries));
      episodeResults.forEach(pageEpisodes => episodes.push(...pageEpisodes));

      return { movies, series, episodes };
    } catch (error) {
      console.error('Error syncing latest content from VidSrc:', error);
      return { movies, series, episodes };
    }
  }
}

export default VidSrcAPI;
