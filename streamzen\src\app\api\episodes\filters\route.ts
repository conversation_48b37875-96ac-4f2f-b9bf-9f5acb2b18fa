import { NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

export async function GET() {
  try {
    const filterOptions = await contentService.getEpisodeFilterOptions();
    return NextResponse.json(filterOptions);
  } catch (error) {
    console.error('Error fetching episode filter options:', error);
    return NextResponse.json(
      { error: 'Failed to fetch filter options' },
      { status: 500 }
    );
  }
}
