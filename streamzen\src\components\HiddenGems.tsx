'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Gem, Star, Play } from 'lucide-react';
import { motion, useInView } from 'framer-motion';
import { cn, getImageUrl, formatRating } from '@/lib/utils';
import ContentCard from './ContentCard';

interface ContentItem {
  _id: string;
  imdbId: string;
  title: string;
  year?: number;
  startYear?: number;
  posterUrl?: string;
  imdbRating?: number;
}

interface HiddenGemsProps {
  movies: ContentItem[];
  series: ContentItem[];
  className?: string;
  style?: React.CSSProperties;
}

const HiddenGems: React.FC<HiddenGemsProps> = ({
  movies,
  series,
  className,
  style
}) => {
  const sectionRef = React.useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const allContent = [
    ...movies.map(item => ({ ...item, type: 'movie' as const })),
    ...series.map(item => ({ ...item, type: 'series' as const }))
  ].slice(0, 6);

  if (allContent.length === 0) return null;

  return (
    <motion.section
      ref={sectionRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8 }}
      className={cn("relative", className)}
      style={style}
    >
      <div className="max-w-[2560px] mx-auto px-6 lg:px-12">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl">
              <Gem className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-4xl lg:text-5xl font-black text-white">
              Hidden Gems
            </h2>
          </div>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Highly rated treasures waiting to be discovered
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          {allContent.map((item, index) => (
            <motion.div
              key={item._id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              className="relative"
            >
              {/* Hidden Gem badge overlay */}
              <div className="absolute top-3 left-3 z-20 flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full text-xs font-bold text-white shadow-lg">
                <Gem className="w-3 h-3" />
                <span>Hidden</span>
              </div>

              <ContentCard
                id={item._id}
                imdbId={item.imdbId}
                title={item.title}
                year={item.year || item.startYear}
                posterUrl={item.posterUrl}
                imdbRating={item.imdbRating}
                description={item.description}
                type={item.type}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

export default HiddenGems;
