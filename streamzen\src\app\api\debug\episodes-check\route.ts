import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * Simple debug endpoint to check episode isLatestRelease flags
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();

    // Find a series with episodes
    const seriesWithEpisodes = await Series.findOne({
      'episodes.0': { $exists: true }
    }).lean();

    if (!seriesWithEpisodes) {
      return NextResponse.json({
        error: 'No series with episodes found'
      });
    }

    // Check the first few episodes
    const firstEpisodes = seriesWithEpisodes.episodes.slice(0, 5);
    
    const episodeDetails = firstEpisodes.map(ep => ({
      season: ep.season,
      episode: ep.episode,
      title: ep.episodeTitle,
      isLatestRelease: ep.isLatestRelease,
      hasIsLatestReleaseField: ep.hasOwnProperty('isLatestRelease'),
      fieldType: typeof ep.isLatestRelease,
      allFields: Object.keys(ep)
    }));

    // Check if any episodes have isLatestRelease: true
    const hasLatestTrue = await Series.countDocuments({
      'episodes.isLatestRelease': true
    });

    // Check if any episodes have isLatestRelease field at all
    const hasLatestField = await Series.countDocuments({
      'episodes.isLatestRelease': { $exists: true }
    });

    return NextResponse.json({
      seriesTitle: seriesWithEpisodes.title,
      imdbId: seriesWithEpisodes.imdbId,
      totalEpisodes: seriesWithEpisodes.episodes.length,
      episodeDetails,
      counts: {
        seriesWithLatestTrue: hasLatestTrue,
        seriesWithLatestField: hasLatestField
      }
    });

  } catch (error) {
    console.error('❌ Debug episodes check error:', error);
    return NextResponse.json(
      { error: 'Failed to check episodes', message: error.message },
      { status: 500 }
    );
  }
}
