import { notFound, redirect } from 'next/navigation';
import { apiClient } from '@/lib/api';
import VidSrcAPI from '@/lib/vidsrc';
import VideoPlayer from '@/components/VideoPlayer';

interface EpisodeWatchPageProps {
  params: Promise<{
    id: string;
    season: string;
    episode: string;
  }>;
}

async function getSeriesAndTriggerSync(imdbId: string, seasonNum: number, episodeNum: number) {
  try {
    // Get series information
    const series = await apiClient.getSeriesById(imdbId);
    if (!series) {
      return null;
    }

    // Check if the specific episode exists
    const episodes = await apiClient.getSeriesEpisodes(imdbId);
    const targetEpisode = episodes.find(
      ep => ep.season === seasonNum && ep.episode === episodeNum
    );

    // If episode doesn't exist, trigger episode sync
    if (!targetEpisode) {
      console.log(`🔄 Episode S${seasonNum}E${episodeNum} not found, triggering episode sync for series: ${series.title}`);

      try {
        // Trigger episode check/sync for this series
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/series/${imdbId}/episodes/check`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const result = await response.json();
          console.log(`✅ Episode sync completed: ${result.stats?.newEpisodesAdded || 0} new episodes added`);
        } else {
          console.error('❌ Episode sync failed:', response.statusText);
        }
      } catch (syncError) {
        console.error('❌ Error triggering episode sync:', syncError);
      }
    }

    return series;
  } catch (error) {
    console.error('Error in getSeriesAndTriggerSync:', error);
    return null;
  }
}

export default async function EpisodeWatchPage({ params }: EpisodeWatchPageProps) {
  const { id, season, episode } = await params;
  const seasonNum = parseInt(season);
  const episodeNum = parseInt(episode);

  if (isNaN(seasonNum) || isNaN(episodeNum)) {
    notFound();
  }

  // Get series and trigger episode sync if needed
  const series = await getSeriesAndTriggerSync(id, seasonNum, episodeNum);

  if (!series) {
    notFound();
  }

  // Redirect to series watch page with episode selected for better UX
  // This provides episode selector and better navigation
  redirect(`/watch/series/${id}?season=${season}&episode=${episode}`);
}
