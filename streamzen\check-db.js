const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/streamzen');

const seriesSchema = new mongoose.Schema({}, { strict: false });
const Series = mongoose.model('Series', seriesSchema);

async function checkDatabase() {
  try {
    console.log('🔍 Checking database structure...');
    
    // Find a series with episodes
    const seriesWithEpisodes = await Series.findOne(
      { 'episodes.0': { $exists: true } },
      { title: 1, posterUrl: 1, episodes: { $slice: 2 } }
    );
    
    if (seriesWithEpisodes) {
      console.log('\n📊 Sample series with episodes:');
      console.log('Series Title:', seriesWithEpisodes.title);
      console.log('Series Poster URL:', seriesWithEpisodes.posterUrl);
      console.log('Episodes count:', seriesWithEpisodes.episodes?.length || 0);
      
      if (seriesWithEpisodes.episodes && seriesWithEpisodes.episodes.length > 0) {
        console.log('\n📺 Sample episode structure:');
        const episode = seriesWithEpisodes.episodes[0];
        console.log('Episode Title:', episode.episodeTitle);
        console.log('Episode Poster URL:', episode.posterUrl);
        console.log('Episode Season/Episode:', `S${episode.season}E${episode.episode}`);
        console.log('Episode has poster:', !!episode.posterUrl);
        console.log('Series has poster:', !!seriesWithEpisodes.posterUrl);
      }
    }
    
    // Check total counts
    const totalSeries = await Series.countDocuments({});
    const seriesWithEpisodesCount = await Series.countDocuments({ 'episodes.0': { $exists: true } });
    const seriesWithPosters = await Series.countDocuments({ posterUrl: { $exists: true, $ne: '', $ne: null } });
    
    console.log('\n📈 Database Statistics:');
    console.log('Total series:', totalSeries);
    console.log('Series with episodes:', seriesWithEpisodesCount);
    console.log('Series with posters:', seriesWithPosters);
    
    // Check episode poster vs series poster distribution
    const episodePosterStats = await Series.aggregate([
      { $match: { 'episodes.0': { $exists: true } } },
      { $unwind: '$episodes' },
      {
        $group: {
          _id: null,
          totalEpisodes: { $sum: 1 },
          episodesWithPosters: {
            $sum: {
              $cond: [
                { $and: [{ $ne: ['$episodes.posterUrl', ''] }, { $ne: ['$episodes.posterUrl', null] }] },
                1,
                0
              ]
            }
          },
          seriesWithPosters: {
            $sum: {
              $cond: [
                { $and: [{ $ne: ['$posterUrl', ''] }, { $ne: ['$posterUrl', null] }] },
                1,
                0
              ]
            }
          }
        }
      }
    ]);
    
    if (episodePosterStats.length > 0) {
      const stats = episodePosterStats[0];
      console.log('\n🖼️ Poster Statistics:');
      console.log('Total episodes:', stats.totalEpisodes);
      console.log('Episodes with posters:', stats.episodesWithPosters);
      console.log('Episodes from series with posters:', stats.seriesWithPosters);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.disconnect();
  }
}

checkDatabase();
