import { NextRequest, NextResponse } from 'next/server';
import UniversalSyncService from '@/lib/universalSyncService';

/**
 * Test endpoint to verify Universal Sync Service is working
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Universal Sync Service...');
    
    const universalSync = UniversalSyncService.getInstance();
    
    // Initialize if not already done
    await universalSync.initialize();
    
    // Get current status
    const status = await universalSync.getSyncStatus();
    const utcNow = new Date().toISOString();
    
    return NextResponse.json({
      success: true,
      message: 'Universal Sync Service test completed',
      utcTime: utcNow,
      status: {
        syncType: status?.syncType || 'UNIVERSAL_SYNC',
        isRunning: status?.isRunning || false,
        lastSyncTime: status?.lastSyncTime || null,
        nextSyncTime: status?.nextSyncTime || null,
        lastResult: status?.lastResult || null
      }
    });

  } catch (error) {
    console.error('❌ Universal Sync Service test failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Test failed',
        message: error.message,
        utcTime: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * POST method for manual test sync
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Universal Sync Service with manual sync...');
    
    const universalSync = UniversalSyncService.getInstance();
    
    // Initialize if not already done
    await universalSync.initialize();
    
    // Force a sync
    const result = await universalSync.forceSync();
    
    return NextResponse.json({
      success: true,
      message: 'Manual sync test completed',
      data: result
    });

  } catch (error) {
    console.error('❌ Manual sync test failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Manual sync test failed',
        message: error.message,
        utcTime: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
