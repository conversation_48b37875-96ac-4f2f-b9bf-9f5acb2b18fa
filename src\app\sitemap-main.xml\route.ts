import { NextRequest, NextResponse } from 'next/server';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlsXML = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlsXML}
</urlset>`;
}

export async function GET(request: NextRequest) {
  try {
    const now = new Date().toISOString();
    
    const staticUrls: SitemapUrl[] = [
      {
        loc: BASE_URL,
        lastmod: now,
        changefreq: 'daily',
        priority: 1.0,
      },
      {
        loc: `${BASE_URL}/movies`,
        lastmod: now,
        changefreq: 'hourly',
        priority: 0.9,
      },
      {
        loc: `${BASE_URL}/series`,
        lastmod: now,
        changefreq: 'hourly',
        priority: 0.9,
      },
      {
        loc: `${BASE_URL}/episodes`,
        lastmod: now,
        changefreq: 'hourly',
        priority: 0.8,
      },
      {
        loc: `${BASE_URL}/search`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.7,
      },
    ];

    // Add genre-based pages for better SEO
    const popularGenres = [
      'Action', 'Adventure', 'Comedy', 'Drama', 'Horror', 'Thriller',
      'Romance', 'Sci-Fi', 'Fantasy', 'Crime', 'Mystery', 'Animation',
      'Family', 'Documentary', 'Biography', 'History', 'War', 'Western'
    ];

    // Add movie genre pages
    popularGenres.forEach(genre => {
      staticUrls.push({
        loc: `${BASE_URL}/movies?genre=${encodeURIComponent(genre)}`,
        lastmod: now,
        changefreq: 'daily',
        priority: 0.8,
      });
    });

    // Add series genre pages
    popularGenres.forEach(genre => {
      staticUrls.push({
        loc: `${BASE_URL}/series?genre=${encodeURIComponent(genre)}`,
        lastmod: now,
        changefreq: 'daily',
        priority: 0.8,
      });
    });

    // Add year-based pages for recent years
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= currentYear - 10; year--) {
      staticUrls.push({
        loc: `${BASE_URL}/movies?year=${year}`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.7,
      });
      staticUrls.push({
        loc: `${BASE_URL}/series?year=${year}`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.7,
      });
    }

    // Add popular search terms and combinations
    const popularSearchTerms = [
      'latest movies', 'new series', 'action movies', 'comedy series',
      'horror movies', 'drama series', 'thriller movies', 'sci-fi series',
      'romance movies', 'crime series', 'animated movies', 'documentary series'
    ];

    popularSearchTerms.forEach(term => {
      staticUrls.push({
        loc: `${BASE_URL}/search?q=${encodeURIComponent(term)}`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.6,
      });
    });

    const sitemapXML = generateSitemapXML(staticUrls);

    return new NextResponse(sitemapXML, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating main sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
