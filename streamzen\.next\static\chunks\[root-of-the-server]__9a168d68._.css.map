{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\",\n      \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\",\n      \"Courier New\", monospace;\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-300: oklch(80.8% 0.114 19.571);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-orange-200: oklch(90.1% 0.076 70.697);\n    --color-orange-300: oklch(83.7% 0.128 66.29);\n    --color-orange-400: oklch(75% 0.183 55.934);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-orange-900: oklch(40.8% 0.123 38.172);\n    --color-amber-400: oklch(82.8% 0.189 84.429);\n    --color-amber-500: oklch(76.9% 0.188 70.08);\n    --color-amber-600: oklch(66.6% 0.179 58.318);\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\n    --color-green-300: oklch(87.1% 0.15 154.449);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-green-900: oklch(39.3% 0.095 152.535);\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\n    --color-teal-600: oklch(60% 0.118 184.704);\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\n    --color-blue-300: oklch(80.9% 0.105 251.813);\n    --color-blue-400: oklch(70.7% 0.165 254.624);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\n    --color-purple-200: oklch(90.2% 0.063 306.703);\n    --color-purple-400: oklch(71.4% 0.203 305.504);\n    --color-purple-500: oklch(62.7% 0.265 303.9);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-700: oklch(49.6% 0.265 301.924);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-purple-900: oklch(38.1% 0.176 304.987);\n    --color-pink-600: oklch(59.2% 0.249 0.584);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --text-7xl: 4.5rem;\n    --text-7xl--line-height: 1;\n    --text-8xl: 6rem;\n    --text-8xl--line-height: 1;\n    --font-weight-light: 300;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --font-weight-black: 900;\n    --tracking-tight: -0.025em;\n    --tracking-wide: 0.025em;\n    --tracking-wider: 0.05em;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --radius-2xl: 1rem;\n    --radius-3xl: 1.5rem;\n    --animate-spin: spin 1s linear infinite;\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --blur-lg: 16px;\n    --blur-xl: 24px;\n    --blur-2xl: 40px;\n    --blur-3xl: 64px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .-inset-1 {\n    inset: calc(var(--spacing) * -1);\n  }\n  .-inset-4 {\n    inset: calc(var(--spacing) * -4);\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .-top-1 {\n    top: calc(var(--spacing) * -1);\n  }\n  .-top-2 {\n    top: calc(var(--spacing) * -2);\n  }\n  .-top-40 {\n    top: calc(var(--spacing) * -40);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-1\\/3 {\n    top: calc(1/3 * 100%);\n  }\n  .top-1\\/4 {\n    top: calc(1/4 * 100%);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-3 {\n    top: calc(var(--spacing) * 3);\n  }\n  .top-3\\/4 {\n    top: calc(3/4 * 100%);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-6 {\n    top: calc(var(--spacing) * 6);\n  }\n  .top-8 {\n    top: calc(var(--spacing) * 8);\n  }\n  .top-12 {\n    top: calc(var(--spacing) * 12);\n  }\n  .top-full {\n    top: 100%;\n  }\n  .-right-1 {\n    right: calc(var(--spacing) * -1);\n  }\n  .-right-3 {\n    right: calc(var(--spacing) * -3);\n  }\n  .-right-40 {\n    right: calc(var(--spacing) * -40);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-1\\/2 {\n    right: calc(1/2 * 100%);\n  }\n  .right-1\\/3 {\n    right: calc(1/3 * 100%);\n  }\n  .right-1\\/4 {\n    right: calc(1/4 * 100%);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-6 {\n    right: calc(var(--spacing) * 6);\n  }\n  .right-8 {\n    right: calc(var(--spacing) * 8);\n  }\n  .-bottom-40 {\n    bottom: calc(var(--spacing) * -40);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-1\\/3 {\n    bottom: calc(1/3 * 100%);\n  }\n  .bottom-1\\/4 {\n    bottom: calc(1/4 * 100%);\n  }\n  .bottom-4 {\n    bottom: calc(var(--spacing) * 4);\n  }\n  .bottom-6 {\n    bottom: calc(var(--spacing) * 6);\n  }\n  .bottom-8 {\n    bottom: calc(var(--spacing) * 8);\n  }\n  .-left-2 {\n    left: calc(var(--spacing) * -2);\n  }\n  .-left-40 {\n    left: calc(var(--spacing) * -40);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-1\\/3 {\n    left: calc(1/3 * 100%);\n  }\n  .left-1\\/4 {\n    left: calc(1/4 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-4 {\n    left: calc(var(--spacing) * 4);\n  }\n  .left-6 {\n    left: calc(var(--spacing) * 6);\n  }\n  .left-8 {\n    left: calc(var(--spacing) * 8);\n  }\n  .-z-10 {\n    z-index: calc(10 * -1);\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-25 {\n    z-index: 25;\n  }\n  .z-30 {\n    z-index: 30;\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-60 {\n    z-index: 60;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .-mt-1 {\n    margin-top: calc(var(--spacing) * -1);\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-1\\.5 {\n    margin-top: calc(var(--spacing) * 1.5);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mt-16 {\n    margin-top: calc(var(--spacing) * 16);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mr-4 {\n    margin-right: calc(var(--spacing) * 4);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .mb-16 {\n    margin-bottom: calc(var(--spacing) * 16);\n  }\n  .ml-0\\.5 {\n    margin-left: calc(var(--spacing) * 0.5);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-4 {\n    margin-left: calc(var(--spacing) * 4);\n  }\n  .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n  }\n  .line-clamp-2 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline {\n    display: inline;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .aspect-\\[2\\/3\\] {\n    aspect-ratio: 2/3;\n  }\n  .h-0\\.5 {\n    height: calc(var(--spacing) * 0.5);\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-1\\.5 {\n    height: calc(var(--spacing) * 1.5);\n  }\n  .h-1\\/3 {\n    height: calc(1/3 * 100%);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-14 {\n    height: calc(var(--spacing) * 14);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-24 {\n    height: calc(var(--spacing) * 24);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-40 {\n    height: calc(var(--spacing) * 40);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-72 {\n    height: calc(var(--spacing) * 72);\n  }\n  .h-80 {\n    height: calc(var(--spacing) * 80);\n  }\n  .h-96 {\n    height: calc(var(--spacing) * 96);\n  }\n  .h-\\[60vh\\] {\n    height: 60vh;\n  }\n  .h-\\[70vh\\] {\n    height: 70vh;\n  }\n  .h-\\[75vh\\] {\n    height: 75vh;\n  }\n  .h-\\[85vh\\] {\n    height: 85vh;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .max-h-48 {\n    max-height: calc(var(--spacing) * 48);\n  }\n  .max-h-64 {\n    max-height: calc(var(--spacing) * 64);\n  }\n  .max-h-72 {\n    max-height: calc(var(--spacing) * 72);\n  }\n  .max-h-80 {\n    max-height: calc(var(--spacing) * 80);\n  }\n  .max-h-96 {\n    max-height: calc(var(--spacing) * 96);\n  }\n  .max-h-\\[500px\\] {\n    max-height: 500px;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-1\\.5 {\n    width: calc(var(--spacing) * 1.5);\n  }\n  .w-1\\/3 {\n    width: calc(1/3 * 100%);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-14 {\n    width: calc(var(--spacing) * 14);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-20 {\n    width: calc(var(--spacing) * 20);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-52 {\n    width: calc(var(--spacing) * 52);\n  }\n  .w-56 {\n    width: calc(var(--spacing) * 56);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-72 {\n    width: calc(var(--spacing) * 72);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-96 {\n    width: calc(var(--spacing) * 96);\n  }\n  .w-\\[600px\\] {\n    width: 600px;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .max-w-\\[1920px\\] {\n    max-width: 1920px;\n  }\n  .max-w-\\[2560px\\] {\n    max-width: 2560px;\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-none {\n    max-width: none;\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xl {\n    max-width: var(--container-xl);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-none {\n    flex: none;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-8 {\n    --tw-translate-x: calc(var(--spacing) * -8);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-16 {\n    --tw-translate-x: calc(var(--spacing) * -16);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-1\\/2 {\n    --tw-translate-x: calc(1/2 * 100%);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-8 {\n    --tw-translate-x: calc(var(--spacing) * 8);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-10 {\n    --tw-translate-x: calc(var(--spacing) * 10);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-16 {\n    --tw-translate-x: calc(var(--spacing) * 16);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-10 {\n    --tw-translate-y: calc(var(--spacing) * -10);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-8 {\n    --tw-translate-y: calc(var(--spacing) * 8);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-100 {\n    --tw-scale-x: 100%;\n    --tw-scale-y: 100%;\n    --tw-scale-z: 100%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-105 {\n    --tw-scale-x: 105%;\n    --tw-scale-y: 105%;\n    --tw-scale-z: 105%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-125 {\n    --tw-scale-x: 125%;\n    --tw-scale-y: 125%;\n    --tw-scale-z: 125%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .rotate-6 {\n    rotate: 6deg;\n  }\n  .rotate-180 {\n    rotate: 180deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-ping {\n    animation: var(--animate-ping);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-not-allowed {\n    cursor: not-allowed;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-around {\n    justify-content: space-around;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .gap-12 {\n    gap: calc(var(--spacing) * 12);\n  }\n  .gap-16 {\n    gap: calc(var(--spacing) * 16);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-1\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-16 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-32 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 32) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 32) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .scroll-smooth {\n    scroll-behavior: smooth;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-3xl {\n    border-radius: var(--radius-3xl);\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .rounded-t-2xl {\n    border-top-left-radius: var(--radius-2xl);\n    border-top-right-radius: var(--radius-2xl);\n  }\n  .rounded-b-2xl {\n    border-bottom-right-radius: var(--radius-2xl);\n    border-bottom-left-radius: var(--radius-2xl);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-4 {\n    border-style: var(--tw-border-style);\n    border-width: 4px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-l-2 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 2px;\n  }\n  .border-amber-500\\/30 {\n    border-color: color-mix(in srgb, oklch(76.9% 0.188 70.08) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-amber-500) 30%, transparent);\n    }\n  }\n  .border-blue-500 {\n    border-color: var(--color-blue-500);\n  }\n  .border-blue-500\\/20 {\n    border-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);\n    }\n  }\n  .border-blue-500\\/30 {\n    border-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);\n    }\n  }\n  .border-blue-500\\/50 {\n    border-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);\n    }\n  }\n  .border-gray-500\\/20 {\n    border-color: color-mix(in srgb, oklch(55.1% 0.027 264.364) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-500) 20%, transparent);\n    }\n  }\n  .border-gray-600 {\n    border-color: var(--color-gray-600);\n  }\n  .border-gray-600\\/30 {\n    border-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-600) 30%, transparent);\n    }\n  }\n  .border-gray-600\\/50 {\n    border-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-600) 50%, transparent);\n    }\n  }\n  .border-gray-700 {\n    border-color: var(--color-gray-700);\n  }\n  .border-gray-700\\/30 {\n    border-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-700) 30%, transparent);\n    }\n  }\n  .border-gray-700\\/50 {\n    border-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-700) 50%, transparent);\n    }\n  }\n  .border-gray-800 {\n    border-color: var(--color-gray-800);\n  }\n  .border-gray-800\\/50 {\n    border-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);\n    }\n  }\n  .border-green-500\\/20 {\n    border-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);\n    }\n  }\n  .border-green-500\\/30 {\n    border-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-green-500) 30%, transparent);\n    }\n  }\n  .border-green-500\\/50 {\n    border-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-green-500) 50%, transparent);\n    }\n  }\n  .border-indigo-500\\/30 {\n    border-color: color-mix(in srgb, oklch(58.5% 0.233 277.117) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-indigo-500) 30%, transparent);\n    }\n  }\n  .border-orange-500\\/30 {\n    border-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-orange-500) 30%, transparent);\n    }\n  }\n  .border-purple-500\\/30 {\n    border-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-purple-500) 30%, transparent);\n    }\n  }\n  .border-red-500 {\n    border-color: var(--color-red-500);\n  }\n  .border-red-500\\/20 {\n    border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);\n    }\n  }\n  .border-red-500\\/30 {\n    border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);\n    }\n  }\n  .border-red-500\\/40 {\n    border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-red-500) 40%, transparent);\n    }\n  }\n  .border-red-500\\/50 {\n    border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);\n    }\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-white {\n    border-color: var(--color-white);\n  }\n  .border-white\\/5 {\n    border-color: color-mix(in srgb, #fff 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 5%, transparent);\n    }\n  }\n  .border-white\\/10 {\n    border-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .border-white\\/20 {\n    border-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .border-white\\/30 {\n    border-color: color-mix(in srgb, #fff 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 30%, transparent);\n    }\n  }\n  .border-yellow-400\\/30 {\n    border-color: color-mix(in srgb, oklch(85.2% 0.199 91.936) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-yellow-400) 30%, transparent);\n    }\n  }\n  .border-yellow-500\\/30 {\n    border-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-yellow-500) 30%, transparent);\n    }\n  }\n  .border-yellow-500\\/50 {\n    border-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-yellow-500) 50%, transparent);\n    }\n  }\n  .border-t-blue-500 {\n    border-top-color: var(--color-blue-500);\n  }\n  .border-t-red-500 {\n    border-top-color: var(--color-red-500);\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-black\\/0 {\n    background-color: color-mix(in srgb, #000 0%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 0%, transparent);\n    }\n  }\n  .bg-black\\/20 {\n    background-color: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n  }\n  .bg-black\\/30 {\n    background-color: color-mix(in srgb, #000 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);\n    }\n  }\n  .bg-black\\/40 {\n    background-color: color-mix(in srgb, #000 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 40%, transparent);\n    }\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-black\\/60 {\n    background-color: color-mix(in srgb, #000 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);\n    }\n  }\n  .bg-black\\/70 {\n    background-color: color-mix(in srgb, #000 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 70%, transparent);\n    }\n  }\n  .bg-black\\/80 {\n    background-color: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n  }\n  .bg-black\\/90 {\n    background-color: color-mix(in srgb, #000 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 90%, transparent);\n    }\n  }\n  .bg-black\\/95 {\n    background-color: color-mix(in srgb, #000 95%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 95%, transparent);\n    }\n  }\n  .bg-blue-400 {\n    background-color: var(--color-blue-400);\n  }\n  .bg-blue-400\\/60 {\n    background-color: color-mix(in srgb, oklch(70.7% 0.165 254.624) 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-400) 60%, transparent);\n    }\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-blue-500\\/10 {\n    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);\n    }\n  }\n  .bg-blue-500\\/20 {\n    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);\n    }\n  }\n  .bg-blue-500\\/30 {\n    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);\n    }\n  }\n  .bg-blue-600\\/5 {\n    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-600) 5%, transparent);\n    }\n  }\n  .bg-blue-600\\/10 {\n    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-600) 10%, transparent);\n    }\n  }\n  .bg-blue-600\\/20 {\n    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-600) 20%, transparent);\n    }\n  }\n  .bg-blue-900\\/20 {\n    background-color: color-mix(in srgb, oklch(37.9% 0.146 265.522) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);\n    }\n  }\n  .bg-current {\n    background-color: currentcolor;\n  }\n  .bg-gray-400 {\n    background-color: var(--color-gray-400);\n  }\n  .bg-gray-500 {\n    background-color: var(--color-gray-500);\n  }\n  .bg-gray-600\\/50 {\n    background-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-600) 50%, transparent);\n    }\n  }\n  .bg-gray-700 {\n    background-color: var(--color-gray-700);\n  }\n  .bg-gray-700\\/5 {\n    background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-700) 5%, transparent);\n    }\n  }\n  .bg-gray-700\\/15 {\n    background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 15%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-700) 15%, transparent);\n    }\n  }\n  .bg-gray-700\\/30 {\n    background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-700) 30%, transparent);\n    }\n  }\n  .bg-gray-700\\/50 {\n    background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-700) 50%, transparent);\n    }\n  }\n  .bg-gray-800 {\n    background-color: var(--color-gray-800);\n  }\n  .bg-gray-800\\/5 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 5%, transparent);\n    }\n  }\n  .bg-gray-800\\/15 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 15%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 15%, transparent);\n    }\n  }\n  .bg-gray-800\\/20 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 20%, transparent);\n    }\n  }\n  .bg-gray-800\\/30 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 30%, transparent);\n    }\n  }\n  .bg-gray-800\\/40 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 40%, transparent);\n    }\n  }\n  .bg-gray-800\\/50 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);\n    }\n  }\n  .bg-gray-800\\/60 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 60%, transparent);\n    }\n  }\n  .bg-gray-900 {\n    background-color: var(--color-gray-900);\n  }\n  .bg-gray-900\\/20 {\n    background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-900) 20%, transparent);\n    }\n  }\n  .bg-gray-900\\/40 {\n    background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-900) 40%, transparent);\n    }\n  }\n  .bg-gray-900\\/50 {\n    background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-900) 50%, transparent);\n    }\n  }\n  .bg-gray-900\\/95 {\n    background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 95%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-900) 95%, transparent);\n    }\n  }\n  .bg-green-400 {\n    background-color: var(--color-green-400);\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-green-500\\/20 {\n    background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);\n    }\n  }\n  .bg-green-500\\/90 {\n    background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-500) 90%, transparent);\n    }\n  }\n  .bg-green-600\\/20 {\n    background-color: color-mix(in srgb, oklch(62.7% 0.194 149.214) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-600) 20%, transparent);\n    }\n  }\n  .bg-green-900\\/20 {\n    background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);\n    }\n  }\n  .bg-orange-500 {\n    background-color: var(--color-orange-500);\n  }\n  .bg-orange-800\\/5 {\n    background-color: color-mix(in srgb, oklch(47% 0.157 37.304) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-orange-800) 5%, transparent);\n    }\n  }\n  .bg-orange-900\\/20 {\n    background-color: color-mix(in srgb, oklch(40.8% 0.123 38.172) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-orange-900) 20%, transparent);\n    }\n  }\n  .bg-purple-500 {\n    background-color: var(--color-purple-500);\n  }\n  .bg-purple-600\\/10 {\n    background-color: color-mix(in srgb, oklch(55.8% 0.288 302.321) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-purple-600) 10%, transparent);\n    }\n  }\n  .bg-purple-600\\/20 {\n    background-color: color-mix(in srgb, oklch(55.8% 0.288 302.321) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-purple-600) 20%, transparent);\n    }\n  }\n  .bg-purple-800\\/5 {\n    background-color: color-mix(in srgb, oklch(43.8% 0.218 303.724) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-purple-800) 5%, transparent);\n    }\n  }\n  .bg-purple-900\\/20 {\n    background-color: color-mix(in srgb, oklch(38.1% 0.176 304.987) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);\n    }\n  }\n  .bg-red-400 {\n    background-color: var(--color-red-400);\n  }\n  .bg-red-500 {\n    background-color: var(--color-red-500);\n  }\n  .bg-red-500\\/10 {\n    background-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-500) 10%, transparent);\n    }\n  }\n  .bg-red-500\\/20 {\n    background-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);\n    }\n  }\n  .bg-red-500\\/30 {\n    background-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);\n    }\n  }\n  .bg-red-500\\/90 {\n    background-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-500) 90%, transparent);\n    }\n  }\n  .bg-red-600 {\n    background-color: var(--color-red-600);\n  }\n  .bg-red-600\\/5 {\n    background-color: color-mix(in srgb, oklch(57.7% 0.245 27.325) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-600) 5%, transparent);\n    }\n  }\n  .bg-red-900\\/20 {\n    background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);\n    }\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/5 {\n    background-color: color-mix(in srgb, #fff 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);\n    }\n  }\n  .bg-white\\/10 {\n    background-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .bg-white\\/15 {\n    background-color: color-mix(in srgb, #fff 15%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 15%, transparent);\n    }\n  }\n  .bg-white\\/20 {\n    background-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .bg-white\\/30 {\n    background-color: color-mix(in srgb, #fff 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);\n    }\n  }\n  .bg-white\\/40 {\n    background-color: color-mix(in srgb, #fff 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 40%, transparent);\n    }\n  }\n  .bg-white\\/50 {\n    background-color: color-mix(in srgb, #fff 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);\n    }\n  }\n  .bg-white\\/60 {\n    background-color: color-mix(in srgb, #fff 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 60%, transparent);\n    }\n  }\n  .bg-yellow-400 {\n    background-color: var(--color-yellow-400);\n  }\n  .bg-yellow-500 {\n    background-color: var(--color-yellow-500);\n  }\n  .bg-yellow-500\\/20 {\n    background-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);\n    }\n  }\n  .bg-yellow-500\\/90 {\n    background-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-yellow-500) 90%, transparent);\n    }\n  }\n  .bg-gradient-to-b {\n    --tw-gradient-position: to bottom in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-l {\n    --tw-gradient-position: to left in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-t {\n    --tw-gradient-position: to top in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-\\[radial-gradient\\(circle_at_1px_1px\\,rgba\\(255\\,255\\,255\\,0\\.02\\)_1px\\,transparent_0\\)\\] {\n    background-image: radial-gradient(circle at 1px 1px,rgba(255,255,255,0.02) 1px,transparent 0);\n  }\n  .from-amber-500 {\n    --tw-gradient-from: var(--color-amber-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-amber-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(76.9% 0.188 70.08) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-amber-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-black {\n    --tw-gradient-from: var(--color-black);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-black\\/50 {\n    --tw-gradient-from: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-black\\/60 {\n    --tw-gradient-from: color-mix(in srgb, #000 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-black) 60%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-black\\/80 {\n    --tw-gradient-from: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-black\\/90 {\n    --tw-gradient-from: color-mix(in srgb, #000 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-black) 90%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-black\\/95 {\n    --tw-gradient-from: color-mix(in srgb, #000 95%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-black) 95%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500 {\n    --tw-gradient-from: var(--color-blue-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500\\/50 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-600 {\n    --tw-gradient-from: var(--color-blue-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-600\\/8 {\n    --tw-gradient-from: color-mix(in srgb, oklch(54.6% 0.245 262.881) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-blue-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-900\\/30 {\n    --tw-gradient-from: color-mix(in srgb, oklch(37.9% 0.146 265.522) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-cyan-500 {\n    --tw-gradient-from: var(--color-cyan-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-emerald-500 {\n    --tw-gradient-from: var(--color-emerald-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-500 {\n    --tw-gradient-from: var(--color-gray-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-800 {\n    --tw-gradient-from: var(--color-gray-800);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-800\\/50 {\n    --tw-gradient-from: color-mix(in srgb, oklch(27.8% 0.033 256.848) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-gray-800) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-900 {\n    --tw-gradient-from: var(--color-gray-900);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-900\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(21% 0.034 264.665) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-gray-900) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-900\\/30 {\n    --tw-gradient-from: color-mix(in srgb, oklch(21% 0.034 264.665) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-gray-900) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-900\\/50 {\n    --tw-gradient-from: color-mix(in srgb, oklch(21% 0.034 264.665) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-gray-900) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-900\\/95 {\n    --tw-gradient-from: color-mix(in srgb, oklch(21% 0.034 264.665) 95%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-gray-900) 95%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-400 {\n    --tw-gradient-from: var(--color-green-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-500 {\n    --tw-gradient-from: var(--color-green-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-600 {\n    --tw-gradient-from: var(--color-green-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-600\\/8 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.7% 0.194 149.214) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-green-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-900\\/30 {\n    --tw-gradient-from: color-mix(in srgb, oklch(39.3% 0.095 152.535) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-indigo-500 {\n    --tw-gradient-from: var(--color-indigo-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-indigo-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(58.5% 0.233 277.117) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-500 {\n    --tw-gradient-from: var(--color-orange-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-purple-500 {\n    --tw-gradient-from: var(--color-purple-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-purple-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-purple-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-purple-600 {\n    --tw-gradient-from: var(--color-purple-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-purple-600\\/8 {\n    --tw-gradient-from: color-mix(in srgb, oklch(55.8% 0.288 302.321) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-purple-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-500 {\n    --tw-gradient-from: var(--color-red-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-red-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-500\\/30 {\n    --tw-gradient-from: color-mix(in srgb, oklch(63.7% 0.237 25.331) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-red-500) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-600 {\n    --tw-gradient-from: var(--color-red-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-600\\/8 {\n    --tw-gradient-from: color-mix(in srgb, oklch(57.7% 0.245 27.325) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-red-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-600\\/10 {\n    --tw-gradient-from: color-mix(in srgb, oklch(57.7% 0.245 27.325) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-red-600) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-600\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(57.7% 0.245 27.325) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-red-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-900\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-900\\/30 {\n    --tw-gradient-from: color-mix(in srgb, oklch(39.6% 0.141 25.723) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-transparent {\n    --tw-gradient-from: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-white {\n    --tw-gradient-from: var(--color-white);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-400 {\n    --tw-gradient-from: var(--color-yellow-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-500 {\n    --tw-gradient-from: var(--color-yellow-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(79.5% 0.184 86.047) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-black {\n    --tw-gradient-via: var(--color-black);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/20 {\n    --tw-gradient-via: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/40 {\n    --tw-gradient-via: color-mix(in srgb, #000 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 40%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/50 {\n    --tw-gradient-via: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/60 {\n    --tw-gradient-via: color-mix(in srgb, #000 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 60%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/70 {\n    --tw-gradient-via: color-mix(in srgb, #000 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 70%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/80 {\n    --tw-gradient-via: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/90 {\n    --tw-gradient-via: color-mix(in srgb, #000 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 90%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-gray-200 {\n    --tw-gradient-via: var(--color-gray-200);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-gray-700 {\n    --tw-gradient-via: var(--color-gray-700);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-gray-900 {\n    --tw-gradient-via: var(--color-gray-900);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-gray-900\\/30 {\n    --tw-gradient-via: color-mix(in srgb, oklch(21% 0.034 264.665) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-gray-900) 30%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-gray-900\\/50 {\n    --tw-gradient-via: color-mix(in srgb, oklch(21% 0.034 264.665) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-gray-900) 50%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-orange-200 {\n    --tw-gradient-via: var(--color-orange-200);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-purple-200 {\n    --tw-gradient-via: var(--color-purple-200);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-purple-600\\/20 {\n    --tw-gradient-via: color-mix(in srgb, oklch(55.8% 0.288 302.321) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-purple-600) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-red-200 {\n    --tw-gradient-via: var(--color-red-200);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-red-500 {\n    --tw-gradient-via: var(--color-red-500);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-transparent {\n    --tw-gradient-via: transparent;\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white\\/20 {\n    --tw-gradient-via: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-amber-600 {\n    --tw-gradient-to: var(--color-amber-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-black {\n    --tw-gradient-to: var(--color-black);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-black\\/20 {\n    --tw-gradient-to: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-black\\/30 {\n    --tw-gradient-to: color-mix(in srgb, #000 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-black) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-black\\/40 {\n    --tw-gradient-to: color-mix(in srgb, #000 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-black) 40%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-black\\/80 {\n    --tw-gradient-to: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-black\\/90 {\n    --tw-gradient-to: color-mix(in srgb, #000 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-black) 90%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-600 {\n    --tw-gradient-to: var(--color-blue-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-600\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(54.6% 0.245 262.881) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-blue-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-700 {\n    --tw-gradient-to: var(--color-blue-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-800\\/30 {\n    --tw-gradient-to: color-mix(in srgb, oklch(42.4% 0.199 265.638) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-blue-800) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-cyan-600 {\n    --tw-gradient-to: var(--color-cyan-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-cyan-600\\/8 {\n    --tw-gradient-to: color-mix(in srgb, oklch(60.9% 0.126 221.723) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-cyan-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-cyan-600\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(60.9% 0.126 221.723) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-cyan-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-500 {\n    --tw-gradient-to: var(--color-emerald-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-600\\/8 {\n    --tw-gradient-to: color-mix(in srgb, oklch(59.6% 0.145 163.225) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-emerald-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-400 {\n    --tw-gradient-to: var(--color-gray-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-600 {\n    --tw-gradient-to: var(--color-gray-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-700\\/50 {\n    --tw-gradient-to: color-mix(in srgb, oklch(37.3% 0.034 259.733) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-gray-700) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-800 {\n    --tw-gradient-to: var(--color-gray-800);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-800\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(27.8% 0.033 256.848) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-gray-800) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-800\\/50 {\n    --tw-gradient-to: color-mix(in srgb, oklch(27.8% 0.033 256.848) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-gray-800) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-800\\/95 {\n    --tw-gradient-to: color-mix(in srgb, oklch(27.8% 0.033 256.848) 95%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-gray-800) 95%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-900 {\n    --tw-gradient-to: var(--color-gray-900);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-900\\/50 {\n    --tw-gradient-to: color-mix(in srgb, oklch(21% 0.034 264.665) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-gray-900) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-600 {\n    --tw-gradient-to: var(--color-green-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-600\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(62.7% 0.194 149.214) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-green-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-700 {\n    --tw-gradient-to: var(--color-green-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-800\\/30 {\n    --tw-gradient-to: color-mix(in srgb, oklch(44.8% 0.119 151.328) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-green-800) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-500 {\n    --tw-gradient-to: var(--color-orange-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-orange-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-600 {\n    --tw-gradient-to: var(--color-orange-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-600\\/8 {\n    --tw-gradient-to: color-mix(in srgb, oklch(64.6% 0.222 41.116) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-orange-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-600\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(64.6% 0.222 41.116) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-orange-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-pink-600 {\n    --tw-gradient-to: var(--color-pink-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-pink-600\\/8 {\n    --tw-gradient-to: color-mix(in srgb, oklch(59.2% 0.249 0.584) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-pink-600) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-pink-600\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(59.2% 0.249 0.584) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-pink-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-500\\/50 {\n    --tw-gradient-to: color-mix(in srgb, oklch(62.7% 0.265 303.9) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-600 {\n    --tw-gradient-to: var(--color-purple-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-600\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(55.8% 0.288 302.321) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-purple-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-700 {\n    --tw-gradient-to: var(--color-purple-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-600 {\n    --tw-gradient-to: var(--color-red-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-600\\/5 {\n    --tw-gradient-to: color-mix(in srgb, oklch(57.7% 0.245 27.325) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-600\\/10 {\n    --tw-gradient-to: color-mix(in srgb, oklch(57.7% 0.245 27.325) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-600\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(57.7% 0.245 27.325) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-600\\/30 {\n    --tw-gradient-to: color-mix(in srgb, oklch(57.7% 0.245 27.325) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-700 {\n    --tw-gradient-to: var(--color-red-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-700\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(50.5% 0.213 27.518) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-700) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-800\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(44.4% 0.177 26.899) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-800) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-800\\/30 {\n    --tw-gradient-to: color-mix(in srgb, oklch(44.4% 0.177 26.899) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-800) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-teal-600 {\n    --tw-gradient-to: var(--color-teal-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-transparent {\n    --tw-gradient-to: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-yellow-600 {\n    --tw-gradient-to: var(--color-yellow-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .bg-\\[length\\:50px_50px\\] {\n    background-size: 50px 50px;\n  }\n  .bg-clip-text {\n    background-clip: text;\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .object-center {\n    object-position: center;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-5 {\n    padding: calc(var(--spacing) * 5);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .py-20 {\n    padding-block: calc(var(--spacing) * 20);\n  }\n  .py-24 {\n    padding-block: calc(var(--spacing) * 24);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-6 {\n    padding-top: calc(var(--spacing) * 6);\n  }\n  .pt-24 {\n    padding-top: calc(var(--spacing) * 24);\n  }\n  .pr-4 {\n    padding-right: calc(var(--spacing) * 4);\n  }\n  .pr-20 {\n    padding-right: calc(var(--spacing) * 20);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pb-6 {\n    padding-bottom: calc(var(--spacing) * 6);\n  }\n  .pb-16 {\n    padding-bottom: calc(var(--spacing) * 16);\n  }\n  .pb-40 {\n    padding-bottom: calc(var(--spacing) * 40);\n  }\n  .pl-4 {\n    padding-left: calc(var(--spacing) * 4);\n  }\n  .pl-12 {\n    padding-left: calc(var(--spacing) * 12);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .font-mono {\n    font-family: var(--font-mono);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-5xl {\n    font-size: var(--text-5xl);\n    line-height: var(--tw-leading, var(--text-5xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-black {\n    --tw-font-weight: var(--font-weight-black);\n    font-weight: var(--font-weight-black);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-light {\n    --tw-font-weight: var(--font-weight-light);\n    font-weight: var(--font-weight-light);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .tracking-wide {\n    --tw-tracking: var(--tracking-wide);\n    letter-spacing: var(--tracking-wide);\n  }\n  .tracking-wider {\n    --tw-tracking: var(--tracking-wider);\n    letter-spacing: var(--tracking-wider);\n  }\n  .text-amber-400 {\n    color: var(--color-amber-400);\n  }\n  .text-black {\n    color: var(--color-black);\n  }\n  .text-blue-300 {\n    color: var(--color-blue-300);\n  }\n  .text-blue-400 {\n    color: var(--color-blue-400);\n  }\n  .text-blue-500 {\n    color: var(--color-blue-500);\n  }\n  .text-gray-200 {\n    color: var(--color-gray-200);\n  }\n  .text-gray-300 {\n    color: var(--color-gray-300);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-green-300 {\n    color: var(--color-green-300);\n  }\n  .text-green-400 {\n    color: var(--color-green-400);\n  }\n  .text-indigo-400 {\n    color: var(--color-indigo-400);\n  }\n  .text-orange-400 {\n    color: var(--color-orange-400);\n  }\n  .text-purple-400 {\n    color: var(--color-purple-400);\n  }\n  .text-red-200 {\n    color: var(--color-red-200);\n  }\n  .text-red-300 {\n    color: var(--color-red-300);\n  }\n  .text-red-400 {\n    color: var(--color-red-400);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-transparent {\n    color: transparent;\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-white\\/30 {\n    color: color-mix(in srgb, #fff 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 30%, transparent);\n    }\n  }\n  .text-white\\/50 {\n    color: color-mix(in srgb, #fff 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 50%, transparent);\n    }\n  }\n  .text-white\\/60 {\n    color: color-mix(in srgb, #fff 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 60%, transparent);\n    }\n  }\n  .text-white\\/70 {\n    color: color-mix(in srgb, #fff 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 70%, transparent);\n    }\n  }\n  .text-yellow-300 {\n    color: var(--color-yellow-300);\n  }\n  .text-yellow-400 {\n    color: var(--color-yellow-400);\n  }\n  .text-yellow-500 {\n    color: var(--color-yellow-500);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .uppercase {\n    text-transform: uppercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .placeholder-gray-400 {\n    &::placeholder {\n      color: var(--color-gray-400);\n    }\n  }\n  .placeholder-gray-500 {\n    &::placeholder {\n      color: var(--color-gray-500);\n    }\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-5 {\n    opacity: 5%;\n  }\n  .opacity-30 {\n    opacity: 30%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-60 {\n    opacity: 60%;\n  }\n  .opacity-75 {\n    opacity: 75%;\n  }\n  .opacity-80 {\n    opacity: 80%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[inset_0_0_20px_rgba\\(239\\,68\\,68\\,0\\.1\\)\\] {\n    --tw-shadow: inset 0 0 20px var(--tw-shadow-color, rgba(239,68,68,0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-red-500\\/20 {\n    --tw-shadow-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 20%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-red-500\\/30 {\n    --tw-shadow-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-red-500\\/50 {\n    --tw-shadow-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 50%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-2xl {\n    --tw-blur: blur(var(--blur-2xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-3xl {\n    --tw-blur: blur(var(--blur-3xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-lg {\n    --tw-blur: blur(var(--blur-lg));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-xl {\n    --tw-blur: blur(var(--blur-xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur {\n    --tw-backdrop-blur: blur(8px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-2xl {\n    --tw-backdrop-blur: blur(var(--blur-2xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xl {\n    --tw-backdrop-blur: blur(var(--blur-xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-500 {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n  .duration-1000 {\n    --tw-duration: 1000ms;\n    transition-duration: 1000ms;\n  }\n  .group-hover\\:ml-auto {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        margin-left: auto;\n      }\n    }\n  }\n  .group-hover\\:w-3\\/4 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        width: calc(3/4 * 100%);\n      }\n    }\n  }\n  .group-hover\\:translate-x-1 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-translate-x: calc(var(--spacing) * 1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-hover\\:scale-105 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-hover\\:scale-110 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-hover\\:bg-black\\/20 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #000 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-black) 20%, transparent);\n        }\n      }\n    }\n  }\n  .group-hover\\:bg-gray-700\\/50 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-700) 50%, transparent);\n        }\n      }\n    }\n  }\n  .group-hover\\:bg-white\\/20 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n        }\n      }\n    }\n  }\n  .group-hover\\:text-gray-400 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: var(--color-gray-400);\n      }\n    }\n  }\n  .group-hover\\:text-red-400 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: var(--color-red-400);\n      }\n    }\n  }\n  .group-hover\\:text-white {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\:shadow-\\[inset_0_0_20px_rgba\\(255\\,255\\,255\\,0\\.05\\)\\] {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-shadow: inset 0 0 20px var(--tw-shadow-color, rgba(255,255,255,0.05));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .group-hover\\:shadow-red-500\\/25 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-shadow-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 25%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 25%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .group-hover\\:blur-2xl {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-blur: blur(var(--blur-2xl));\n        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n      }\n    }\n  }\n  .last\\:border-b-0 {\n    &:last-child {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 0px;\n    }\n  }\n  .hover\\:translate-x-1 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-x: calc(var(--spacing) * 1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:scale-105 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:scale-110 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:scale-\\[1\\.01\\] {\n    &:hover {\n      @media (hover: hover) {\n        scale: 1.01;\n      }\n    }\n  }\n  .hover\\:border-gray-600 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-gray-600);\n      }\n    }\n  }\n  .hover\\:border-gray-600\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-gray-600) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-orange-500\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-orange-500) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-red-500\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-red-500\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-red-500\\/70 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 70%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-red-500) 70%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-white\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, #fff 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-white\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, #fff 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-white) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-white\\/40 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, #fff 40%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-white) 40%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-black {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-black);\n      }\n    }\n  }\n  .hover\\:bg-black\\/60 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #000 60%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-black) 60%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-black\\/70 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #000 70%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-black) 70%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-black\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #000 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-black) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-blue-500\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-200);\n      }\n    }\n  }\n  .hover\\:bg-gray-400 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-400);\n      }\n    }\n  }\n  .hover\\:bg-gray-600\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-600) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-700);\n      }\n    }\n  }\n  .hover\\:bg-gray-700\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-700) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-700\\/60 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 60%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-700) 60%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-700\\/70 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 70%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-700) 70%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-800\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-800) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-800\\/40 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 40%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-800) 40%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-800\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-800\\/70 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 70%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-800) 70%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-900\\/60 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 60%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-900) 60%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-green-500\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-green-500) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-red-500 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-500);\n      }\n    }\n  }\n  .hover\\:bg-red-500\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-red-500\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-red-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-600);\n      }\n    }\n  }\n  .hover\\:bg-red-600\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(57.7% 0.245 27.325) 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-red-600) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-700);\n      }\n    }\n  }\n  .hover\\:bg-white\\/5 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 5%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 5%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-white\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 10%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-white\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-white\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-white\\/60 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 60%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 60%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:from-gray-700\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: color-mix(in srgb, oklch(37.3% 0.034 259.733) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--color-gray-700) 50%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-orange-500\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 30%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-red-500 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-red-500);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-red-500\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: color-mix(in srgb, oklch(63.7% 0.237 25.331) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--color-red-500) 30%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-red-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-red-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-red-700);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-gray-600\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: color-mix(in srgb, oklch(44.6% 0.03 256.802) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-to: color-mix(in oklab, var(--color-gray-600) 50%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-red-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-red-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-red-600\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: color-mix(in srgb, oklch(57.7% 0.245 27.325) 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 20%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-red-600\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: color-mix(in srgb, oklch(57.7% 0.245 27.325) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 30%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-red-700);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-red-800 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-red-800);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:text-blue-300 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-300);\n      }\n    }\n  }\n  .hover\\:text-gray-300 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-300);\n      }\n    }\n  }\n  .hover\\:text-green-300 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-green-300);\n      }\n    }\n  }\n  .hover\\:text-orange-300 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-orange-300);\n      }\n    }\n  }\n  .hover\\:text-red-300 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-300);\n      }\n    }\n  }\n  .hover\\:text-red-500 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-500);\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-2xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-red-500\\/25 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 25%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 25%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .hover\\:shadow-red-500\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 50%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .focus\\:border-red-500 {\n    &:focus {\n      border-color: var(--color-red-500);\n    }\n  }\n  .focus\\:border-red-500\\/50 {\n    &:focus {\n      border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:border-transparent {\n    &:focus {\n      border-color: transparent;\n    }\n  }\n  .focus\\:bg-black\\/70 {\n    &:focus {\n      background-color: color-mix(in srgb, #000 70%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-black) 70%, transparent);\n      }\n    }\n  }\n  .focus\\:bg-gray-900\\/70 {\n    &:focus {\n      background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 70%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-gray-900) 70%, transparent);\n      }\n    }\n  }\n  .focus\\:bg-white\\/20 {\n    &:focus {\n      background-color: color-mix(in srgb, #fff 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n      }\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-blue-500 {\n    &:focus {\n      --tw-ring-color: var(--color-blue-500);\n    }\n  }\n  .focus\\:ring-gray-500 {\n    &:focus {\n      --tw-ring-color: var(--color-gray-500);\n    }\n  }\n  .focus\\:ring-red-500 {\n    &:focus {\n      --tw-ring-color: var(--color-red-500);\n    }\n  }\n  .focus\\:ring-red-500\\/50 {\n    &:focus {\n      --tw-ring-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:ring-white\\/20 {\n    &:focus {\n      --tw-ring-color: color-mix(in srgb, #fff 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n      }\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:bg-gray-300 {\n    &:active {\n      background-color: var(--color-gray-300);\n    }\n  }\n  .active\\:bg-white\\/20 {\n    &:active {\n      background-color: color-mix(in srgb, #fff 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n      }\n    }\n  }\n  .active\\:bg-white\\/30 {\n    &:active {\n      background-color: color-mix(in srgb, #fff 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-white) 30%, transparent);\n      }\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:bg-gray-600 {\n    &:disabled {\n      background-color: var(--color-gray-600);\n    }\n  }\n  .disabled\\:from-gray-700 {\n    &:disabled {\n      --tw-gradient-from: var(--color-gray-700);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .disabled\\:to-gray-800 {\n    &:disabled {\n      --tw-gradient-to: var(--color-gray-800);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .disabled\\:opacity-40 {\n    &:disabled {\n      opacity: 40%;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .disabled\\:shadow-none {\n    &:disabled {\n      --tw-shadow: 0 0 #0000;\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .disabled\\:hover\\:border-gray-600\\/50 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          border-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 50%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            border-color: color-mix(in oklab, var(--color-gray-600) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .disabled\\:hover\\:border-red-500\\/50 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          border-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .disabled\\:hover\\:bg-gray-800\\/50 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 50%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .disabled\\:hover\\:from-red-500\\/20 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          --tw-gradient-from: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            --tw-gradient-from: color-mix(in oklab, var(--color-red-500) 20%, transparent);\n          }\n          --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n        }\n      }\n    }\n  }\n  .disabled\\:hover\\:to-red-600\\/10 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          --tw-gradient-to: color-mix(in srgb, oklch(57.7% 0.245 27.325) 10%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 10%, transparent);\n          }\n          --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n        }\n      }\n    }\n  }\n  .disabled\\:hover\\:text-gray-300 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--color-gray-300);\n        }\n      }\n    }\n  }\n  .sm\\:block {\n    @media (width >= 40rem) {\n      display: block;\n    }\n  }\n  .sm\\:flex {\n    @media (width >= 40rem) {\n      display: flex;\n    }\n  }\n  .sm\\:grid-cols-2 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .sm\\:grid-cols-3 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:items-center {\n    @media (width >= 40rem) {\n      align-items: center;\n    }\n  }\n  .sm\\:space-y-0 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-y-reverse: 0;\n        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));\n        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));\n      }\n    }\n  }\n  .sm\\:space-x-4 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .sm\\:space-x-6 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .md\\:text-3xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .md\\:text-4xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .md\\:text-6xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .md\\:text-7xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-7xl);\n      line-height: var(--tw-leading, var(--text-7xl--line-height));\n    }\n  }\n  .lg\\:col-span-1 {\n    @media (width >= 64rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .lg\\:col-span-3 {\n    @media (width >= 64rem) {\n      grid-column: span 3 / span 3;\n    }\n  }\n  .lg\\:block {\n    @media (width >= 64rem) {\n      display: block;\n    }\n  }\n  .lg\\:flex {\n    @media (width >= 64rem) {\n      display: flex;\n    }\n  }\n  .lg\\:hidden {\n    @media (width >= 64rem) {\n      display: none;\n    }\n  }\n  .lg\\:h-\\[85vh\\] {\n    @media (width >= 64rem) {\n      height: 85vh;\n    }\n  }\n  .lg\\:w-52 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 52);\n    }\n  }\n  .lg\\:w-56 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 56);\n    }\n  }\n  .lg\\:w-72 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 72);\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-5 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(5, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-6 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n  .lg\\:flex-row {\n    @media (width >= 64rem) {\n      flex-direction: row;\n    }\n  }\n  .lg\\:space-y-0 {\n    @media (width >= 64rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-y-reverse: 0;\n        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));\n        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));\n      }\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:px-12 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:px-24 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 24);\n    }\n  }\n  .lg\\:py-24 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 24);\n    }\n  }\n  .lg\\:text-2xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .lg\\:text-3xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .lg\\:text-4xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .lg\\:text-5xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .lg\\:text-6xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .lg\\:text-7xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-7xl);\n      line-height: var(--tw-leading, var(--text-7xl--line-height));\n    }\n  }\n  .lg\\:text-8xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-8xl);\n      line-height: var(--tw-leading, var(--text-8xl--line-height));\n    }\n  }\n  .xl\\:col-span-1 {\n    @media (width >= 80rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .xl\\:col-span-2 {\n    @media (width >= 80rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .xl\\:col-span-3 {\n    @media (width >= 80rem) {\n      grid-column: span 3 / span 3;\n    }\n  }\n  .xl\\:grid-cols-3 {\n    @media (width >= 80rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .xl\\:grid-cols-5 {\n    @media (width >= 80rem) {\n      grid-template-columns: repeat(5, minmax(0, 1fr));\n    }\n  }\n  .xl\\:grid-cols-6 {\n    @media (width >= 80rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n  .xl\\:text-8xl {\n    @media (width >= 80rem) {\n      font-size: var(--text-8xl);\n      line-height: var(--tw-leading, var(--text-8xl--line-height));\n    }\n  }\n  .\\32 xl\\:grid-cols-8 {\n    @media (width >= 96rem) {\n      grid-template-columns: repeat(8, minmax(0, 1fr));\n    }\n  }\n}\n:root {\n  --background: #000000;\n  --foreground: #ffffff;\n  --surface: #0a0a0a;\n  --surface-elevated: #141414;\n  --surface-glass: rgba(20, 20, 20, 0.8);\n  --border: rgba(255, 255, 255, 0.08);\n  --border-hover: rgba(255, 255, 255, 0.16);\n  --text-primary: #ffffff;\n  --text-secondary: #b3b3b3;\n  --text-muted: #737373;\n  --accent: #2a2a2a;\n  --accent-hover: #3a3a3a;\n  --accent-light: #4a4a4a;\n  --accent-dark: #1a1a1a;\n  --success: #30d158;\n  --warning: #ff9f0a;\n  --error: #ff453a;\n}\n* {\n  box-sizing: border-box;\n}\nhtml {\n  scroll-behavior: smooth;\n}\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n  line-height: 1.6;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n.custom-scrollbar::-webkit-scrollbar {\n  width: 8px;\n}\n.custom-scrollbar::-webkit-scrollbar-track {\n  background: rgba(55, 65, 81, 0.3);\n  border-radius: 4px;\n}\n.custom-scrollbar::-webkit-scrollbar-thumb {\n  background: rgba(107, 114, 128, 0.6);\n  border-radius: 4px;\n}\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\n  background: rgba(107, 114, 128, 0.8);\n}\n::-webkit-scrollbar-track {\n  background: transparent;\n}\n::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 3px;\n  transition: background 0.2s ease;\n}\n::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.4);\n}\n::-webkit-scrollbar-corner {\n  background: transparent;\n}\n.glass {\n  background: var(--surface-glass);\n  backdrop-filter: blur(20px) saturate(180%);\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\n  border: 1px solid var(--border);\n}\n.glass-elevated {\n  background: rgba(30, 30, 30, 0.9);\n  backdrop-filter: blur(40px) saturate(200%);\n  -webkit-backdrop-filter: blur(40px) saturate(200%);\n  border: 1px solid var(--border-hover);\n}\n.animate-fade-in {\n  animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);\n}\n.animate-slide-up {\n  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);\n}\n.animate-scale-in {\n  animation: scaleIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@keyframes scaleIn {\n  from {\n    opacity: 0;\n    transform: scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n.hover-lift {\n  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);\n}\n.hover-lift:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);\n}\n.hover-glow {\n  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);\n}\n.hover-glow:hover {\n  box-shadow: 0 0 30px rgba(0, 113, 227, 0.3);\n}\n.scrollbar-hide {\n  -ms-overflow-style: none;\n  scrollbar-width: none;\n}\n.scrollbar-hide::-webkit-scrollbar {\n  display: none;\n}\n.line-clamp-1 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.line-clamp-3 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\n.focus-ring {\n  outline: none;\n  transition: box-shadow 0.2s ease;\n}\n.focus-ring:focus-visible {\n  box-shadow: 0 0 0 3px rgba(74, 74, 74, 0.5);\n}\n.shimmer {\n  background: linear-gradient(90deg,\n    rgba(255, 255, 255, 0.05) 25%,\n    rgba(255, 255, 255, 0.1) 50%,\n    rgba(255, 255, 255, 0.05) 75%\n  );\n  background-size: 200% 100%;\n  animation: shimmer 2s infinite;\n}\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n.bg-gradient-radial {\n  background: radial-gradient(circle, var(--tw-gradient-stops));\n}\n.bg-gradient-conic {\n  background: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));\n}\n.card-hover {\n  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);\n}\n.card-hover:hover {\n  transform: translateY(-12px) scale(1.03);\n  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.8),\n    0 0 40px rgba(74, 74, 74, 0.25),\n    0 0 80px rgba(42, 42, 42, 0.15);\n}\n.page-transition {\n  animation: pageEnter 0.6s cubic-bezier(0.16, 1, 0.3, 1);\n}\n@keyframes pageEnter {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes ping {\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EA4nJE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA5nJJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EAmIE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AA/QF;;AAAA;EAoRE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;;EAKA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAKA;;;;;;EAKA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAKE;;;;EAKA;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAMI;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;;EAOzB;;;;;EAOE;IAAuB;;;;;;EAQvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;;EAQvB;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;;EAQvB;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAOpD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAQI;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;IAErB;MAAgD;;;;;;EAWlD;IAAuB;;;;;IAErB;MAAgD;;;;;;EAWlD;IAAuB;;;;;EAO3B;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;AAK7B;;;;;;;;;;;;;;;;;;;;AAmBA;;;;AAGA;;;;AAGA;;;;;;;;;AAQA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;;AASA;;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAMA;;;;AAGA;;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA", "debugId": null}}]}