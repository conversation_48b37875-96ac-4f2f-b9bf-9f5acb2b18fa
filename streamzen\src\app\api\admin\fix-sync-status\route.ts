import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';

/**
 * Fix corrupted sync status records
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Fixing sync status records...');
    
    await connectDB();
    const { default: SyncStatus } = await import('@/models/SyncStatus');
    
    // Find all sync status records
    const allSyncStatuses = await SyncStatus.find({});
    console.log(`📊 Found ${allSyncStatuses.length} sync status records`);
    
    let fixedCount = 0;
    const now = new Date();
    
    for (const status of allSyncStatuses) {
      let needsUpdate = false;
      const updateData: any = {};
      
      // Fix missing syncType
      if (!status.syncType) {
        updateData.syncType = 'LEGACY_SYNC';
        needsUpdate = true;
        console.log(`🔧 Adding missing syncType to record ${status._id}`);
      }
      
      // Fix missing or invalid nextSyncTime
      if (!status.nextSyncTime || isNaN(new Date(status.nextSyncTime).getTime())) {
        // Calculate next sync time based on sync type
        let nextSync;
        if (status.syncType === 'UNIVERSAL_SYNC') {
          // Next 3-hour UTC interval
          const currentHour = now.getUTCHours();
          const nextSyncHour = Math.ceil((currentHour + 1) / 3) * 3;
          nextSync = new Date(now);
          nextSync.setUTCHours(nextSyncHour % 24, 0, 0, 0);
          if (nextSyncHour >= 24) {
            nextSync.setUTCDate(nextSync.getUTCDate() + 1);
          }
        } else {
          // Default: 3 hours from now
          nextSync = new Date(now.getTime() + 3 * 60 * 60 * 1000);
        }
        
        updateData.nextSyncTime = nextSync;
        needsUpdate = true;
        console.log(`🔧 Fixing nextSyncTime for ${status.syncType}: ${nextSync.toISOString()}`);
      }
      
      // Fix missing lastSyncTime (set to epoch if null)
      if (!status.lastSyncTime) {
        updateData.lastSyncTime = new Date(0);
        needsUpdate = true;
        console.log(`🔧 Setting lastSyncTime to epoch for ${status.syncType}`);
      }
      
      // Ensure isRunning is boolean
      if (typeof status.isRunning !== 'boolean') {
        updateData.isRunning = false;
        needsUpdate = true;
        console.log(`🔧 Fixing isRunning for ${status.syncType}`);
      }
      
      if (needsUpdate) {
        await SyncStatus.updateOne({ _id: status._id }, updateData);
        fixedCount++;
      }
    }
    
    // Clean up any duplicate UNIVERSAL_SYNC records (keep only the latest)
    const universalSyncs = await SyncStatus.find({ syncType: 'UNIVERSAL_SYNC' }).sort({ createdAt: -1 });
    if (universalSyncs.length > 1) {
      console.log(`🧹 Found ${universalSyncs.length} UNIVERSAL_SYNC records, keeping only the latest`);
      
      // Keep the first (latest) and delete the rest
      const toDelete = universalSyncs.slice(1);
      for (const duplicate of toDelete) {
        await SyncStatus.deleteOne({ _id: duplicate._id });
        console.log(`🗑️ Deleted duplicate UNIVERSAL_SYNC record ${duplicate._id}`);
      }
    }
    
    // Get final status
    const finalStatuses = await SyncStatus.find({}).sort({ syncType: 1 });
    
    console.log('✅ Sync status fix completed');
    
    return NextResponse.json({
      success: true,
      message: 'Sync status records fixed successfully',
      stats: {
        totalRecords: allSyncStatuses.length,
        recordsFixed: fixedCount,
        finalRecords: finalStatuses.length
      },
      syncStatuses: finalStatuses.map(s => ({
        syncType: s.syncType,
        lastSyncTime: s.lastSyncTime,
        nextSyncTime: s.nextSyncTime,
        isRunning: s.isRunning
      }))
    });

  } catch (error) {
    console.error('❌ Error fixing sync status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fix sync status',
        message: error.message
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Check current sync status records
 */
export async function GET(request: NextRequest) {
  try {
    await connectDB();
    const { default: SyncStatus } = await import('@/models/SyncStatus');
    
    const allSyncStatuses = await SyncStatus.find({}).sort({ syncType: 1 });
    
    return NextResponse.json({
      success: true,
      count: allSyncStatuses.length,
      syncStatuses: allSyncStatuses.map(s => ({
        id: s._id,
        syncType: s.syncType,
        lastSyncTime: s.lastSyncTime,
        nextSyncTime: s.nextSyncTime,
        isRunning: s.isRunning,
        createdAt: s.createdAt,
        updatedAt: s.updatedAt
      }))
    });

  } catch (error) {
    console.error('❌ Error getting sync status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get sync status',
        message: error.message
      },
      { status: 500 }
    );
  }
}
