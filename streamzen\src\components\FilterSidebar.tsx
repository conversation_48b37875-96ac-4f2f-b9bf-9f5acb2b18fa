'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Filter, X, ChevronDown, ChevronUp } from 'lucide-react';
import { apiClient, FilterOptions } from '@/lib/api';

interface FilterSidebarProps {
  currentFilters: Record<string, string | undefined>;
  basePath: string;
  contentType: 'movies' | 'series' | 'episodes';
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({
  currentFilters,
  basePath,
  contentType
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // State for filter options
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null);
  const [loading, setLoading] = useState(true);

  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    sort: true,
    genre: true,
    language: false,
    country: false,
    year: false,
    rating: false,
    quality: contentType === 'movies' || contentType === 'episodes'
  });

  // Static sort options
  const sortOptions = [
    { value: 'createdAt', label: 'Recently Added' },
    { value: 'title', label: 'Title' },
    { value: 'year', label: contentType === 'movies' ? 'Release Year' : 'Start Year' },
    { value: 'imdbRating', label: 'IMDb Rating' },
    { value: 'popularity', label: 'Popularity' }
  ];

  // Fetch filter options
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        setLoading(true);
        const options = contentType === 'movies'
          ? await apiClient.getMovieFilterOptions()
          : contentType === 'series'
          ? await apiClient.getSeriesFilterOptions()
          : await apiClient.getEpisodeFilterOptions();
        setFilterOptions(options);
      } catch (error) {
        console.error('Failed to fetch filter options:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFilterOptions();
  }, [contentType]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const updateFilter = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString());

    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }

    // Reset to page 1 when filters change
    params.delete('page');

    router.push(`${basePath}?${params.toString()}`);
  };

  const clearAllFilters = () => {
    router.push(basePath);
  };

  const hasActiveFilters = Object.entries(currentFilters).some(
    ([key, value]) => value && key !== 'page' && key !== 'sortBy' && key !== 'sortOrder'
  );

  const FilterSection: React.FC<{
    title: string;
    sectionKey: string;
    children: React.ReactNode;
  }> = ({ title, sectionKey, children }) => (
    <div className="border-b border-gray-700/50 pb-6 mb-6 last:border-b-0">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="flex items-center justify-between w-full text-left text-white font-semibold mb-4 hover:text-gray-300 transition-all duration-300 p-3 rounded-xl hover:bg-gray-800/50 group"
      >
        <span className="text-lg">{title}</span>
        {expandedSections[sectionKey] ? (
          <ChevronUp size={18} className="text-gray-400 group-hover:text-white transition-colors duration-300" />
        ) : (
          <ChevronDown size={18} className="text-gray-400 group-hover:text-white transition-colors duration-300" />
        )}
      </button>
      {expandedSections[sectionKey] && (
        <div className="space-y-2 animate-fade-in">
          {children}
        </div>
      )}
    </div>
  );

  return (
    <div className="glass-elevated rounded-2xl border border-gray-700/50 p-8 backdrop-blur-sm">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gray-800/50 rounded-xl flex items-center justify-center">
            <Filter size={20} className="text-white" />
          </div>
          <h3 className="text-white font-bold text-xl">Filters</h3>
        </div>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-gray-400 hover:text-white transition-all duration-300 p-2 rounded-xl hover:bg-gray-800/50 group"
            title="Clear all filters"
          >
            <X size={18} className="group-hover:scale-110 transition-transform duration-300" />
          </button>
        )}
      </div>

      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      )}

      {!loading && (
        <>
          <FilterSection title="Sort By" sectionKey="sort">
            {sortOptions.map((option) => (
              <label key={option.value} className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                <input
                  type="radio"
                  name="sortBy"
                  value={option.value}
                  checked={currentFilters.sortBy === option.value || (!currentFilters.sortBy && option.value === 'createdAt')}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                />
                <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">{option.label}</span>
              </label>
            ))}

            <div className="mt-6 pt-6 border-t border-gray-700/50">
              <label className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                <input
                  type="radio"
                  name="sortOrder"
                  value="desc"
                  checked={currentFilters.sortOrder === 'desc' || !currentFilters.sortOrder}
                  onChange={(e) => updateFilter('sortOrder', e.target.value)}
                  className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                />
                <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">Newest First</span>
              </label>
              <label className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                <input
                  type="radio"
                  name="sortOrder"
                  value="asc"
                  checked={currentFilters.sortOrder === 'asc'}
                  onChange={(e) => updateFilter('sortOrder', e.target.value)}
                  className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                />
                <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">Oldest First</span>
              </label>
            </div>
          </FilterSection>

          {/* Genres with Counts */}
          {filterOptions?.genres && filterOptions.genres.length > 0 && (
            <FilterSection title="Genre" sectionKey="genre">
              <div className="max-h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                {filterOptions.genres.map((genreItem) => (
                  <label key={genreItem.genre} className="flex items-center justify-between cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                    <div className="flex items-center space-x-4">
                      <input
                        type="checkbox"
                        checked={currentFilters.genre === genreItem.genre}
                        onChange={(e) => updateFilter('genre', e.target.checked ? genreItem.genre : null)}
                        className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 rounded-md transition-all duration-300"
                      />
                      <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">
                        {genreItem.genre}
                      </span>
                    </div>
                    <span className="text-gray-500 text-sm font-medium bg-gray-800/50 px-2 py-1 rounded-lg">
                      {genreItem.count}
                    </span>
                  </label>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Languages */}
          {filterOptions?.languages && filterOptions.languages.length > 0 && (
            <FilterSection title="Language" sectionKey="language">
              <div className="max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                {/* Clear Language Option */}
                <label className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                  <input
                    type="radio"
                    name="language"
                    value=""
                    checked={!currentFilters.language}
                    onChange={(e) => updateFilter('language', null)}
                    className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                  />
                  <span className="text-gray-400 text-base font-medium group-hover:text-white transition-colors duration-300">All Languages</span>
                </label>
                {filterOptions.languages.map((languageItem) => (
                  <label key={languageItem.language} className="flex items-center justify-between cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                    <div className="flex items-center space-x-4">
                      <input
                        type="radio"
                        name="language"
                        value={languageItem.language}
                        checked={currentFilters.language === languageItem.language}
                        onChange={(e) => updateFilter('language', e.target.value)}
                        className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                      />
                      <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">{languageItem.language}</span>
                    </div>
                    <span className="text-gray-500 text-sm font-medium bg-gray-800/50 px-3 py-1 rounded-full group-hover:bg-gray-700/50 transition-all duration-300">
                      {languageItem.count}
                    </span>
                  </label>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Countries */}
          {filterOptions?.countries && filterOptions.countries.length > 0 && (
            <FilterSection title="Country" sectionKey="country">
              <div className="max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                {/* Clear Country Option */}
                <label className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                  <input
                    type="radio"
                    name="country"
                    value=""
                    checked={!currentFilters.country}
                    onChange={(e) => updateFilter('country', null)}
                    className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                  />
                  <span className="text-gray-400 text-base font-medium group-hover:text-white transition-colors duration-300">All Countries</span>
                </label>
                {filterOptions.countries.map((countryItem) => (
                  <label key={countryItem.country} className="flex items-center justify-between cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                    <div className="flex items-center space-x-4">
                      <input
                        type="radio"
                        name="country"
                        value={countryItem.country}
                        checked={currentFilters.country === countryItem.country}
                        onChange={(e) => updateFilter('country', e.target.value)}
                        className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                      />
                      <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">{countryItem.country}</span>
                    </div>
                    <span className="text-gray-500 text-sm font-medium bg-gray-800/50 px-3 py-1 rounded-full group-hover:bg-gray-700/50 transition-all duration-300">
                      {countryItem.count}
                    </span>
                  </label>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Years */}
          {filterOptions?.years && filterOptions.years.length > 0 && (
            <FilterSection title="Year" sectionKey="year">
              <div className="max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                {filterOptions.years.map((year) => (
                  <label key={year} className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                    <input
                      type="radio"
                      name="year"
                      value={year}
                      checked={currentFilters.year === year.toString()}
                      onChange={(e) => updateFilter('year', e.target.checked ? year.toString() : null)}
                      className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                    />
                    <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">{year}</span>
                  </label>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Ratings */}
          {filterOptions?.ratings && filterOptions.ratings.length > 0 && (
            <FilterSection title="Rating" sectionKey="rating">
              <div className="max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                {filterOptions.ratings.map((rating) => (
                  <label key={rating} className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                    <input
                      type="radio"
                      name="rating"
                      value={rating}
                      checked={currentFilters.rating === rating}
                      onChange={(e) => updateFilter('rating', e.target.checked ? rating : null)}
                      className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                    />
                    <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">{rating}</span>
                  </label>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Quality */}
          {filterOptions?.qualities && filterOptions.qualities.length > 0 && (
            <FilterSection title="Quality" sectionKey="quality">
              <div className="max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                {filterOptions.qualities.map((quality) => (
                  <label key={quality} className="flex items-center space-x-4 cursor-pointer p-3 rounded-xl hover:bg-gray-800/30 transition-all duration-300 group">
                    <input
                      type="radio"
                      name="quality"
                      value={quality}
                      checked={currentFilters.quality === quality}
                      onChange={(e) => updateFilter('quality', e.target.checked ? quality : null)}
                      className="w-5 h-5 text-gray-600 bg-gray-800 border-gray-600 focus:ring-gray-500 focus:ring-2 transition-all duration-300"
                    />
                    <span className="text-gray-300 text-base font-medium group-hover:text-white transition-colors duration-300">{quality}</span>
                  </label>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="pt-6 border-t border-gray-800">
              <button
                onClick={clearAllFilters}
                className="w-full bg-red-600 hover:bg-red-500 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FilterSidebar;