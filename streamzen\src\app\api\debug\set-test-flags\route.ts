import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * Set some test isLatestRelease flags to verify the pages work
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();

    console.log('🔧 Setting test isLatestRelease flags...');

    // Find 20 series with episodes and set their latest episodes as latest releases
    const seriesWithEpisodes = await Series.find({
      'episodes.0': { $exists: true }
    }).limit(20);

    let updatedCount = 0;
    let episodeCount = 0;

    for (const series of seriesWithEpisodes) {
      if (series.episodes && series.episodes.length > 0) {
        // Set the first episode as latest release for testing
        const firstEpisode = series.episodes[0];
        firstEpisode.isLatestRelease = true;
        firstEpisode.updatedAt = new Date();
        
        await series.save();
        updatedCount++;
        episodeCount++;
        
        console.log(`✅ Set test flag for: ${series.title} - S${firstEpisode.season}E${firstEpisode.episode}`);
      }
    }

    // Verify the flags were set
    const verifyCount = await Series.countDocuments({
      'episodes.isLatestRelease': true
    });

    console.log(`✅ Set ${episodeCount} test episodes as latest releases`);

    return NextResponse.json({
      success: true,
      message: 'Set test isLatestRelease flags',
      stats: {
        seriesUpdated: updatedCount,
        episodesMarked: episodeCount,
        verificationCount: verifyCount
      }
    });

  } catch (error) {
    console.error('❌ Set test flags error:', error);
    return NextResponse.json(
      { error: 'Failed to set test flags', message: error.message },
      { status: 500 }
    );
  }
}
