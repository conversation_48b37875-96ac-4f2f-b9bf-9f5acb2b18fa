import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import IMDbEpisodeScraper from '@/lib/imdbEpisodeScraper';

/**
 * ULTRA-FAST Embedded Episode Sync - Optimized for < 0.5 second response time
 * This endpoint syncs episodes directly into the Series document (embedded)
 * Replaces the old separate Episode collection approach
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  const startTime = Date.now();
  
  try {
    const { id } = await params;
    console.log(`⚡ ULTRA-FAST embedded episode sync for series: ${id}`);

    await connectDB();

    // OPTIMIZATION 1: Get series with current episodes
    const series = await Series.findOne({ imdbId: id });

    if (!series) {
      return NextResponse.json(
        { success: false, error: 'Series not found' },
        { status: 404 }
      );
    }

    const currentEpisodes = series.episodes || [];
    const quickCheckDuration = (Date.now() - startTime) / 1000;
    console.log(`📺 ${series.title}: ${currentEpisodes.length} existing episodes (${quickCheckDuration}s)`);

    // OPTIMIZATION 2: Ultra-fast completeness check
    if (currentEpisodes.length >= 20) { // Assume complete if 20+ episodes
      const duration = (Date.now() - startTime) / 1000;
      console.log(`⏭️ Series appears complete (${currentEpisodes.length} episodes), ultra-fast skip in ${duration}s`);
      return NextResponse.json({
        success: true,
        message: 'Series appears complete, sync skipped for speed',
        stats: {
          totalFound: currentEpisodes.length,
          newEpisodesAdded: 0,
          totalEpisodesInDB: currentEpisodes.length,
          duration: `${duration}s`,
          skipped: true,
          reason: 'complete_series'
        }
      });
    }

    // OPTIMIZATION 3: Fast IMDb scraping with timeout
    const imdbScraper = IMDbEpisodeScraper.getInstance();
    const imdbPromise = imdbScraper.getSeriesEpisodes(series.imdbId);
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('IMDb timeout')), 3000) // 3 second timeout
    );

    let imdbEpisodeData;
    try {
      imdbEpisodeData = await Promise.race([imdbPromise, timeoutPromise]);
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.log(`⚠️ IMDb timeout/error for ${series.title} in ${duration}s`);
      return NextResponse.json({
        success: false,
        error: 'IMDb data unavailable (timeout)',
        stats: {
          totalFound: 0,
          newEpisodesAdded: 0,
          totalEpisodesInDB: currentEpisodes.length,
          duration: `${duration}s`,
          reason: 'imdb_timeout'
        }
      });
    }

    if (!imdbEpisodeData) {
      const duration = (Date.now() - startTime) / 1000;
      console.log(`⚠️ No IMDb data for ${series.title} in ${duration}s`);
      return NextResponse.json({
        success: false,
        error: 'No episode data found on IMDb',
        stats: {
          totalFound: 0,
          newEpisodesAdded: 0,
          totalEpisodesInDB: currentEpisodes.length,
          duration: `${duration}s`
        }
      });
    }

    const imdbDuration = (Date.now() - startTime) / 1000;
    console.log(`🎯 IMDb: ${imdbEpisodeData.totalEpisodes} episodes in ${imdbDuration}s`);

    // OPTIMIZATION 4: Fast existing episode lookup using Set
    const existingSet = new Set<string>();
    currentEpisodes.forEach(ep => {
      existingSet.add(`S${ep.season}E${ep.episode}`);
    });

    // OPTIMIZATION 5: Build new episodes array for embedding
    const newEpisodes = [];
    let newEpisodesCount = 0;

    for (const season of imdbEpisodeData.seasons) {
      for (const imdbEpisode of season.episodes) {
        const episodeKey = `S${imdbEpisode.season}E${imdbEpisode.episode}`;
        
        // Skip if episode already exists
        if (existingSet.has(episodeKey)) {
          continue;
        }

        // Generate VidSrc URLs
        const embedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${imdbEpisode.season}&episode=${imdbEpisode.episode}`;
        const embedUrlTmdb = series.tmdbId ? `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId}&season=${imdbEpisode.season}&episode=${imdbEpisode.episode}` : '';

        newEpisodes.push({
          season: imdbEpisode.season,
          episode: imdbEpisode.episode,
          episodeTitle: imdbEpisode.title || `Episode ${imdbEpisode.episode}`,
          description: imdbEpisode.description || `${series.title} - Season ${imdbEpisode.season}, Episode ${imdbEpisode.episode}`,
          airDate: imdbEpisode.airDate ? new Date(imdbEpisode.airDate) : undefined,
          runtime: imdbEpisode.runtime || '45 min',
          imdbRating: imdbEpisode.rating,
          posterUrl: series.posterUrl, // Use series poster as fallback
          embedUrl: embedUrl,
          embedUrlTmdb: embedUrlTmdb,
          vidsrcUrl: embedUrl,
          vidsrcTmdbUrl: embedUrlTmdb,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        newEpisodesCount++;

        // OPTIMIZATION 6: Limit new episodes to prevent timeout
        if (newEpisodes.length >= 50) {
          break;
        }
      }
      if (newEpisodes.length >= 50) {
        break;
      }
    }

    // OPTIMIZATION 7: Single atomic update to embed new episodes
    if (newEpisodes.length > 0) {
      // Combine existing and new episodes, then sort
      const allEpisodes = [...currentEpisodes, ...newEpisodes];
      allEpisodes.sort((a, b) => {
        if (a.season !== b.season) return a.season - b.season;
        return a.episode - b.episode;
      });

      await Series.updateOne(
        { imdbId: id },
        {
          $set: {
            episodes: allEpisodes,
            episodeCount: allEpisodes.length,
            lastEpisodeUpdate: new Date()
          }
        }
      );
    }

    const totalDuration = (Date.now() - startTime) / 1000;
    console.log(`⚡ ULTRA-FAST embedded sync completed: ${newEpisodesCount} new episodes in ${totalDuration}s`);

    return NextResponse.json({
      success: true,
      message: `Ultra-fast embedded sync completed in ${totalDuration}s`,
      stats: {
        totalFound: imdbEpisodeData.totalEpisodes,
        newEpisodesAdded: newEpisodesCount,
        totalEpisodesInDB: currentEpisodes.length + newEpisodesCount,
        duration: `${totalDuration}s`,
        performance: 'ultra_fast_embedded'
      }
    });

  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    console.error(`❌ Ultra-fast embedded sync failed in ${duration}s:`, error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Ultra-fast embedded sync failed',
        message: error.message,
        duration: `${duration}s`
      },
      { status: 500 }
    );
  }
}

/**
 * GET method for testing
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  return POST(request, { params });
}
