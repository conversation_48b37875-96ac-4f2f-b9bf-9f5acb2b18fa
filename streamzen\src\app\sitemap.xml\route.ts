import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Movie from '@/models/Movie';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://freemovieswatchnow.com';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get counts for sitemap index
    const [movieCount, seriesCount, episodeCount] = await Promise.all([
      Movie.countDocuments(),
      Series.countDocuments(),
      Episode.countDocuments()
    ]);

    // Generate sitemap index
    const sitemaps = [
      {
        loc: `${BASE_URL}/sitemap-main.xml`,
        lastmod: new Date().toISOString(),
      },
    ];

    // Add paginated sitemaps for large datasets
    const ITEMS_PER_SITEMAP = 50000;

    // Movies pagination
    const moviePages = Math.ceil(movieCount / ITEMS_PER_SITEMAP);
    for (let i = 1; i <= moviePages; i++) {
      sitemaps.push({
        loc: `${BASE_URL}/sitemap-movies${i > 1 ? `-${i}` : ''}.xml`,
        lastmod: new Date().toISOString(),
      });
    }

    // Series pagination
    const seriesPages = Math.ceil(seriesCount / ITEMS_PER_SITEMAP);
    for (let i = 1; i <= seriesPages; i++) {
      sitemaps.push({
        loc: `${BASE_URL}/sitemap-series${i > 1 ? `-${i}` : ''}.xml`,
        lastmod: new Date().toISOString(),
      });
    }

    // Episodes pagination
    const episodePages = Math.ceil(episodeCount / ITEMS_PER_SITEMAP);
    for (let i = 1; i <= episodePages; i++) {
      sitemaps.push({
        loc: `${BASE_URL}/sitemap-episodes${i > 1 ? `-${i}` : ''}.xml`,
        lastmod: new Date().toISOString(),
      });
    }

    const sitemapIndexXML = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps.map(sitemap => `  <sitemap>
    <loc>${sitemap.loc}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`).join('\n')}
</sitemapindex>`;

    return new NextResponse(sitemapIndexXML, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating sitemap index:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
