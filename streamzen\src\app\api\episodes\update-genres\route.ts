import { NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

export async function POST() {
  try {
    console.log('🔄 Starting episode genre update...');
    
    const result = await contentService.updateEpisodesWithoutGenres();
    
    return NextResponse.json({
      success: true,
      message: `Updated ${result.updated} episodes with genres`,
      updated: result.updated,
      errors: result.errors
    });
  } catch (error) {
    console.error('Error updating episode genres:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update episode genres',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
