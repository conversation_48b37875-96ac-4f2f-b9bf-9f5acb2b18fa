import { Suspense } from 'react';
import { Metadata } from 'next';
import ContentGrid from '@/components/ContentGrid';
import OptimizedFilterSystem from '@/components/OptimizedFilterSystem';
import LoadingSpinner from '@/components/LoadingSpinner';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface SeriesPageProps {
  searchParams: Promise<{
    page?: string;
    genre?: string;
    year?: string;
    language?: string;
    country?: string;
    rating?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  }>;
}

// Transform series to content item for grid display
function transformSeriesToContentItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    genres: series.genres,
    description: series.description,
    type: 'series' as const,
    href: `/watch/series/${series.imdbId}`,
    totalSeasons: series.totalSeasons,
    status: series.status,
    language: series.language,
    country: series.country
  };
}

async function getOptimizedSeriesData(searchParams: Awaited<SeriesPageProps['searchParams']>) {
  try {
    console.log('🚀 Fetching optimized series page data...');
    const startTime = Date.now();

    // Build query parameters
    const params = new URLSearchParams();
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    // Single API call to get both content and filters
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/series/optimized?${params.toString()}`, {
      next: { revalidate: 180 } // Cache for 3 minutes
    });

    if (!response.ok) {
      throw new Error(`Series API failed: ${response.status}`);
    }

    const data = await response.json();
    const endTime = Date.now();
    
    console.log(`✅ Optimized series page data loaded in ${endTime - startTime}ms`);
    return data;

  } catch (error) {
    console.error('❌ Error fetching optimized series data:', error);
    
    // Return empty data structure on error
    return {
      content: {
        data: [],
        pagination: { page: 1, limit: 24, total: 0, pages: 0 }
      },
      filters: {
        genres: [],
        languages: [],
        countries: [],
        years: [],
        ratings: []
      },
      meta: {
        currentFilters: {},
        hasActiveFilters: false
      }
    };
  }
}

export async function generateMetadata({ searchParams }: SeriesPageProps): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  
  return SEOGenerator.generateSeriesPageMetadata({
    search: resolvedSearchParams.search,
    genre: resolvedSearchParams.genre,
    year: resolvedSearchParams.year ? parseInt(resolvedSearchParams.year) : undefined,
    language: resolvedSearchParams.language,
    country: resolvedSearchParams.country,
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page) : 1
  });
}

export default async function OptimizedSeriesPage({ searchParams }: SeriesPageProps) {
  const resolvedSearchParams = await searchParams;
  const data = await getOptimizedSeriesData(resolvedSearchParams);
  
  const { content, filters, meta } = data;
  const { data: series, pagination } = content;

  // Generate structured data for SEO
  const collectionSchema = SchemaGenerator.generateCollectionPageSchema(
    resolvedSearchParams.search ? `Search Results for "${resolvedSearchParams.search}"` : 'TV Series',
    'Watch the latest TV series and shows online free in HD quality. Discover thousands of series from all genres.',
    `/series${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`,
    series.slice(0, 10).map(show => ({
      name: `${show.title} (${show.startYear})`,
      url: `/watch/series/${show.imdbId}`,
      image: show.posterUrl
    }))
  );

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            collectionSchema
          ])
        }}
      />

      {/* Hero Section */}
      <div className="relative bg-gradient-to-b from-gray-900 via-black to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-transparent to-black/80" />
        
        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24">
          <div className="mb-12">
            {/* Premium Title with Gradient Text */}
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-purple-200 to-gray-400 mb-6 tracking-tight leading-none">
                TV Series
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-gray-600 rounded-full mb-8"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8">
              Explore premium TV series and binge-watch your favorites. From award-winning dramas to thrilling adventures, discover your next obsession.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative bg-black">
        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl lg:text-5xl font-black text-white mb-2">
                  {resolvedSearchParams.search ? 'Search Results' : 'TV Series'}
                </h1>
                {resolvedSearchParams.search && (
                  <p className="text-gray-400 text-lg">
                    Results for "{resolvedSearchParams.search}"
                  </p>
                )}
              </div>
              <div className="glass-elevated px-4 py-2 rounded-xl border border-gray-700/50">
                <span className="text-gray-300 text-base font-medium">
                  {pagination.total.toLocaleString()} series • Page {pagination.page} of {pagination.pages}
                </span>
              </div>
            </div>

            {/* Optimized Filter System */}
            <div className="glass-elevated p-6 rounded-2xl border border-gray-700/50">
              <OptimizedFilterSystem
                currentFilters={resolvedSearchParams}
                filterOptions={filters}
                basePath="/series"
                contentType="series"
              />
            </div>

            {/* Content Grid */}
            <Suspense fallback={<LoadingSpinner />}>
              <ContentGrid
                items={series.map(transformSeriesToContentItem)}
                pagination={pagination}
                basePath="/series"
                currentFilters={resolvedSearchParams}
              />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
