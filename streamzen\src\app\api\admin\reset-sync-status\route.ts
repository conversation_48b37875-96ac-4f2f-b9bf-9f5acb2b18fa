import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';

/**
 * Reset stuck sync status (when isRunning is stuck at true)
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Resetting stuck sync status...');
    
    await connectDB();
    const { default: SyncStatus } = await import('@/models/SyncStatus');
    
    // Find all running syncs
    const runningSync = await SyncStatus.find({ isRunning: true });
    console.log(`📊 Found ${runningSync.length} sync(s) marked as running`);
    
    if (runningSync.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No stuck sync processes found',
        resetCount: 0
      });
    }
    
    // Reset all running syncs to false
    const result = await SyncStatus.updateMany(
      { isRunning: true },
      { 
        isRunning: false,
        updatedAt: new Date()
      }
    );
    
    console.log(`✅ Reset ${result.modifiedCount} stuck sync process(es)`);
    
    // Log details of reset syncs
    for (const sync of runningSync) {
      console.log(`🔧 Reset ${sync.syncType}: was running since ${sync.updatedAt}`);
    }
    
    return NextResponse.json({
      success: true,
      message: `Successfully reset ${result.modifiedCount} stuck sync process(es)`,
      resetCount: result.modifiedCount,
      resetSyncs: runningSync.map(s => ({
        syncType: s.syncType,
        wasRunningSince: s.updatedAt
      }))
    });

  } catch (error) {
    console.error('❌ Error resetting sync status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to reset sync status',
        message: error.message
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Check which syncs are currently marked as running
 */
export async function GET(request: NextRequest) {
  try {
    await connectDB();
    const { default: SyncStatus } = await import('@/models/SyncStatus');
    
    const runningSync = await SyncStatus.find({ isRunning: true });
    const allSyncs = await SyncStatus.find({}).sort({ syncType: 1 });
    
    return NextResponse.json({
      success: true,
      runningCount: runningSync.length,
      totalSyncs: allSyncs.length,
      runningSyncs: runningSync.map(s => ({
        syncType: s.syncType,
        runningSince: s.updatedAt,
        nextSyncTime: s.nextSyncTime
      })),
      allSyncs: allSyncs.map(s => ({
        syncType: s.syncType,
        isRunning: s.isRunning,
        lastSyncTime: s.lastSyncTime,
        nextSyncTime: s.nextSyncTime,
        updatedAt: s.updatedAt
      }))
    });

  } catch (error) {
    console.error('❌ Error checking sync status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check sync status',
        message: error.message
      },
      { status: 500 }
    );
  }
}
