import { NextRequest, NextResponse } from 'next/server';

// GET method for easy browser testing
export async function GET(request: NextRequest) {
  return await runSync();
}

export async function POST(request: NextRequest) {
  return await runSync();
}

async function runSync() {
  try {
    console.log('🔧 Manual VidSrc Episodes Sync Triggered');

    // Call the sync endpoint
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/sync/vidsrc-episodes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const result = await response.json();
      return NextResponse.json({
        success: true,
        message: 'Manual sync completed successfully',
        ...result
      });
    } else {
      const error = await response.text();
      return NextResponse.json(
        {
          success: false,
          error: 'Manual sync failed',
          details: error
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ Manual sync error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Manual sync failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
