'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Filter, X, ChevronDown, Search, SlidersHorizontal, Star, Calendar, Globe, Tag, Sparkles } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { FilterOptions } from '@/lib/api';
import { cn } from '@/lib/utils';

interface OptimizedFilterSystemProps {
  currentFilters: Record<string, string | undefined>;
  filterOptions: FilterOptions;
  basePath: string;
  contentType: 'movies' | 'series' | 'episodes';
}

const OptimizedFilterSystem: React.FC<OptimizedFilterSystemProps> = ({
  currentFilters,
  filterOptions,
  basePath,
  contentType
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState(currentFilters.search || '');

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click is outside any dropdown
      const target = event.target as Element;
      const isInsideDropdown = target.closest('[data-dropdown]');

      if (!isInsideDropdown) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const updateFilter = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString());

    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }

    // Reset to page 1 when filters change
    params.delete('page');

    router.push(`${basePath}?${params.toString()}`);
    setActiveDropdown(null);
  };

  const clearAllFilters = () => {
    router.push(basePath);
    setSearchQuery('');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateFilter('search', searchQuery || null);
  };

  const hasActiveFilters = Object.entries(currentFilters).some(
    ([key, value]) => value && key !== 'page' && key !== 'sortBy' && key !== 'sortOrder'
  );

  const getFilterIcon = (filterType: string) => {
    switch (filterType) {
      case 'genre': return Tag;
      case 'year': return Calendar;
      case 'language': case 'country': return Globe;
      case 'rating': return Star;
      default: return Filter;
    }
  };

  const FilterDropdown = ({ 
    filterType, 
    options, 
    title 
  }: { 
    filterType: string; 
    options: any[]; 
    title: string; 
  }) => {
    const isActive = activeDropdown === filterType;
    const currentValue = currentFilters[filterType];
    const Icon = getFilterIcon(filterType);

    return (
      <div className="relative" data-dropdown>
        <button
          onClick={() => setActiveDropdown(isActive ? null : filterType)}
          className={cn(
            "flex items-center space-x-2 px-4 py-2 rounded-xl border transition-all duration-300 text-sm font-medium",
            currentValue
              ? "bg-blue-500/20 border-blue-500/50 text-blue-300"
              : "bg-gray-800/50 border-gray-700/50 text-gray-300 hover:bg-gray-700/50 hover:border-gray-600/50"
          )}
        >
          <Icon className="w-4 h-4" />
          <span>{title}</span>
          {currentValue && (
            <span className="px-2 py-0.5 bg-blue-500/30 rounded-full text-xs">
              {currentValue}
            </span>
          )}
          <ChevronDown className={cn(
            "w-4 h-4 transition-transform duration-200",
            isActive && "rotate-180"
          )} />
        </button>

        <AnimatePresence>
          {isActive && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-xl shadow-2xl z-50"
            >
              <div className="p-3">
                <div className="max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent">
                  {options.map((option, index) => {
                    // Handle different option structures based on filter type
                    let value, count;

                    if (typeof option === 'string' || typeof option === 'number') {
                      // Simple string/number values (years, ratings, qualities)
                      value = String(option);
                      count = null;
                    } else if (typeof option === 'object' && option !== null) {
                      // Object with count (genres, languages, countries)
                      if (filterType === 'genre') {
                        value = option.genre;
                        count = option.count;
                      } else if (filterType === 'language') {
                        value = option.language;
                        count = option.count;
                      } else if (filterType === 'country') {
                        value = option.country;
                        count = option.count;
                      } else if (filterType === 'quality') {
                        value = option.quality || option.name || option.value || String(option);
                        count = option.count || null;
                      } else {
                        // Fallback
                        value = option.genre || option.language || option.country || option.quality || option.name || option.value || String(option);
                        count = option.count || null;
                      }
                    } else {
                      value = String(option);
                      count = null;
                    }

                    return (
                      <button
                        key={index}
                        onClick={() => updateFilter(filterType, value)}
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-lg transition-all duration-200 text-sm flex items-center justify-between",
                          currentValue === value
                            ? "bg-blue-500/20 text-blue-300"
                            : "text-gray-300 hover:bg-gray-800/50 hover:text-white"
                        )}
                      >
                        <span className="capitalize">{value}</span>
                        {count && (
                          <span className="text-xs text-gray-500">
                            {count}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const SortDropdown = () => {
    const sortDropdownActive = activeDropdown === 'sort';
    const currentSortBy = currentFilters.sortBy || 'createdAt';
    const currentSortOrder = currentFilters.sortOrder || 'desc';

    const availableSortOptions = [
      { value: 'createdAt', label: 'Recently Added', order: 'desc' },
      { value: 'imdbRating', label: 'Highest Rated', order: 'desc' },
      { value: 'popularity', label: 'Most Popular', order: 'desc' },
      { value: 'title', label: 'A-Z', order: 'asc' },
      { value: 'title', label: 'Z-A', order: 'desc' },
      { value: 'year', label: 'Newest First', order: 'desc' },
      { value: 'year', label: 'Oldest First', order: 'asc' },
    ];

    const selectedSortLabel = availableSortOptions.find(
      option => option.value === currentSortBy && option.order === currentSortOrder
    )?.label || 'Recently Added';

    const handleSortDropdownToggle = () => {
      setActiveDropdown(sortDropdownActive ? null : 'sort');
    };

    const handleSortOptionClick = (sortValue: string, sortOrder: string) => {
      updateFilter('sortBy', sortValue);
      updateFilter('sortOrder', sortOrder);
    };

    return (
      <div className="relative" data-dropdown>
        <button
          onClick={handleSortDropdownToggle}
          className="flex items-center space-x-2 px-4 py-2 rounded-xl border bg-gray-800/50 border-gray-700/50 text-gray-300 hover:bg-gray-700/50 hover:border-gray-600/50 transition-all duration-300 text-sm font-medium"
        >
          <SlidersHorizontal className="w-4 h-4" />
          <span>Sort: {selectedSortLabel}</span>
          <ChevronDown className={cn(
            "w-4 h-4 transition-transform duration-200",
            sortDropdownActive && "rotate-180"
          )} />
        </button>

        <AnimatePresence>
          {sortDropdownActive && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-xl shadow-2xl z-50"
            >
              <div className="p-3">
                {availableSortOptions.map((sortOption, optionIndex) => (
                  <button
                    key={optionIndex}
                    onClick={() => handleSortOptionClick(sortOption.value, sortOption.order)}
                    className={cn(
                      "w-full text-left px-3 py-2 rounded-lg transition-all duration-200 text-sm",
                      currentSortBy === sortOption.value && currentSortOrder === sortOption.order
                        ? "bg-blue-500/20 text-blue-300"
                        : "text-gray-300 hover:bg-gray-800/50 hover:text-white"
                    )}
                  >
                    {sortOption.label}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <form onSubmit={handleSearch} className="relative">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={`Search ${contentType}...`}
            className="w-full pl-12 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
          />
          {searchQuery && (
            <button
              type="button"
              onClick={() => {
                setSearchQuery('');
                updateFilter('search', null);
              }}
              className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </form>

      {/* Filter Chips */}
      <div className="flex flex-wrap gap-3">
        {/* Sort */}
        <SortDropdown />

        {/* Genre Filter */}
        {filterOptions?.genres && filterOptions.genres.length > 0 && (
          <FilterDropdown
            filterType="genre"
            options={filterOptions.genres}
            title="Genre"
          />
        )}

        {/* Year Filter */}
        {filterOptions?.years && filterOptions.years.length > 0 && (
          <FilterDropdown
            filterType="year"
            options={filterOptions.years}
            title="Year"
          />
        )}

        {/* Language Filter */}
        {filterOptions?.languages && filterOptions.languages.length > 0 && (
          <FilterDropdown
            filterType="language"
            options={filterOptions.languages}
            title="Language"
          />
        )}

        {/* Country Filter */}
        {filterOptions?.countries && filterOptions.countries.length > 0 && (
          <FilterDropdown
            filterType="country"
            options={filterOptions.countries}
            title="Country"
          />
        )}

        {/* Rating Filter */}
        {filterOptions?.ratings && filterOptions.ratings.length > 0 && (
          <FilterDropdown
            filterType="rating"
            options={filterOptions.ratings}
            title="Rating"
          />
        )}

        {/* Quality Filter (for movies) */}
        {filterOptions?.qualities && filterOptions.qualities.length > 0 && (
          <FilterDropdown
            filterType="quality"
            options={filterOptions.qualities}
            title="Quality"
          />
        )}

        {/* Clear All Filters */}
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="flex items-center space-x-2 px-4 py-2 rounded-xl border border-red-500/50 bg-red-500/10 text-red-300 hover:bg-red-500/20 hover:border-red-500/70 transition-all duration-300 text-sm font-medium"
          >
            <X className="w-4 h-4" />
            <span>Clear All</span>
          </button>
        )}
      </div>

      {/* Active Filter Tags */}
      <AnimatePresence>
        <motion.div className="flex flex-wrap gap-2">
          {Object.entries(currentFilters).map(([key, value]) => {
            if (!value || key === 'page' || key === 'sortBy' || key === 'sortOrder') return null;
            
            return (
              <motion.div
                key={key}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center space-x-1 px-3 py-1 bg-blue-500/20 border border-blue-500/30 text-blue-300 rounded-full text-sm"
              >
                <span className="capitalize">{key}:</span>
                <span className="font-medium">{value}</span>
                <button
                  onClick={() => updateFilter(key, null)}
                  className="ml-1 text-blue-400 hover:text-blue-300 transition-colors duration-200"
                >
                  <X className="w-3 h-3" />
                </button>
              </motion.div>
            );
          })}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default OptimizedFilterSystem;
