'use client';

import React, { useState, useEffect } from 'react';
import { Clock, CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';
import Button from './ui/Button';
import { cn } from '@/lib/utils';

interface RequestStatus {
  _id: string;
  imdbIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processedCount: number;
  totalCount: number;
  failedIds: string[];
  errorMessages: string[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

const RequestHistory: React.FC = () => {
  const [requests, setRequests] = useState<RequestStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load requests from localStorage on component mount
  useEffect(() => {
    const savedRequests = localStorage.getItem('streamzen_requests');
    if (savedRequests) {
      try {
        const parsed = JSON.parse(savedRequests);
        setRequests(parsed);
      } catch (error) {
        console.error('Error parsing saved requests:', error);
      }
    }
  }, []);

  // Save requests to localStorage whenever requests change
  useEffect(() => {
    if (requests.length > 0) {
      localStorage.setItem('streamzen_requests', JSON.stringify(requests));
    }
  }, [requests]);

  const refreshRequest = async (requestId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/requests/${requestId}`);
      if (response.ok) {
        const updatedRequest = await response.json();
        setRequests(prev => 
          prev.map(req => req._id === requestId ? updatedRequest : req)
        );
      }
    } catch (error) {
      console.error('Error refreshing request:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="text-yellow-400" size={16} />;
      case 'processing':
        return <Loader2 className="text-blue-400 animate-spin" size={16} />;
      case 'completed':
        return <CheckCircle className="text-green-400" size={16} />;
      case 'failed':
        return <XCircle className="text-red-400" size={16} />;
      default:
        return <Clock className="text-gray-400" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400';
      case 'processing':
        return 'text-blue-400';
      case 'completed':
        return 'text-green-400';
      case 'failed':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProgressPercentage = (request: RequestStatus) => {
    return Math.round((request.processedCount / request.totalCount) * 100);
  };

  // Add new request to history (called from parent component)
  const addRequest = (request: RequestStatus) => {
    setRequests(prev => [request, ...prev.slice(0, 9)]); // Keep only last 10 requests
  };

  // Expose addRequest function to parent
  React.useEffect(() => {
    (window as any).addRequestToHistory = addRequest;
    return () => {
      delete (window as any).addRequestToHistory;
    };
  }, []);

  if (requests.length === 0) {
    return (
      <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl p-8 border border-gray-800/50">
        <div className="flex items-center mb-8">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mr-4">
            <Clock className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-white font-bold text-2xl">Request History</h2>
            <p className="text-gray-400 text-sm">Track your submission history</p>
          </div>
        </div>
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-gray-800/50 rounded-3xl flex items-center justify-center mx-auto mb-6">
            <Clock className="text-gray-400" size={32} />
          </div>
          <h3 className="text-white font-semibold text-lg mb-2">No requests yet</h3>
          <p className="text-gray-400 text-sm leading-relaxed max-w-sm mx-auto">
            Submit your first content request to see processing status and history here
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl p-8 border border-gray-800/50">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mr-4">
            <Clock className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-white font-bold text-2xl">Request History</h2>
            <p className="text-gray-400 text-sm">Track your submission history</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <div className="px-3 py-1 bg-gray-800/50 rounded-full">
            <span className="text-gray-300 text-sm font-medium">{requests.length} requests</span>
          </div>
          <Button
            onClick={() => window.location.reload()}
            variant="ghost"
            size="sm"
            className="flex items-center space-x-2 bg-gray-800/50 hover:bg-gray-800/70 rounded-xl px-3 py-2"
          >
            <RefreshCw size={16} />
            <span>Refresh</span>
          </Button>
        </div>
      </div>

      <div className="space-y-6 max-h-[500px] overflow-y-auto custom-scrollbar">
        {requests.map((request) => (
          <div
            key={request._id}
            className="bg-gray-800/30 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 hover:bg-gray-800/40 transition-all duration-200"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-700/50 rounded-xl flex items-center justify-center">
                  {getStatusIcon(request.status)}
                </div>
                <div>
                  <span className={cn('font-semibold text-sm capitalize', getStatusColor(request.status))}>
                    {request.status}
                  </span>
                  <div className="text-gray-400 text-xs mt-0.5">
                    {formatDate(request.createdAt)}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <div className="text-white font-medium text-sm">
                    {request.processedCount}/{request.totalCount}
                  </div>
                  <div className="text-gray-400 text-xs">
                    {getProgressPercentage(request)}% complete
                  </div>
                </div>
                {(request.status === 'processing' || request.status === 'pending') && (
                  <Button
                    onClick={() => refreshRequest(request._id)}
                    disabled={isLoading}
                    variant="ghost"
                    size="sm"
                    className="w-8 h-8 p-0 bg-gray-700/50 hover:bg-gray-700/70 rounded-lg"
                  >
                    <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
                  </Button>
                )}
              </div>
            </div>

            <div className="mb-4">
              <div className="w-full bg-gray-700/50 rounded-full h-3 overflow-hidden">
                <div
                  className={cn(
                    'h-3 rounded-full transition-all duration-500 relative',
                    request.status === 'completed' ? 'bg-gradient-to-r from-green-500 to-green-600' :
                    request.status === 'failed' ? 'bg-gradient-to-r from-red-500 to-red-600' :
                    request.status === 'processing' ? 'bg-gradient-to-r from-blue-500 to-blue-600' : 'bg-gradient-to-r from-yellow-500 to-yellow-600'
                  )}
                  style={{ width: `${getProgressPercentage(request)}%` }}
                >
                  {request.status === 'processing' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-300">{request.totalCount} items</span>
                </div>
                {request.failedIds.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-red-400">{request.failedIds.length} failed</span>
                  </div>
                )}
              </div>
              <div className="text-gray-400 text-xs">
                ID: {request._id.slice(-8)}
              </div>
              {request.completedAt && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-400 text-xs">
                    Completed: {formatDate(request.completedAt)}
                  </span>
                </div>
              )}
            </div>

            {request.errorMessages.length > 0 && (
              <div className="mt-4 p-4 bg-red-900/20 border border-red-500/30 rounded-xl">
                <div className="flex items-center space-x-2 mb-3">
                  <div className="w-4 h-4 bg-red-500/20 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  </div>
                  <span className="text-red-400 font-medium text-sm">Processing Errors</span>
                </div>
                <ul className="space-y-2 text-red-300 text-xs">
                  {request.errorMessages.slice(0, 3).map((error, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-1 h-1 bg-red-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      <span>{error}</span>
                    </li>
                  ))}
                  {request.errorMessages.length > 3 && (
                    <li className="flex items-start space-x-2">
                      <div className="w-1 h-1 bg-red-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      <span className="italic">... and {request.errorMessages.length - 3} more errors</span>
                    </li>
                  )}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default RequestHistory;
