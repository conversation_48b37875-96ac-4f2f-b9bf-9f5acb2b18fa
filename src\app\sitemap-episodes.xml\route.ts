import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Episode from '@/models/Episode';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlsXML = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlsXML}
</urlset>`;
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = 50000; // Max URLs per sitemap
    const skip = (page - 1) * limit;

    // Get episodes with pagination, prioritizing recent and high-rated content
    const episodes = await Episode.find({})
      .select('imdbId seriesTitle season episode episodeTitle airDate imdbRating updatedAt createdAt')
      .sort({ 
        createdAt: -1,    // Newest episodes first
        airDate: -1,      // Recent air dates
        imdbRating: -1,   // High-rated episodes
        season: -1,       // Latest seasons
        episode: -1       // Latest episodes
      })
      .skip(skip)
      .limit(limit)
      .lean();

    const urls: SitemapUrl[] = episodes.map(episode => {
      // Calculate priority based on recency and rating
      let priority = 0.5; // Base priority for episodes
      
      const now = new Date();
      const episodeDate = episode.airDate || episode.createdAt;
      const daysSinceAired = episodeDate ? Math.floor((now.getTime() - episodeDate.getTime()) / (1000 * 60 * 60 * 24)) : 365;
      
      // Recent episodes get higher priority
      if (daysSinceAired <= 7) priority = 0.8;        // Last week
      else if (daysSinceAired <= 30) priority = 0.7;   // Last month
      else if (daysSinceAired <= 90) priority = 0.6;   // Last 3 months
      
      // High-rated episodes get bonus
      if (episode.imdbRating && episode.imdbRating > 8.0) priority += 0.1;
      else if (episode.imdbRating && episode.imdbRating > 7.0) priority += 0.05;

      // Season finales and premieres get bonus (episode 1 or high episode numbers)
      if (episode.episode === 1) priority += 0.05; // Season premiere
      if (episode.episode >= 20) priority += 0.05; // Likely season finale

      // Cap priority at 0.9
      priority = Math.min(priority, 0.9);

      // Determine change frequency based on recency
      let changefreq: SitemapUrl['changefreq'] = 'yearly';
      if (daysSinceAired <= 7) changefreq = 'daily';
      else if (daysSinceAired <= 30) changefreq = 'weekly';
      else if (daysSinceAired <= 90) changefreq = 'monthly';

      return {
        loc: `${BASE_URL}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
        lastmod: episode.updatedAt?.toISOString() || episode.createdAt?.toISOString() || new Date().toISOString(),
        changefreq,
        priority: Math.round(priority * 10) / 10, // Round to 1 decimal
      };
    });

    const sitemapXML = generateSitemapXML(urls);

    return new NextResponse(sitemapXML, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=1800, s-maxage=1800', // Cache for 30 minutes (episodes change more frequently)
      },
    });
  } catch (error) {
    console.error('Error generating episodes sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
