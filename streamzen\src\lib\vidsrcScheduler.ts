import connectDB from './mongodb';
import mongoose from 'mongoose';

// Schema for tracking sync status
const SyncStatusSchema = new mongoose.Schema({
  syncType: { type: String, required: true, unique: true },
  lastSyncTime: { type: Date, required: true },
  nextSyncTime: { type: Date, required: true },
  isRunning: { type: Boolean, default: false },
  lastResult: {
    success: Boolean,
    message: String,
    episodesProcessed: Number,
    duration: String,
    timestamp: Date
  }
}, { timestamps: true });

const SyncStatus = mongoose.models.SyncStatus || mongoose.model('SyncStatus', SyncStatusSchema);

export class VidSrcScheduler {
  private static instance: VidSrcScheduler;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL_HOURS = 3; // 3 hours
  private readonly SYNC_TYPE = 'vidsrc-episodes';

  private constructor() {}

  static getInstance(): VidSrcScheduler {
    if (!VidSrcScheduler.instance) {
      VidSrcScheduler.instance = new VidSrcScheduler();
    }
    return VidSrcScheduler.instance;
  }

  /**
   * Initialize the scheduler - call this once when the app starts
   */
  async initialize(): Promise<void> {
    try {
      await connectDB();
      console.log('🕐 Initializing VidSrc Scheduler...');
      
      // Get or create sync status
      let syncStatus = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });
      
      if (!syncStatus) {
        // First time setup - schedule next sync for 3 hours from now
        const now = new Date();
        const nextSync = new Date(now.getTime() + (this.SYNC_INTERVAL_HOURS * 60 * 60 * 1000));
        
        syncStatus = new SyncStatus({
          syncType: this.SYNC_TYPE,
          lastSyncTime: new Date(0), // Never synced
          nextSyncTime: nextSync,
          isRunning: false
        });
        
        await syncStatus.save();
        console.log(`📅 First time setup - next sync scheduled for: ${nextSync.toISOString()}`);
      }
      
      // Start the scheduler
      this.startScheduler();
      
    } catch (error) {
      console.error('❌ Failed to initialize VidSrc Scheduler:', error);
    }
  }

  /**
   * Start the background scheduler
   */
  private startScheduler(): void {
    // Check every 5 minutes if it's time to sync
    this.syncInterval = setInterval(async () => {
      await this.checkAndRunSync();
    }, 5 * 60 * 1000); // 5 minutes

    console.log('✅ VidSrc Scheduler started - checking every 5 minutes');
  }

  /**
   * Stop the scheduler
   */
  stopScheduler(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('🛑 VidSrc Scheduler stopped');
    }
  }

  /**
   * Check if it's time to sync and run if needed
   */
  private async checkAndRunSync(): Promise<void> {
    try {
      await connectDB();
      
      const syncStatus = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });
      if (!syncStatus) return;

      const now = new Date();
      
      // Check if it's time to sync and not already running
      if (now >= syncStatus.nextSyncTime && !syncStatus.isRunning) {
        console.log('⏰ Time for scheduled VidSrc sync!');
        await this.runScheduledSync();
      }
      
    } catch (error) {
      console.error('❌ Error in scheduler check:', error);
    }
  }

  /**
   * Run the scheduled sync
   */
  private async runScheduledSync(): Promise<void> {
    try {
      // Mark as running
      await SyncStatus.updateOne(
        { syncType: this.SYNC_TYPE },
        { isRunning: true }
      );

      console.log('🚀 Starting scheduled VidSrc sync...');
      const startTime = Date.now();

      // Call the sync endpoint
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const response = await fetch(`${baseUrl}/api/sync/vidsrc-episodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      const duration = (Date.now() - startTime) / 1000;

      // Calculate next sync time (3 hours from now)
      const nextSyncTime = new Date(Date.now() + (this.SYNC_INTERVAL_HOURS * 60 * 60 * 1000));

      // Update sync status
      await SyncStatus.updateOne(
        { syncType: this.SYNC_TYPE },
        {
          lastSyncTime: new Date(),
          nextSyncTime,
          isRunning: false,
          lastResult: {
            success: result.success,
            message: result.message,
            episodesProcessed: result.stats?.totalEpisodes || 0,
            duration: `${duration}s`,
            timestamp: new Date()
          }
        }
      );

      console.log(`✅ Scheduled sync completed in ${duration}s`);
      console.log(`📅 Next sync scheduled for: ${nextSyncTime.toISOString()}`);

    } catch (error) {
      console.error('❌ Scheduled sync failed:', error);
      
      // Mark as not running and schedule retry in 30 minutes
      const retryTime = new Date(Date.now() + (30 * 60 * 1000));
      await SyncStatus.updateOne(
        { syncType: this.SYNC_TYPE },
        {
          isRunning: false,
          nextSyncTime: retryTime,
          lastResult: {
            success: false,
            message: `Sync failed: ${error.message}`,
            timestamp: new Date()
          }
        }
      );
    }
  }

  /**
   * Force a manual sync (for manual triggers)
   */
  async forceSync(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      await connectDB();
      
      const syncStatus = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });
      
      if (syncStatus?.isRunning) {
        return {
          success: false,
          message: 'Sync is already running. Please wait for it to complete.'
        };
      }

      console.log('🔧 Manual VidSrc sync triggered');
      
      // Mark as running
      await SyncStatus.updateOne(
        { syncType: this.SYNC_TYPE },
        { isRunning: true }
      );

      const startTime = Date.now();

      // Call the sync endpoint
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const response = await fetch(`${baseUrl}/api/sync/vidsrc-episodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      const duration = (Date.now() - startTime) / 1000;

      // Update sync status (don't change next scheduled time for manual sync)
      await SyncStatus.updateOne(
        { syncType: this.SYNC_TYPE },
        {
          lastSyncTime: new Date(),
          isRunning: false,
          lastResult: {
            success: result.success,
            message: result.message,
            episodesProcessed: result.stats?.totalEpisodes || 0,
            duration: `${duration}s`,
            timestamp: new Date()
          }
        }
      );

      return {
        success: true,
        message: `Manual sync completed in ${duration}s`,
        data: result
      };

    } catch (error) {
      console.error('❌ Manual sync failed:', error);
      
      // Mark as not running
      await SyncStatus.updateOne(
        { syncType: this.SYNC_TYPE },
        { isRunning: false }
      );

      return {
        success: false,
        message: `Manual sync failed: ${error.message}`
      };
    }
  }

  /**
   * Get current sync status
   */
  async getSyncStatus(): Promise<any> {
    try {
      await connectDB();
      const syncStatus = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });
      
      if (!syncStatus) {
        return {
          syncType: this.SYNC_TYPE,
          status: 'not_initialized',
          message: 'Scheduler not initialized'
        };
      }

      const now = new Date();
      const timeUntilNext = syncStatus.nextSyncTime.getTime() - now.getTime();
      const hoursUntilNext = Math.max(0, timeUntilNext / (1000 * 60 * 60));

      return {
        syncType: this.SYNC_TYPE,
        status: syncStatus.isRunning ? 'running' : 'scheduled',
        lastSyncTime: syncStatus.lastSyncTime,
        nextSyncTime: syncStatus.nextSyncTime,
        hoursUntilNext: Math.round(hoursUntilNext * 100) / 100,
        isRunning: syncStatus.isRunning,
        lastResult: syncStatus.lastResult,
        intervalHours: this.SYNC_INTERVAL_HOURS
      };
      
    } catch (error) {
      return {
        syncType: this.SYNC_TYPE,
        status: 'error',
        message: error.message
      };
    }
  }
}

export default VidSrcScheduler;
