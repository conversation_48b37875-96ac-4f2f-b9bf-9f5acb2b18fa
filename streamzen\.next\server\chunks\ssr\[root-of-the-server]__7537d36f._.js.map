{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatYear(year: number): string {\n  return year.toString();\n}\n\nexport function formatRating(rating: number | string): string {\n  if (!rating) return 'N/A';\n  if (typeof rating === 'number') return rating.toFixed(1);\n  if (typeof rating === 'string' && !isNaN(Number(rating))) return Number(rating).toFixed(1);\n  return rating.toString(); // Return as-is for MPAA ratings like \"R\", \"PG-13\"\n}\n\nexport function formatRuntime(runtime: string): string {\n  return runtime;\n}\n\nexport function getImageUrl(url: string | undefined, fallback: string = '/placeholder-poster.jpg'): string {\n  if (!url) return fallback;\n  \n  // If it's already a full URL, return as is\n  if (url.startsWith('http')) return url;\n  \n  // If it's a relative path, make it absolute\n  return url.startsWith('/') ? url : `/${url}`;\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).trim() + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAY;IACrC,OAAO,KAAK,QAAQ;AACtB;AAEO,SAAS,aAAa,MAAuB;IAClD,IAAI,CAAC,QAAQ,OAAO;IACpB,IAAI,OAAO,WAAW,UAAU,OAAO,OAAO,OAAO,CAAC;IACtD,IAAI,OAAO,WAAW,YAAY,CAAC,MAAM,OAAO,UAAU,OAAO,OAAO,QAAQ,OAAO,CAAC;IACxF,OAAO,OAAO,QAAQ,IAAI,kDAAkD;AAC9E;AAEO,SAAS,cAAc,OAAe;IAC3C,OAAO;AACT;AAEO,SAAS,YAAY,GAAuB,EAAE,WAAmB,yBAAyB;IAC/F,IAAI,CAAC,KAAK,OAAO;IAEjB,2CAA2C;IAC3C,IAAI,IAAI,UAAU,CAAC,SAAS,OAAO;IAEnC,4CAA4C;IAC5C,OAAO,IAAI,UAAU,CAAC,OAAO,MAAM,CAAC,CAAC,EAAE,KAAK;AAC9C;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AAC/C", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n\nexport interface Movie {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Series {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Episode {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: string;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n}\n\nexport interface ContentFilters {\n  genre?: string;\n  year?: number;\n  language?: string;\n  country?: string;\n  rating?: string;\n  quality?: string;\n  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n  search?: string;\n  isLatestRelease?: boolean; // ✅ NEW: Filter for VidSrc latest episodes only\n}\n\nexport interface GenreCount {\n  genre: string;\n  count: number;\n}\n\nexport interface LanguageCount {\n  language: string;\n  count: number;\n}\n\nexport interface CountryCount {\n  country: string;\n  count: number;\n}\n\nexport interface FilterOptions {\n  genres: GenreCount[];\n  languages: LanguageCount[];\n  countries: CountryCount[];\n  years: number[];\n  ratings: string[];\n  qualities: string[];\n}\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    // For server-side rendering, use absolute URL\n    const baseUrl = this.baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed: ${url}`, error);\n      throw error;\n    }\n  }\n\n  // Movies\n  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<Movie>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Movie>>(`/api/movies?${params.toString()}`);\n  }\n\n  async getMovie(id: string): Promise<Movie> {\n    return this.request<Movie>(`/api/movies/${id}`);\n  }\n\n  // Series\n  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<Series>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Series>>(`/api/series?${params.toString()}`);\n  }\n\n  async getSeriesById(id: string): Promise<Series> {\n    return this.request<Series>(`/api/series/${id}`);\n  }\n\n  async getSeriesEpisodes(id: string, season?: number): Promise<Episode[]> {\n    const params = season ? `?season=${season}` : '';\n    return this.request<Episode[]>(`/api/series/${id}/episodes${params}`);\n  }\n\n  // Episodes\n  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<Episode>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Episode>>(`/api/episodes?${params.toString()}`);\n  }\n\n  // Requests\n  async createBulkRequest(imdbIds: string[], contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<{ requestId: string; status: string; totalCount: number; message: string }> {\n    return this.request('/api/requests', {\n      method: 'POST',\n      body: JSON.stringify({ imdbIds, contentType }),\n    });\n  }\n\n  async getRequestStatus(requestId: string): Promise<any> {\n    return this.request(`/api/requests/${requestId}`);\n  }\n\n  // Filter Options\n  async getMovieFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/movies/filters');\n  }\n\n  async getSeriesFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/series/filters');\n  }\n\n  async getEpisodeFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/episodes/filters');\n  }\n\n  // Search\n  async search(query: string, type: 'all' | 'movies' | 'series' | 'episodes' = 'all', page: number = 1, limit: number = 20): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      type,\n      page: page.toString(),\n      limit: limit.toString()\n    });\n    return this.request(`/api/search?${params.toString()}`);\n  }\n\n  async getSearchSuggestions(query: string, limit: number = 8): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      limit: limit.toString()\n    });\n    return this.request(`/api/search/suggestions?${params.toString()}`);\n  }\n\n  // Sync\n  async syncContent(): Promise<{ success: boolean; message: string; counts: { movies: number; series: number; episodes: number } }> {\n    return this.request('/api/sync', {\n      method: 'POST',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default ApiClient;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI,CAAC,6EAAyD,uBAAuB;AA+HzI,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,8CAA8C;QAC9C,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,6EAAyD,uBAAuB;QACjH,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,UAAU,UAAU;QAE5E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE;YAC5C,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAqC;QAC/E,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA2B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IAClF;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,YAAY,EAAE,IAAI;IAChD;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAsC;QAChF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA4B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACnF;IAEA,MAAM,cAAc,EAAU,EAAmB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAS,CAAC,YAAY,EAAE,IAAI;IACjD;IAEA,MAAM,kBAAkB,EAAU,EAAE,MAAe,EAAsB;QACvE,MAAM,SAAS,SAAS,CAAC,QAAQ,EAAE,QAAQ,GAAG;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAY,CAAC,YAAY,EAAE,GAAG,SAAS,EAAE,QAAQ;IACtE;IAEA,WAAW;IACX,MAAM,YAAY,UAA0B,CAAC,CAAC,EAAuC;QACnF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA6B,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;IACtF;IAEA,WAAW;IACX,MAAM,kBAAkB,OAAiB,EAAE,cAA2C,MAAM,EAAuF;QACjL,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAY;QAC9C;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAAgB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,WAAW;IAClD;IAEA,iBAAiB;IACjB,MAAM,wBAAgD;QACpD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,0BAAkD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,SAAS;IACT,MAAM,OAAO,KAAa,EAAE,OAAiD,KAAK,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAgB;QACtI,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH;YACA,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACxD;IAEA,MAAM,qBAAqB,KAAa,EAAE,QAAgB,CAAC,EAAgB;QACzE,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,wBAAwB,EAAE,OAAO,QAAQ,IAAI;IACpE;IAEA,OAAO;IACP,MAAM,cAA4H;QAChI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/hooks/useSearch.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { apiClient } from '@/lib/api';\n\nexport interface SearchResult {\n  _id: string;\n  title: string;\n  type: 'movie' | 'series' | 'episode';\n  posterUrl?: string;\n  rating?: number; // IMDb rating (numeric)\n  mpaaRating?: string; // MPAA rating (R, PG-13, etc.)\n  releaseDate?: string;\n  imdbId?: string;\n  score: number;\n  \n  // Series specific\n  totalSeasons?: number;\n  \n  // Episode specific\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n  seriesPoster?: string;\n  seriesImdbId?: string;\n  airDate?: string;\n}\n\nexport interface SearchResponse {\n  results: SearchResult[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n  query: string;\n}\n\nexport interface SearchSuggestion {\n  _id: string;\n  title: string;\n  type: 'movie' | 'series' | 'episode';\n  posterUrl?: string;\n  rating?: number; // IMDb rating (numeric)\n  mpaaRating?: string; // MPAA rating (R, PG-13, etc.)\n  releaseDate?: string;\n  imdbId?: string;\n  score: number;\n  \n  // Series specific\n  totalSeasons?: number;\n  \n  // Episode specific\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n  seriesPoster?: string;\n  seriesImdbId?: string;\n  airDate?: string;\n}\n\nexport interface SuggestionsResponse {\n  suggestions: SearchSuggestion[];\n  query: string;\n}\n\nexport const useSearch = () => {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const search = useCallback(async (\n    query: string,\n    type: 'all' | 'movies' | 'series' | 'episodes' = 'all',\n    page: number = 1,\n    limit: number = 20\n  ): Promise<SearchResponse | null> => {\n    if (!query.trim()) {\n      return {\n        results: [],\n        pagination: { page: 1, limit, total: 0, pages: 0 },\n        query: ''\n      };\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await apiClient.search(query.trim(), type, page, limit);\n      return response;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Search failed';\n      setError(errorMessage);\n      console.error('Search error:', err);\n      return null;\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  const getSuggestions = useCallback(async (\n    query: string,\n    limit: number = 8\n  ): Promise<SuggestionsResponse | null> => {\n    if (!query.trim() || query.trim().length < 2) {\n      return { suggestions: [], query: '' };\n    }\n\n    try {\n      const response = await apiClient.getSearchSuggestions(query.trim(), limit);\n      return response;\n    } catch (err) {\n      console.error('Suggestions error:', err);\n      return { suggestions: [], query: query.trim() };\n    }\n  }, []);\n\n  return {\n    search,\n    getSuggestions,\n    isLoading,\n    error\n  };\n};\n\nexport const useSearchSuggestions = (query: string, limit: number = 8) => {\n  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const { getSuggestions } = useSearch();\n\n  useEffect(() => {\n    const fetchSuggestions = async () => {\n      if (!query.trim() || query.trim().length < 2) {\n        setSuggestions([]);\n        return;\n      }\n\n      setIsLoading(true);\n      const result = await getSuggestions(query, limit);\n      if (result) {\n        setSuggestions(result.suggestions);\n      }\n      setIsLoading(false);\n    };\n\n    const timeoutId = setTimeout(fetchSuggestions, 300); // Debounce\n    return () => clearTimeout(timeoutId);\n  }, [query, limit, getSuggestions]);\n\n  return {\n    suggestions,\n    isLoading\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAgEO,MAAM,YAAY;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACzB,OACA,OAAiD,KAAK,EACtD,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;gBACL,SAAS,EAAE;gBACX,YAAY;oBAAE,MAAM;oBAAG;oBAAO,OAAO;oBAAG,OAAO;gBAAE;gBACjD,OAAO;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,MAAM,MAAM;YAClE,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,OACA,QAAgB,CAAC;QAEjB,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YAC5C,OAAO;gBAAE,aAAa,EAAE;gBAAE,OAAO;YAAG;QACtC;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,MAAM,IAAI,IAAI;YACpE,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;gBAAE,aAAa,EAAE;gBAAE,OAAO,MAAM,IAAI;YAAG;QAChD;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,uBAAuB,CAAC,OAAe,QAAgB,CAAC;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,cAAc,EAAE,GAAG;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC5C,eAAe,EAAE;gBACjB;YACF;YAEA,aAAa;YACb,MAAM,SAAS,MAAM,eAAe,OAAO;YAC3C,IAAI,QAAQ;gBACV,eAAe,OAAO,WAAW;YACnC;YACA,aAAa;QACf;QAEA,MAAM,YAAY,WAAW,kBAAkB,MAAM,WAAW;QAChE,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAO;QAAO;KAAe;IAEjC,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Search, X, Clock, Star, Calendar, Play, TrendingUp, Filter, Sparkles, Zap, ArrowRight, History } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSearchSuggestions, SearchSuggestion } from '@/hooks/useSearch';\nimport { cn, formatRating } from '@/lib/utils';\nimport Image from 'next/image';\n\ninterface SearchBarProps {\n  placeholder?: string;\n  className?: string;\n  showSuggestions?: boolean;\n  onSearch?: (query: string) => void;\n  autoFocus?: boolean;\n  onClose?: () => void;\n}\n\nconst SearchBar: React.FC<SearchBarProps> = ({\n  placeholder = \"Search movies, series, episodes...\",\n  className = \"\",\n  showSuggestions = true,\n  onSearch,\n  autoFocus = false,\n  onClose\n}) => {\n  const [query, setQuery] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const [isFocused, setIsFocused] = useState(false);\n  const [searchHistory, setSearchHistory] = useState<string[]>([]);\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedType, setSelectedType] = useState<'all' | 'movies' | 'series' | 'episodes'>('all');\n\n  const router = useRouter();\n  const inputRef = useRef<HTMLInputElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const { suggestions, isLoading } = useSearchSuggestions(query, 12);\n\n  // Auto focus if requested\n  useEffect(() => {\n    if (autoFocus && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [autoFocus]);\n\n  // Load search history from localStorage\n  useEffect(() => {\n    const history = localStorage.getItem('searchHistory');\n    if (history) {\n      setSearchHistory(JSON.parse(history));\n    }\n  }, []);\n\n  // Save search to history\n  const saveToHistory = (searchQuery: string) => {\n    const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 5);\n    setSearchHistory(newHistory);\n    localStorage.setItem('searchHistory', JSON.stringify(newHistory));\n  };\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setSelectedIndex(-1);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleSearch = (searchQuery: string = query, type: string = selectedType) => {\n    if (!searchQuery.trim()) return;\n\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    saveToHistory(searchQuery.trim());\n\n    if (onSearch) {\n      onSearch(searchQuery.trim());\n    } else {\n      // Navigate to search page with the query and type\n      const params = new URLSearchParams({\n        q: searchQuery.trim(),\n        ...(type !== 'all' && { type })\n      });\n      router.push(`/search?${params.toString()}`);\n    }\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!showSuggestions || !isOpen) {\n      if (e.key === 'Enter') {\n        handleSearch();\n      }\n      return;\n    }\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => \n          prev < suggestions.length - 1 ? prev + 1 : prev\n        );\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => prev > -1 ? prev - 1 : -1);\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0 && suggestions[selectedIndex]) {\n          handleSuggestionClick(suggestions[selectedIndex]);\n        } else {\n          handleSearch();\n        }\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        inputRef.current?.blur();\n        break;\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: SearchSuggestion) => {\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    \n    // Navigate based on content type\n    if (suggestion.type === 'movie') {\n      router.push(`/watch/movie/${suggestion.imdbId}`);\n    } else if (suggestion.type === 'series') {\n      router.push(`/watch/series/${suggestion.imdbId}`);\n    } else if (suggestion.type === 'episode') {\n      router.push(`/watch/series/${suggestion.seriesImdbId}?season=${suggestion.season}&episode=${suggestion.episode}`);\n    }\n  };\n\n  const clearSearch = () => {\n    setQuery('');\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    inputRef.current?.focus();\n  };\n\n  const formatRating = (rating?: number | string) => {\n    if (!rating) return 'N/A';\n    if (typeof rating === 'number') return rating.toFixed(1);\n    if (typeof rating === 'string' && !isNaN(Number(rating))) return Number(rating).toFixed(1);\n    return rating; // Return as-is for MPAA ratings like \"R\", \"PG-13\"\n  };\n\n  const formatDate = (date?: string) => {\n    if (!date) return '';\n    return new Date(date).getFullYear().toString();\n  };\n\n  return (\n    <div ref={containerRef} className={cn(\"relative\", className)}>\n      {/* Enhanced Search Input */}\n      <motion.div\n        className=\"relative\"\n        initial={{ scale: 0.95, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        {/* Search Icon */}\n        <motion.div\n          className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\"\n          animate={{\n            scale: isFocused ? 1.1 : 1,\n            color: isFocused ? '#ef4444' : '#9ca3af'\n          }}\n          transition={{ duration: 0.2 }}\n        >\n          <Search className=\"h-5 w-5\" />\n        </motion.div>\n\n        {/* Enhanced Input */}\n        <motion.input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => {\n            setQuery(e.target.value);\n            setIsOpen(showSuggestions && (e.target.value.trim().length >= 2 || searchHistory.length > 0));\n            setSelectedIndex(-1);\n          }}\n          onFocus={() => {\n            setIsFocused(true);\n            if (showSuggestions && (query.trim().length >= 2 || searchHistory.length > 0)) {\n              setIsOpen(true);\n            }\n          }}\n          onBlur={() => setIsFocused(false)}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          className={cn(\n            \"w-full h-14 pl-12 pr-20 bg-gradient-to-r from-gray-900/50 to-gray-800/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl text-white placeholder-gray-400 transition-all duration-300\",\n            \"focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:border-red-500/50 focus:bg-gray-900/70\",\n            \"hover:border-gray-600/50 hover:bg-gray-900/60\"\n          )}\n          whileFocus={{ scale: 1.02 }}\n        />\n\n        {/* Action Buttons */}\n        <div className=\"absolute inset-y-0 right-0 flex items-center space-x-2 pr-4\">\n          {/* Filter Button */}\n          <motion.button\n            onClick={() => setShowFilters(!showFilters)}\n            className={cn(\n              \"p-2 rounded-lg transition-all duration-200\",\n              showFilters\n                ? \"bg-red-500/20 text-red-400 border border-red-500/30\"\n                : \"text-gray-400 hover:text-white hover:bg-white/10\"\n            )}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Filter className=\"h-4 w-4\" />\n          </motion.button>\n\n          {/* Clear Button */}\n          <AnimatePresence>\n            {query && (\n              <motion.button\n                initial={{ scale: 0, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0, opacity: 0 }}\n                onClick={clearSearch}\n                className=\"p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <X className=\"h-4 w-4\" />\n              </motion.button>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* Animated Border */}\n        <motion.div\n          className=\"absolute inset-0 rounded-2xl border-2 border-red-500/30 pointer-events-none\"\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{\n            opacity: isFocused ? 1 : 0,\n            scale: isFocused ? 1 : 0.95\n          }}\n          transition={{ duration: 0.2 }}\n        />\n      </motion.div>\n\n      {/* Search Suggestions */}\n      {showSuggestions && isOpen && (\n        <div className=\"absolute top-full left-0 right-0 mt-2 bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl z-50 max-h-96 overflow-y-auto\">\n          {isLoading ? (\n            <div className=\"p-4 text-center text-gray-400\">\n              <div className=\"animate-spin w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full mx-auto\"></div>\n              <p className=\"mt-2 text-sm\">Searching...</p>\n            </div>\n          ) : suggestions.length > 0 ? (\n            <div className=\"py-2\">\n              {suggestions.map((suggestion, index) => (\n                <button\n                  key={`${suggestion.type}-${suggestion._id}`}\n                  onClick={() => handleSuggestionClick(suggestion)}\n                  className={`w-full px-4 py-3 flex items-center space-x-3 hover:bg-gray-800/50 transition-colors text-left ${\n                    selectedIndex === index ? 'bg-gray-800/50' : ''\n                  }`}\n                >\n                  {/* Poster/Thumbnail */}\n                  <div className=\"flex-shrink-0 w-12 h-16 bg-gray-800 rounded-lg overflow-hidden\">\n                    {(suggestion.posterUrl || suggestion.seriesPoster) ? (\n                      <Image\n                        src={suggestion.posterUrl || suggestion.seriesPoster || ''}\n                        alt={suggestion.title}\n                        width={48}\n                        height={64}\n                        className=\"w-full h-full object-cover\"\n                        unoptimized\n                      />\n                    ) : (\n                      <div className=\"w-full h-full flex items-center justify-center\">\n                        <Play className=\"w-6 h-6 text-gray-600\" />\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Content Info */}\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <h4 className=\"text-white font-medium truncate\">\n                        {suggestion.title}\n                      </h4>\n                      <span className={`px-2 py-0.5 text-xs rounded-full ${\n                        suggestion.type === 'movie' ? 'bg-blue-600/20 text-blue-400' :\n                        suggestion.type === 'series' ? 'bg-green-600/20 text-green-400' :\n                        'bg-purple-600/20 text-purple-400'\n                      }`}>\n                        {suggestion.type === 'episode' ? 'EP' : suggestion.type.toUpperCase()}\n                      </span>\n                    </div>\n\n                    {/* Episode specific info */}\n                    {suggestion.type === 'episode' && (\n                      <p className=\"text-gray-400 text-sm truncate mb-1\">\n                        {suggestion.seriesTitle} • S{suggestion.season}E{suggestion.episode}\n                      </p>\n                    )}\n\n                    {/* Series specific info */}\n                    {suggestion.type === 'series' && suggestion.totalSeasons && (\n                      <p className=\"text-gray-400 text-sm mb-1\">\n                        {suggestion.totalSeasons} Season{suggestion.totalSeasons > 1 ? 's' : ''}\n                      </p>\n                    )}\n\n                    {/* Rating and Date */}\n                    <div className=\"flex items-center space-x-3 text-xs text-gray-500\">\n                      {suggestion.rating && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Star className=\"w-3 h-3 text-yellow-500\" />\n                          <span>{formatRating(suggestion.rating)}</span>\n                        </div>\n                      )}\n                      {(suggestion.releaseDate || suggestion.airDate) && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"w-3 h-3\" />\n                          <span>{formatDate(suggestion.releaseDate || suggestion.airDate)}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </button>\n              ))}\n              \n              {/* View All Results */}\n              <div className=\"border-t border-gray-700/50 mt-2 pt-2\">\n                <button\n                  onClick={() => handleSearch()}\n                  className=\"w-full px-4 py-2 text-center text-red-400 hover:text-red-300 text-sm font-medium transition-colors\"\n                >\n                  View all results for \"{query}\"\n                </button>\n              </div>\n            </div>\n          ) : query.trim().length >= 2 ? (\n            <div className=\"p-4 text-center text-gray-400\">\n              <Search className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\n              <p className=\"text-sm\">No results found for \"{query}\"</p>\n              <p className=\"text-xs mt-1\">Try different keywords or check spelling</p>\n            </div>\n          ) : null}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchBar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAmBA,MAAM,YAAsC,CAAC,EAC3C,cAAc,oCAAoC,EAClD,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,QAAQ,EACR,YAAY,KAAK,EACjB,OAAO,EACR;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAE3F,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;IAE/D,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAU;IAEd,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,IAAI,SAAS;YACX,iBAAiB,KAAK,KAAK,CAAC;QAC9B;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;YAAC;eAAgB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;SAAa,CAAC,KAAK,CAAC,GAAG;QAC3F,iBAAiB;QACjB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAChF,UAAU;gBACV,iBAAiB,CAAC;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC,cAAsB,KAAK,EAAE,OAAe,YAAY;QAC5E,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,UAAU;QACV,iBAAiB,CAAC;QAClB,cAAc,YAAY,IAAI;QAE9B,IAAI,UAAU;YACZ,SAAS,YAAY,IAAI;QAC3B,OAAO;YACL,kDAAkD;YAClD,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG,YAAY,IAAI;gBACnB,GAAI,SAAS,SAAS;oBAAE;gBAAK,CAAC;YAChC;YACA,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,QAAQ,IAAI;QAC5C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,mBAAmB,CAAC,QAAQ;YAC/B,IAAI,EAAE,GAAG,KAAK,SAAS;gBACrB;YACF;YACA;QACF;QAEA,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,YAAY,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE7C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,OAAO,CAAC,IAAI,OAAO,IAAI,CAAC;gBACjD;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,KAAK,WAAW,CAAC,cAAc,EAAE;oBACpD,sBAAsB,WAAW,CAAC,cAAc;gBAClD,OAAO;oBACL;gBACF;gBACA;YACF,KAAK;gBACH,UAAU;gBACV,iBAAiB,CAAC;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,UAAU;QACV,iBAAiB,CAAC;QAElB,iCAAiC;QACjC,IAAI,WAAW,IAAI,KAAK,SAAS;YAC/B,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,MAAM,EAAE;QACjD,OAAO,IAAI,WAAW,IAAI,KAAK,UAAU;YACvC,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,WAAW,MAAM,EAAE;QAClD,OAAO,IAAI,WAAW,IAAI,KAAK,WAAW;YACxC,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,WAAW,YAAY,CAAC,QAAQ,EAAE,WAAW,MAAM,CAAC,SAAS,EAAE,WAAW,OAAO,EAAE;QAClH;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,UAAU;QACV,iBAAiB,CAAC;QAClB,SAAS,OAAO,EAAE;IACpB;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,QAAQ,OAAO;QACpB,IAAI,OAAO,WAAW,UAAU,OAAO,OAAO,OAAO,CAAC;QACtD,IAAI,OAAO,WAAW,YAAY,CAAC,MAAM,OAAO,UAAU,OAAO,OAAO,QAAQ,OAAO,CAAC;QACxF,OAAO,QAAQ,kDAAkD;IACnE;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,IAAI,KAAK,MAAM,WAAW,GAAG,QAAQ;IAC9C;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAEhD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAM,SAAS;gBAAE;gBACnC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;;kCAG5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO,YAAY,MAAM;4BACzB,OAAO,YAAY,YAAY;wBACjC;wBACA,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;wBACX,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC;4BACT,SAAS,EAAE,MAAM,CAAC,KAAK;4BACvB,UAAU,mBAAmB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,IAAI,KAAK,cAAc,MAAM,GAAG,CAAC;4BAC3F,iBAAiB,CAAC;wBACpB;wBACA,SAAS;4BACP,aAAa;4BACb,IAAI,mBAAmB,CAAC,MAAM,IAAI,GAAG,MAAM,IAAI,KAAK,cAAc,MAAM,GAAG,CAAC,GAAG;gCAC7E,UAAU;4BACZ;wBACF;wBACA,QAAQ,IAAM,aAAa;wBAC3B,WAAW;wBACX,aAAa;wBACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+LACA,sGACA;wBAEF,YAAY;4BAAE,OAAO;wBAAK;;;;;;kCAI5B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,cACI,wDACA;gCAEN,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAIpB,8OAAC,yLAAA,CAAA,kBAAe;0CACb,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,MAAM;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAC7B,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAK;wBACnC,SAAS;4BACP,SAAS,YAAY,IAAI;4BACzB,OAAO,YAAY,IAAI;wBACzB;wBACA,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;YAK/B,mBAAmB,wBAClB,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;2BAE5B,YAAY,MAAM,GAAG,kBACvB,8OAAC;oBAAI,WAAU;;wBACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;gCAEC,SAAS,IAAM,sBAAsB;gCACrC,WAAW,CAAC,8FAA8F,EACxG,kBAAkB,QAAQ,mBAAmB,IAC7C;;kDAGF,8OAAC;wCAAI,WAAU;kDACZ,AAAC,WAAW,SAAS,IAAI,WAAW,YAAY,iBAC/C,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,WAAW,SAAS,IAAI,WAAW,YAAY,IAAI;4CACxD,KAAK,WAAW,KAAK;4CACrB,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,WAAW;;;;;iEAGb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAMtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,WAAW,KAAK;;;;;;kEAEnB,8OAAC;wDAAK,WAAW,CAAC,iCAAiC,EACjD,WAAW,IAAI,KAAK,UAAU,iCAC9B,WAAW,IAAI,KAAK,WAAW,mCAC/B,oCACA;kEACC,WAAW,IAAI,KAAK,YAAY,OAAO,WAAW,IAAI,CAAC,WAAW;;;;;;;;;;;;4CAKtE,WAAW,IAAI,KAAK,2BACnB,8OAAC;gDAAE,WAAU;;oDACV,WAAW,WAAW;oDAAC;oDAAK,WAAW,MAAM;oDAAC;oDAAE,WAAW,OAAO;;;;;;;4CAKtE,WAAW,IAAI,KAAK,YAAY,WAAW,YAAY,kBACtD,8OAAC;gDAAE,WAAU;;oDACV,WAAW,YAAY;oDAAC;oDAAQ,WAAW,YAAY,GAAG,IAAI,MAAM;;;;;;;0DAKzE,8OAAC;gDAAI,WAAU;;oDACZ,WAAW,MAAM,kBAChB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAM,aAAa,WAAW,MAAM;;;;;;;;;;;;oDAGxC,CAAC,WAAW,WAAW,IAAI,WAAW,OAAO,mBAC5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,WAAW,WAAW,WAAW,IAAI,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;+BAhEjE,GAAG,WAAW,IAAI,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE;;;;;sCAyE/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM;gCACf,WAAU;;oCACX;oCACwB;oCAAM;;;;;;;;;;;;;;;;;2BAIjC,MAAM,IAAI,GAAG,MAAM,IAAI,kBACzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;;gCAAU;gCAAuB;gCAAM;;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;2BAE5B;;;;;;;;;;;;AAKd;uCAEe", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Search, Home, Film, Tv, Calendar, Plus, Play, X, Menu, Sparkles, TrendingUp } from 'lucide-react';\nimport { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport SearchBar from './SearchBar';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const { scrollY } = useScroll();\n\n  // Transform scroll position to background opacity\n  const backgroundOpacity = useTransform(scrollY, [0, 100], [0.8, 0.95]);\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Home, badge: null },\n    { href: '/movies', label: 'Movies', icon: Film, badge: null },\n    { href: '/series', label: 'Series', icon: Tv, badge: null },\n    { href: '/episodes', label: 'Episodes', icon: Calendar, badge: 'New' },\n  ];\n\n  return (\n    <motion.nav\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-500\",\n        isScrolled\n          ? \"backdrop-blur-2xl bg-black/95 border-b border-white/10 shadow-2xl\"\n          : \"backdrop-blur-xl bg-black/80 border-b border-white/5\"\n      )}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-4 sm:px-6 lg:px-12\">\n        <div className=\"flex items-center justify-between h-16 sm:h-20\">\n          {/* Enhanced Logo with Animation - Mobile Optimized */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 sm:space-x-4 focus-ring rounded-2xl px-2 sm:px-3 py-2 transition-all duration-300 hover:bg-white/5 group\">\n            <motion.div\n              className=\"relative\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-2xl transition-all duration-300 group-hover:shadow-red-500/25\">\n                <Play size={20} className=\"sm:w-6 sm:h-6 text-white fill-current ml-0.5\" />\n              </div>\n              <div className=\"absolute -inset-1 bg-gradient-to-br from-red-500/20 to-red-600/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-300\" />\n\n              {/* Floating sparkles */}\n              <motion.div\n                className=\"absolute -top-1 -right-1 w-3 h-3\"\n                animate={{\n                  rotate: [0, 360],\n                  scale: [1, 1.2, 1]\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n              >\n                <Sparkles size={12} className=\"text-yellow-400\" />\n              </motion.div>\n            </motion.div>\n\n            <div className=\"hidden xs:block\">\n              <motion.h1\n                className=\"text-white text-lg sm:text-xl md:text-2xl font-black tracking-tight\"\n                whileHover={{ scale: 1.02 }}\n              >\n                <span className=\"hidden sm:inline\">free</span><span className=\"text-red-500\">Movies</span><span className=\"hidden sm:inline\">WatchNow</span>\n              </motion.h1>\n              <p className=\"text-gray-400 text-xs sm:text-sm font-medium -mt-1 flex items-center space-x-1\">\n                <span className=\"hidden sm:inline\">Premium </span><span>Entertainment</span>\n                <TrendingUp size={10} className=\"sm:w-3 sm:h-3 text-green-400\" />\n              </p>\n            </div>\n          </Link>\n\n          {/* Enhanced Navigation Links */}\n          <div className=\"hidden lg:flex items-center space-x-2\">\n            {navItems.map((item, index) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n\n              return (\n                <motion.div\n                  key={item.href}\n                  initial={{ opacity: 0, y: -20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'relative flex items-center space-x-3 px-6 py-3 rounded-2xl transition-all duration-300 focus-ring group',\n                      isActive\n                        ? 'bg-gradient-to-r from-red-500/20 to-red-600/20 text-white shadow-lg border border-red-500/30 backdrop-blur-sm'\n                        : 'text-gray-300 hover:text-white hover:bg-white/10'\n                    )}\n                  >\n                    <Icon size={20} className={cn(\n                      'transition-all duration-300',\n                      isActive ? 'text-red-400' : 'group-hover:text-white'\n                    )} />\n                    <span className={cn(\n                      'text-base font-semibold tracking-wide transition-all duration-300',\n                      isActive ? 'text-white' : 'group-hover:text-white'\n                    )}>\n                      {item.label}\n                    </span>\n\n                    {/* Badge for new features */}\n                    {item.badge && (\n                      <motion.div\n                        className=\"px-2 py-0.5 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-bold rounded-full\"\n                        animate={{ scale: [1, 1.1, 1] }}\n                        transition={{ duration: 2, repeat: Infinity }}\n                      >\n                        {item.badge}\n                      </motion.div>\n                    )}\n\n                    {/* Enhanced Active Indicator */}\n                    {isActive && (\n                      <motion.div\n                        className=\"absolute bottom-0 left-1/2 -translate-x-1/2 w-1 h-1 bg-red-500 rounded-full shadow-lg shadow-red-500/50\"\n                        layoutId=\"activeIndicator\"\n                        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                      />\n                    )}\n\n                    {/* Hover glow effect */}\n                    <div className={cn(\n                      'absolute inset-0 rounded-2xl transition-all duration-300 pointer-events-none',\n                      isActive\n                        ? 'shadow-[inset_0_0_20px_rgba(239,68,68,0.1)]'\n                        : 'group-hover:shadow-[inset_0_0_20px_rgba(255,255,255,0.05)]'\n                    )} />\n                  </Link>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          {/* Ultra Premium Actions - Mobile Optimized */}\n          <div className=\"flex items-center space-x-2 sm:space-x-4\">\n            {/* Premium Search Button */}\n            <button\n              onClick={() => setIsSearchOpen(!isSearchOpen)}\n              className=\"p-2 sm:p-3 md:p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-xl sm:rounded-2xl transition-all duration-300 focus-ring group\"\n            >\n              {isSearchOpen ? (\n                <X size={20} className=\"sm:w-6 sm:h-6 group-hover:scale-110 transition-transform duration-300\" />\n              ) : (\n                <Search size={20} className=\"sm:w-6 sm:h-6 group-hover:scale-110 transition-transform duration-300\" />\n              )}\n            </button>\n\n            {/* Apple TV + Netflix Style Request Button */}\n            <Link\n              href=\"/request\"\n              className=\"hidden sm:flex items-center space-x-2 md:space-x-3 px-4 sm:px-6 md:px-8 py-2 sm:py-3 md:py-4 bg-red-600 rounded-xl sm:rounded-2xl text-white hover:bg-red-700 transition-all duration-300 focus-ring font-semibold shadow-2xl hover:scale-105\"\n            >\n              <Plus size={16} className=\"sm:w-5 sm:h-5\" />\n              <span className=\"text-sm sm:text-base md:text-lg\">Request</span>\n            </Link>\n\n            {/* Premium Mobile Menu Button */}\n            <button className=\"lg:hidden p-2 sm:p-3 md:p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-xl sm:rounded-2xl transition-all duration-300 focus-ring group\">\n              <div className=\"w-5 h-5 sm:w-6 sm:h-6 flex flex-col justify-center space-y-1 sm:space-y-1.5\">\n                <div className=\"w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4\" />\n                <div className=\"w-full h-0.5 bg-current rounded\" />\n                <div className=\"w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4 group-hover:ml-auto\" />\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Premium Mobile Navigation - Enhanced */}\n      <div className=\"lg:hidden border-t border-white/5 glass\">\n        <div className=\"flex items-center justify-around py-2 sm:py-4 px-1 sm:px-2\">\n          {navItems.map((item) => {\n            const Icon = item.icon;\n            const isActive = pathname === item.href;\n\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  'flex flex-col items-center space-y-1 sm:space-y-2 px-2 sm:px-4 py-2 sm:py-3 rounded-lg sm:rounded-xl transition-all duration-300 focus-ring min-w-0 flex-1 max-w-[80px] sm:max-w-none',\n                  isActive\n                    ? 'text-white bg-white/15'\n                    : 'text-gray-400 hover:text-white hover:bg-white/10'\n                )}\n              >\n                <Icon size={18} className=\"sm:w-5 sm:h-5 flex-shrink-0\" />\n                <span className=\"text-xs font-medium truncate w-full text-center\">{item.label}</span>\n                {item.badge && (\n                  <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1 rounded-full\">\n                    {item.badge}\n                  </span>\n                )}\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Enhanced Search Overlay */}\n      <AnimatePresence>\n        {isSearchOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.3, ease: \"easeOut\" }}\n            className=\"absolute top-full left-0 right-0 backdrop-blur-2xl bg-black/95 border-b border-white/10 shadow-2xl z-40\"\n          >\n            <div className=\"max-w-[2560px] mx-auto px-4 sm:px-6 lg:px-12 py-4 sm:py-6 md:py-8\">\n              <motion.div\n                initial={{ scale: 0.95, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                transition={{ delay: 0.1, duration: 0.3 }}\n                className=\"max-w-xl sm:max-w-2xl mx-auto\"\n              >\n                <SearchBar\n                  placeholder=\"Search movies, series, episodes...\"\n                  className=\"w-full\"\n                  onSearch={() => setIsSearchOpen(false)}\n                />\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,aAAuB;IAC3B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IAE5B,kDAAkD;IAClD,MAAM,oBAAoB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAI,EAAE;QAAC;QAAK;KAAK;IAErE,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,mMAAA,CAAA,OAAI;YAAE,OAAO;QAAK;QACpD;YAAE,MAAM;YAAW,OAAO;YAAU,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;QAAK;QAC5D;YAAE,MAAM;YAAW,OAAO;YAAU,MAAM,8LAAA,CAAA,KAAE;YAAE,OAAO;QAAK;QAC1D;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;QAAM;KACtE;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,sEACA;;0BAGN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,QAAQ;oDAAC;oDAAG;iDAAI;gDAChB,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CACpB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;sDAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;8DAAW,8OAAC;oDAAK,WAAU;8DAAe;;;;;;8DAAa,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAE/H,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;8DAAe,8OAAC;8DAAK;;;;;;8DACxD,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM;gCACnB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2GACA,WACI,kHACA;;0DAGN,8OAAC;gDAAK,MAAM;gDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC1B,+BACA,WAAW,iBAAiB;;;;;;0DAE9B,8OAAC;gDAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,qEACA,WAAW,eAAe;0DAEzB,KAAK,KAAK;;;;;;4CAIZ,KAAK,KAAK,kBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAE3C,KAAK,KAAK;;;;;;4CAKd,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,UAAS;gDACT,YAAY;oDAAE,MAAM;oDAAU,WAAW;oDAAK,SAAS;gDAAG;;;;;;0DAK9D,8OAAC;gDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gFACA,WACI,gDACA;;;;;;;;;;;;mCAlDH,KAAK,IAAI;;;;;4BAuDpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;8CAET,6BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;wCAAI,WAAU;;;;;6DAEvB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAKhC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAIpD,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yLACA,WACI,2BACA;;8CAGN,8OAAC;oCAAK,MAAM;oCAAI,WAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAmD,KAAK,KAAK;;;;;;gCAC5E,KAAK,KAAK,kBACT,8OAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;;2BAbV,KAAK,IAAI;;;;;oBAkBpB;;;;;;;;;;;0BAKJ,8OAAC,yLAAA,CAAA,kBAAe;0BACb,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;oBAC7C,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAM,SAAS;4BAAE;4BACnC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;sCAEV,cAAA,8OAAC,+HAAA,CAAA,UAAS;gCACR,aAAY;gCACZ,WAAU;gCACV,UAAU,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;uCAEe", "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef } from 'react';\nimport <PERSON> from 'next/link';\nimport { Github, Twitter, Instagram, Youtube, Mail, Heart, Zap, Play, Sparkles, TrendingUp, Star, Globe } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n  const footerRef = useRef<HTMLElement>(null);\n  const isInView = useInView(footerRef, { once: true, margin: \"-100px\" });\n\n  const footerLinks = {\n    platform: [\n      { label: 'Movies', href: '/movies' },\n      { label: 'TV Series', href: '/series' },\n      { label: 'Latest Episodes', href: '/episodes' },\n      { label: 'Search', href: '/search' }\n    ],\n    genres: [\n      { label: 'Action Movies', href: '/movies?genre=Action' },\n      { label: 'Comedy Series', href: '/series?genre=Comedy' },\n      { label: 'Horror Movies', href: '/movies?genre=Horror' },\n      { label: 'Drama Series', href: '/series?genre=Drama' }\n    ],\n    popular: [\n      { label: 'Latest Movies', href: '/movies?sortBy=createdAt&sortOrder=desc' },\n      { label: 'Top Rated', href: '/movies?sortBy=imdbRating&sortOrder=desc' },\n      { label: 'New Series', href: '/series?sortBy=createdAt&sortOrder=desc' },\n      { label: 'Trending Now', href: '/movies?sortBy=popularity&sortOrder=desc' }\n    ]\n  };\n\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com', label: 'GitHub' },\n    { icon: Twitter, href: 'https://twitter.com', label: 'Twitter' },\n    { icon: Instagram, href: 'https://instagram.com', label: 'Instagram' },\n    { icon: Youtube, href: 'https://youtube.com', label: 'YouTube' }\n  ];\n\n  return (\n    <footer\n      ref={footerRef}\n      className=\"relative bg-gradient-to-b from-black via-gray-900/50 to-black border-t border-white/10\"\n    >\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={isInView ? { opacity: 1 } : { opacity: 0 }}\n        transition={{ duration: 0.8 }}\n        className=\"relative\"\n      >\n        {/* Enhanced Background Effects */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute -top-40 -left-40 w-80 h-80 bg-blue-600/10 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.1, 0.2, 0.1]\n          }}\n          transition={{ duration: 8, repeat: Infinity, ease: \"easeInOut\" }}\n        />\n        <motion.div\n          className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600/10 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.2, 1, 1.2],\n            opacity: [0.2, 0.1, 0.2]\n          }}\n          transition={{ duration: 10, repeat: Infinity, ease: \"easeInOut\" }}\n        />\n        <motion.div\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-red-600/5 rounded-full blur-3xl\"\n          animate={{\n            rotate: [0, 360],\n            scale: [1, 1.1, 1]\n          }}\n          transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n        />\n\n        {/* Floating particles */}\n        {[...Array(12)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white/20 rounded-full\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              y: [0, -20, 0],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative max-w-[2560px] mx-auto px-4 sm:px-6 lg:px-12\">\n        {/* Enhanced Main Footer Content */}\n        <div className=\"py-8 sm:py-12 md:py-16 lg:py-20\">\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12 lg:gap-16\">\n            {/* Enhanced Brand Section */}\n            <motion.div\n              className=\"sm:col-span-2 lg:col-span-1\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n            >\n              <div className=\"flex items-center space-x-3 sm:space-x-4 mb-6 sm:mb-8\">\n                <motion.div\n                  className=\"relative group\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className=\"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-2xl\">\n                    <Play size={24} className=\"sm:w-7 sm:h-7 text-white fill-current ml-0.5 sm:ml-1\" />\n                  </div>\n                  <div className=\"absolute -inset-1 bg-gradient-to-br from-red-500/30 to-red-600/30 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-300\" />\n\n                  {/* Floating sparkles */}\n                  <motion.div\n                    className=\"absolute -top-1 -right-1 w-3 h-3\"\n                    animate={{\n                      rotate: [0, 360],\n                      scale: [1, 1.2, 1]\n                    }}\n                    transition={{\n                      duration: 3,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }}\n                  >\n                    <Sparkles size={12} className=\"text-yellow-400\" />\n                  </motion.div>\n                </motion.div>\n                <div>\n                  <motion.h3\n                    className=\"text-white text-3xl font-black tracking-tight\"\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    free<span className=\"text-red-500\">Movies</span>WatchNow\n                  </motion.h3>\n                  <p className=\"text-gray-400 text-sm font-medium flex items-center space-x-1\">\n                    <span>Premium Entertainment</span>\n                    <TrendingUp size={12} className=\"text-green-400\" />\n                  </p>\n                </div>\n              </div>\n              \n              <motion.p\n                className=\"text-gray-400 text-lg leading-relaxed mb-8\"\n                initial={{ opacity: 0 }}\n                animate={isInView ? { opacity: 1 } : { opacity: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n              >\n                Experience the future of streaming with our premium platform. Unlimited movies, series, and episodes at your fingertips with crystal-clear quality and lightning-fast loading.\n              </motion.p>\n\n              {/* Enhanced Social Links */}\n              <motion.div\n                className=\"flex items-center space-x-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n                transition={{ duration: 0.6, delay: 0.6 }}\n              >\n                {socialLinks.map((social, index) => {\n                  const Icon = social.icon;\n                  return (\n                    <motion.a\n                      key={social.label}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-12 h-12 bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-white/10 hover:border-white/20 transition-all duration-300 focus-ring group\"\n                      whileHover={{ scale: 1.1, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n                      transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}\n                    >\n                      <Icon size={20} className=\"group-hover:scale-110 transition-transform duration-300\" />\n                    </motion.a>\n                  );\n                })}\n              </motion.div>\n            </motion.div>\n\n            {/* Links Sections */}\n            <div className=\"lg:col-span-3\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                {/* Platform Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6 flex items-center\">\n                    <Zap size={20} className=\"mr-2 text-blue-400\" />\n                    Browse\n                  </h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.platform.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Genres Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6\">Popular Genres</h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.genres.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Popular Content Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6\">Trending</h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.popular.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* SEO Content Section */}\n        <div className=\"py-12 border-t border-white/10\">\n          <div className=\"text-center\">\n            <h4 className=\"text-white text-2xl font-bold mb-4\">Watch Movies and TV Series Online Free</h4>\n            <p className=\"text-gray-400 text-lg max-w-4xl mx-auto leading-relaxed\">\n              freeMoviesWatchNow is your ultimate destination for watching movies and TV series online free in HD quality.\n              Discover thousands of movies, binge-watch your favorite series, and catch up on the latest episodes.\n              Our platform offers a premium streaming experience with multiple sources and high-quality content\n              from various genres including action, comedy, drama, horror, thriller, romance, and more.\n            </p>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0\">\n            <div className=\"flex items-center space-x-2 text-gray-400 text-lg\">\n              <span>© {currentYear} freeMoviesWatchNow. Made with</span>\n              <Heart size={16} className=\"text-red-500 animate-pulse\" />\n              <span>for entertainment lovers.</span>\n            </div>\n            \n            <div className=\"flex items-center space-x-8 text-gray-400\">\n              <span className=\"text-lg\">All rights reserved</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\" />\n                <span className=\"text-lg font-medium\">All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      </motion.div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACtC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;QAAE,MAAM;QAAM,QAAQ;IAAS;IAErE,MAAM,cAAc;QAClB,UAAU;YACR;gBAAE,OAAO;gBAAU,MAAM;YAAU;YACnC;gBAAE,OAAO;gBAAa,MAAM;YAAU;YACtC;gBAAE,OAAO;gBAAmB,MAAM;YAAY;YAC9C;gBAAE,OAAO;gBAAU,MAAM;YAAU;SACpC;QACD,QAAQ;YACN;gBAAE,OAAO;gBAAiB,MAAM;YAAuB;YACvD;gBAAE,OAAO;gBAAiB,MAAM;YAAuB;YACvD;gBAAE,OAAO;gBAAiB,MAAM;YAAuB;YACvD;gBAAE,OAAO;gBAAgB,MAAM;YAAsB;SACtD;QACD,SAAS;YACP;gBAAE,OAAO;gBAAiB,MAAM;YAA0C;YAC1E;gBAAE,OAAO;gBAAa,MAAM;YAA2C;YACvE;gBAAE,OAAO;gBAAc,MAAM;YAA0C;YACvE;gBAAE,OAAO;gBAAgB,MAAM;YAA2C;SAC3E;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,MAAM;YAAsB,OAAO;QAAS;QAC5D;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAuB,OAAO;QAAU;QAC/D;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAyB,OAAO;QAAY;QACrE;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAuB,OAAO;QAAU;KAChE;IAED,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;kBAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS,WAAW;gBAAE,SAAS;YAAE,IAAI;gBAAE,SAAS;YAAE;YAClD,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAGV,8OAAC;oBAAI,WAAU;;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;gCAClB,SAAS;oCAAC;oCAAK;oCAAK;iCAAI;4BAC1B;4BACA,YAAY;gCAAE,UAAU;gCAAG,QAAQ;gCAAU,MAAM;4BAAY;;;;;;sCAEjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,OAAO;oCAAC;oCAAK;oCAAG;iCAAI;gCACpB,SAAS;oCAAC;oCAAK;oCAAK;iCAAI;4BAC1B;4BACA,YAAY;gCAAE,UAAU;gCAAI,QAAQ;gCAAU,MAAM;4BAAY;;;;;;sCAElE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,QAAQ;oCAAC;oCAAG;iCAAI;gCAChB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCAAE,UAAU;gCAAI,QAAQ;gCAAU,MAAM;4BAAS;;;;;;wBAI9D;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAChC;gCACA,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,SAAS;wCAAC;wCAAG;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU,IAAI,KAAK,MAAM,KAAK;oCAC9B,QAAQ;oCACR,OAAO,KAAK,MAAM,KAAK;gCACzB;+BAdK;;;;;;;;;;;8BAmBX,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS,WAAW;4CAAE,SAAS;4CAAG,GAAG;wCAAE,IAAI;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC/D,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;;;;;;;0EAE5B,8OAAC;gEAAI,WAAU;;;;;;0EAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,SAAS;oEACP,QAAQ;wEAAC;wEAAG;qEAAI;oEAChB,OAAO;wEAAC;wEAAG;wEAAK;qEAAE;gEACpB;gEACA,YAAY;oEACV,UAAU;oEACV,QAAQ;oEACR,MAAM;gEACR;0EAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,MAAM;oEAAI,WAAU;;;;;;;;;;;;;;;;;kEAGlC,8OAAC;;0EACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gEACR,WAAU;gEACV,YAAY;oEAAE,OAAO;gEAAK;;oEAC3B;kFACK,8OAAC;wEAAK,WAAU;kFAAe;;;;;;oEAAa;;;;;;;0EAElD,8OAAC;gEAAE,WAAU;;kFACX,8OAAC;kFAAK;;;;;;kFACN,8OAAC,kNAAA,CAAA,aAAU;wEAAC,MAAM;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAKtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS,WAAW;oDAAE,SAAS;gDAAE,IAAI;oDAAE,SAAS;gDAAE;gDAClD,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;0DACzC;;;;;;0DAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS,WAAW;oDAAE,SAAS;oDAAG,GAAG;gDAAE,IAAI;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC/D,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;0DAEvC,YAAY,GAAG,CAAC,CAAC,QAAQ;oDACxB,MAAM,OAAO,OAAO,IAAI;oDACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDAEP,MAAM,OAAO,IAAI;wDACjB,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;4DAAK,GAAG,CAAC;wDAAE;wDAChC,UAAU;4DAAE,OAAO;wDAAK;wDACxB,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS,WAAW;4DAAE,SAAS;4DAAG,GAAG;wDAAE,IAAI;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC/D,YAAY;4DAAE,UAAU;4DAAK,OAAO,MAAM,QAAQ;wDAAI;kEAEtD,cAAA,8OAAC;4DAAK,MAAM;4DAAI,WAAU;;;;;;uDAXrB,OAAO,KAAK;;;;;gDAcvB;;;;;;;;;;;;kDAKJ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,gMAAA,CAAA,MAAG;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAuB;;;;;;;sEAGlD,8OAAC;4DAAG,WAAU;sEACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,KAAK,IAAI;wEACf,WAAU;kFAET,KAAK,KAAK;;;;;;mEALN,KAAK,IAAI;;;;;;;;;;;;;;;;8DAaxB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAG,WAAU;sEACX,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,KAAK,IAAI;wEACf,WAAU;kFAET,KAAK,KAAK;;;;;;mEALN,KAAK,IAAI;;;;;;;;;;;;;;;;8DAaxB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAG,WAAU;sEACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,KAAK,IAAI;wEACf,WAAU;kFAET,KAAK,KAAK;;;;;;mEALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAiBhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAA0D;;;;;;;;;;;;;;;;;sCAU3E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAK;oDAAG;oDAAY;;;;;;;0DACrB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC3B,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;uCAEe", "debugId": null}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/UniversalSyncInitializer.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface SyncStatus {\n  isRunning: boolean;\n  lastSyncTime: string | null;\n  nextSyncTime: string | null;\n  syncType: string;\n}\n\ninterface InitStatus {\n  initialized: boolean;\n  error: string | null;\n  status: SyncStatus | null;\n  utcTime: string | null;\n}\n\nexport default function UniversalSyncInitializer() {\n  const [initStatus, setInitStatus] = useState<InitStatus>({\n    initialized: false,\n    error: null,\n    status: null,\n    utcTime: null\n  });\n\n  useEffect(() => {\n    const initializeUniversalSync = async () => {\n      try {\n        console.log('🌍 Initializing Universal Sync Service...');\n        \n        const response = await fetch('/api/admin/init-universal-sync', {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n        });\n\n        const result = await response.json();\n\n        if (result.success) {\n          console.log('✅ Universal Sync Service initialized:', result.message);\n          console.log('🕐 UTC Time:', result.utcTime);\n          console.log('📅 Next Sync:', result.status?.nextSyncTime);\n          \n          setInitStatus({\n            initialized: true,\n            error: null,\n            status: result.status,\n            utcTime: result.utcTime\n          });\n        } else {\n          console.error('❌ Failed to initialize Universal Sync Service:', result.message);\n          setInitStatus({\n            initialized: false,\n            error: result.message || 'Unknown error',\n            status: null,\n            utcTime: result.utcTime\n          });\n        }\n      } catch (error) {\n        console.error('❌ Error initializing Universal Sync Service:', error);\n        setInitStatus({\n          initialized: false,\n          error: error.message || 'Network error',\n          status: null,\n          utcTime: new Date().toISOString()\n        });\n      }\n    };\n\n    // Initialize on component mount\n    initializeUniversalSync();\n\n    // Set up periodic status checks every 5 minutes\n    const statusInterval = setInterval(async () => {\n      try {\n        const response = await fetch('/api/sync/universal?action=status');\n        const result = await response.json();\n        \n        if (result.success && result.data) {\n          setInitStatus(prev => ({\n            ...prev,\n            status: {\n              isRunning: result.data.isRunning,\n              lastSyncTime: result.data.lastSyncTime,\n              nextSyncTime: result.data.nextSyncTime,\n              syncType: result.data.syncType\n            },\n            utcTime: result.utcTime\n          }));\n        }\n      } catch (error) {\n        console.error('❌ Error checking sync status:', error);\n      }\n    }, 5 * 60 * 1000); // 5 minutes\n\n    return () => {\n      clearInterval(statusInterval);\n    };\n  }, []);\n\n  // Format time for display\n  const formatTime = (timeString: string | null) => {\n    if (!timeString) return 'Never';\n    return new Date(timeString).toLocaleString('en-US', {\n      timeZone: 'UTC',\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit',\n      timeZoneName: 'short'\n    });\n  };\n\n  // Calculate time until next sync\n  const getTimeUntilNextSync = () => {\n    if (!initStatus.status?.nextSyncTime) return 'Unknown';\n    \n    const now = new Date();\n    const nextSync = new Date(initStatus.status.nextSyncTime);\n    const diff = nextSync.getTime() - now.getTime();\n    \n    if (diff <= 0) return 'Due now';\n    \n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n    \n    return `${hours}h ${minutes}m`;\n  };\n\n  // This component doesn't render anything visible, but shows status in console\n  // You can uncomment the JSX below if you want a visible status indicator\n\n  return null;\n\n  /* Uncomment this section if you want a visible status indicator\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50\">\n      <div className=\"bg-black/80 backdrop-blur-sm rounded-lg border border-gray-700 p-4 text-white text-xs max-w-sm\">\n        <div className=\"flex items-center space-x-2 mb-2\">\n          <div className={`w-2 h-2 rounded-full ${\n            initStatus.initialized \n              ? (initStatus.status?.isRunning ? 'bg-yellow-400 animate-pulse' : 'bg-green-400')\n              : 'bg-red-400'\n          }`} />\n          <span className=\"font-semibold\">Universal Sync</span>\n        </div>\n        \n        {initStatus.error ? (\n          <div className=\"text-red-400\">Error: {initStatus.error}</div>\n        ) : (\n          <div className=\"space-y-1\">\n            <div>Status: {initStatus.status?.isRunning ? 'Running' : 'Scheduled'}</div>\n            <div>Last: {formatTime(initStatus.status?.lastSyncTime)}</div>\n            <div>Next: {getTimeUntilNextSync()}</div>\n            <div>UTC: {formatTime(initStatus.utcTime)}</div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n  */\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAkBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,aAAa;QACb,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,0BAA0B;YAC9B,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,MAAM,WAAW,MAAM,MAAM,kCAAkC;oBAC7D,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,GAAG,CAAC,yCAAyC,OAAO,OAAO;oBACnE,QAAQ,GAAG,CAAC,gBAAgB,OAAO,OAAO;oBAC1C,QAAQ,GAAG,CAAC,iBAAiB,OAAO,MAAM,EAAE;oBAE5C,cAAc;wBACZ,aAAa;wBACb,OAAO;wBACP,QAAQ,OAAO,MAAM;wBACrB,SAAS,OAAO,OAAO;oBACzB;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,kDAAkD,OAAO,OAAO;oBAC9E,cAAc;wBACZ,aAAa;wBACb,OAAO,OAAO,OAAO,IAAI;wBACzB,QAAQ;wBACR,SAAS,OAAO,OAAO;oBACzB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,cAAc;oBACZ,aAAa;oBACb,OAAO,MAAM,OAAO,IAAI;oBACxB,QAAQ;oBACR,SAAS,IAAI,OAAO,WAAW;gBACjC;YACF;QACF;QAEA,gCAAgC;QAChC;QAEA,gDAAgD;QAChD,MAAM,iBAAiB,YAAY;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,cAAc,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,QAAQ;gCACN,WAAW,OAAO,IAAI,CAAC,SAAS;gCAChC,cAAc,OAAO,IAAI,CAAC,YAAY;gCACtC,cAAc,OAAO,IAAI,CAAC,YAAY;gCACtC,UAAU,OAAO,IAAI,CAAC,QAAQ;4BAChC;4BACA,SAAS,OAAO,OAAO;wBACzB,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF,GAAG,IAAI,KAAK,OAAO,YAAY;QAE/B,OAAO;YACL,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;YAClD,UAAU;YACV,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,cAAc;QAChB;IACF;IAEA,iCAAiC;IACjC,MAAM,uBAAuB;QAC3B,IAAI,CAAC,WAAW,MAAM,EAAE,cAAc,OAAO;QAE7C,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,KAAK,WAAW,MAAM,CAAC,YAAY;QACxD,MAAM,OAAO,SAAS,OAAO,KAAK,IAAI,OAAO;QAE7C,IAAI,QAAQ,GAAG,OAAO;QAEtB,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;QAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;QAEjE,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;IAChC;IAEA,8EAA8E;IAC9E,yEAAyE;IAEzE,OAAO;AAEP;;;;;;;;;;;;;;;;;;;;;;;;;;EA0BA,GACF", "debugId": null}}]}