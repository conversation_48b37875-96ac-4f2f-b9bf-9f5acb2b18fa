import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';

/**
 * Test the episodes page query to see what's failing
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await connectDB();

    console.log('🔍 Testing episodes page query...');

    // Test the series match stage
    const seriesMatchStage = {
      'episodes.isLatestRelease': true
    };

    console.log('🔍 Series match stage:', seriesMatchStage);

    // Test simple find
    const seriesCount = await Series.countDocuments(seriesMatchStage);
    console.log(`📊 Series matching query: ${seriesCount}`);

    if (seriesCount === 0) {
      return NextResponse.json({
        error: 'No series found with isLatestRelease episodes',
        seriesMatchStage
      });
    }

    // Get a few series for testing
    const testSeries = await Series.find(seriesMatchStage).limit(3).lean();
    console.log(`📝 Found ${testSeries.length} test series`);

    // Test the aggregation pipeline
    const aggregationPipeline = [
      // Match series with VidSrc latest episodes
      { $match: seriesMatchStage },
      // Unwind episodes array to work with individual episodes
      { $unwind: '$episodes' },
      // Filter to only VidSrc latest episodes
      { 
        $match: { 
          'episodes.isLatestRelease': true 
        } 
      },
      // Sort by episode creation date (newest first)
      {
        $sort: {
          'episodes.createdAt': -1
        }
      },
      // Group by series to get the latest VidSrc episode per series
      {
        $group: {
          _id: '$imdbId',
          series: { $first: '$$ROOT' },
          latestEpisode: { $first: '$episodes' }
        }
      },
      // Project the final episode structure
      {
        $project: {
          _id: '$latestEpisode._id',
          imdbId: '$_id',
          seriesTitle: '$series.title',
          season: '$latestEpisode.season',
          episode: '$latestEpisode.episode',
          episodeTitle: '$latestEpisode.episodeTitle',
          isLatestRelease: '$latestEpisode.isLatestRelease'
        }
      },
      // Limit for testing
      { $limit: 10 }
    ];

    console.log('🔄 Testing aggregation pipeline...');
    const aggregationResult = await Series.aggregate(aggregationPipeline);
    console.log(`✅ Aggregation returned ${aggregationResult.length} episodes`);

    return NextResponse.json({
      success: true,
      stats: {
        seriesCount,
        testSeriesFound: testSeries.length,
        aggregationResults: aggregationResult.length
      },
      sampleSeries: testSeries.map(s => ({
        title: s.title,
        imdbId: s.imdbId,
        episodeCount: s.episodes?.length || 0,
        latestEpisodes: s.episodes?.filter(ep => ep.isLatestRelease).length || 0
      })),
      sampleEpisodes: aggregationResult.slice(0, 3)
    });

  } catch (error) {
    console.error('❌ Test episodes query error:', error);
    return NextResponse.json(
      { error: 'Test failed', message: error.message },
      { status: 500 }
    );
  }
}
