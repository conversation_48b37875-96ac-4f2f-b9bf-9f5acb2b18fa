import axios from 'axios';
import * as cheerio from 'cheerio';

export interface ScrapedMovieData {
  title: string;
  year: number;
  rating?: string;
  runtime?: string;
  imdbRating?: number;
  imdbVotes?: string;
  popularity?: number;
  popularityDelta?: number;
  posterUrl?: string;
  backdropUrl?: string;
  trailerUrl?: string;
  trailerRuntime?: string;
  trailerLikes?: string;
  description?: string;
  genres?: string[];
  director?: string;
  cast?: string[];
  language?: string;
  country?: string;
}

export interface ScrapedSeriesData {
  title: string;
  startYear: number;
  endYear?: number;
  rating?: string;
  imdbRating?: number;
  imdbVotes?: string;
  popularity?: number;
  popularityDelta?: number;
  posterUrl?: string;
  backdropUrl?: string;
  trailerUrl?: string;
  description?: string;
  genres?: string[];
  creator?: string;
  cast?: string[];
  language?: string;
  country?: string;
  totalSeasons?: number;
  status?: string;
}

class IMDbScraper {
  private static instance: IMDbScraper;
  private requestCount = 0;
  private lastRequestTime = 0;
  private readonly RATE_LIMIT = 30; // requests per minute
  private readonly MIN_DELAY = 2000; // 2 seconds between requests

  static getInstance(): IMDbScraper {
    if (!IMDbScraper.instance) {
      IMDbScraper.instance = new IMDbScraper();
    }
    return IMDbScraper.instance;
  }

  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.MIN_DELAY) {
      const delay = this.MIN_DELAY - timeSinceLastRequest;
      // Add random jitter to avoid detection patterns
      const jitter = Math.random() * 1000; // 0-1000ms random delay
      await new Promise(resolve => setTimeout(resolve, delay + jitter));
    }

    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  private getRandomUserAgent(): string {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  }

  private async fetchPage(imdbId: string): Promise<cheerio.CheerioAPI> {
    await this.rateLimit();

    const url = `https://www.imdb.com/title/${imdbId}/`;
    const headers = {
      'User-Agent': this.getRandomUserAgent(),
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
    };

    try {
      const response = await axios.get(url, { headers, timeout: 30000 });
      return cheerio.load(response.data);
    } catch (error) {
      console.error(`Error fetching IMDb page for ${imdbId}:`, error);
      throw new Error(`Failed to fetch IMDb page: ${error.message}`);
    }
  }

  private extractBasicInfo($: cheerio.CheerioAPI): { title: string; year: number; type: 'movie' | 'series' } {
    const titleElement = $('h1[data-testid="hero__pageTitle"] span[data-testid="hero__primary-text"]');
    const title = titleElement.text().trim();
    
    if (!title) {
      throw new Error('Could not extract title from IMDb page');
    }

    // Extract year from the release info
    const yearElement = $('ul.ipc-inline-list a[href*="/releaseinfo/"]');
    const yearText = yearElement.text().trim();
    const year = parseInt(yearText) || new Date().getFullYear();

    // Determine if it's a movie or series
    const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();
    const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');
    
    return {
      title,
      year,
      type: isMovie ? 'movie' : 'series'
    };
  }

  private extractRating($: cheerio.CheerioAPI): string | undefined {
    const ratingElement = $('ul.ipc-inline-list a[href*="/parentalguide/"]');
    return ratingElement.text().trim() || undefined;
  }

  private extractRuntime($: cheerio.CheerioAPI): string | undefined {
    const runtimeElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < runtimeElements.length; i++) {
      const text = $(runtimeElements[i]).text().trim();
      if (text.includes('h') || text.includes('min')) {
        return text;
      }
    }
    return undefined;
  }

  private extractIMDbRating($: cheerio.CheerioAPI): { rating?: number; votes?: string } {
    const ratingElement = $('div[data-testid="hero-rating-bar__aggregate-rating__score"] span');
    const rating = parseFloat(ratingElement.text().trim()) || undefined;
    
    const votesElement = $('div.sc-d541859f-3');
    const votes = votesElement.text().trim() || undefined;
    
    return { rating, votes };
  }

  private extractPopularity($: cheerio.CheerioAPI): { popularity?: number; delta?: number } {
    const popularityElement = $('div[data-testid="hero-rating-bar__popularity__score"]');
    const popularity = parseInt(popularityElement.text().trim()) || undefined;
    
    const deltaElement = $('div[data-testid="hero-rating-bar__popularity__delta"]');
    const deltaText = deltaElement.text().trim();
    const delta = deltaText ? parseInt(deltaText.replace(/[^\d-]/g, '')) : undefined;
    
    return { popularity, delta };
  }

  private extractPosterUrl($: cheerio.CheerioAPI): string | undefined {
    // Try multiple selectors for poster image
    const selectors = [
      'div[data-testid="hero-media__poster"] img',
      '.ipc-image[data-testid="hero-media__poster"]',
      '.poster img',
      '.ipc-media img',
      'img[class*="poster"]',
      'a[class*="ipc-lockup-overlay"] img'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const src = element.attr('src');
      if (src && src.includes('media-amazon.com')) {
        // Clean up the URL to get high quality image
        return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1000_.').replace(/\._.*?\./, '._V1_FMjpg_UX1000_.');
      }
    }

    return undefined;
  }

  private extractBackdropUrl($: cheerio.CheerioAPI): string | undefined {
    // Try to extract backdrop/hero image
    const selectors = [
      '.hero-media__slate-overlay img',
      '.slate img',
      '.hero__background img',
      'div[data-testid="hero-media"] img'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const src = element.attr('src');
      if (src && src.includes('media-amazon.com')) {
        return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1920_.');
      }
    }

    return undefined;
  }

  private extractTrailerInfo($: cheerio.CheerioAPI): { url?: string; runtime?: string; likes?: string } {
    const trailerElement = $('a[data-testid="video-player-slate-overlay"]');
    const url = trailerElement.attr('href') || undefined;
    
    const runtimeElement = $('span[data-testid="video-player-slate-runtime"]');
    const runtime = runtimeElement.text().trim() || undefined;
    
    const likesElement = $('span.ipc-reaction-summary__label');
    const likes = likesElement.text().trim() || undefined;
    
    return { url, runtime, likes };
  }

  private extractDescription($: cheerio.CheerioAPI): string | undefined {
    // Try multiple selectors for description
    const selectors = [
      'div[data-testid="hero-media__slate"] img',
      'span[data-testid="plot-xl"]',
      'span[data-testid="plot-l"]',
      'span[data-testid="plot"]'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      if (selector.includes('img')) {
        const alt = element.attr('alt');
        if (alt && alt.length > 20) return alt;
      } else {
        const text = element.text().trim();
        if (text && text.length > 10) return text;
      }
    }

    return undefined;
  }

  private extractGenres($: cheerio.CheerioAPI): string[] {
    const genres: string[] = [];

    // Updated genre selectors based on current IMDb structure
    const genreSelectors = [
      // Current IMDb hero section genres (most common)
      '[data-testid="genres"] .ipc-chip .ipc-chip__text',
      '[data-testid="genres"] .ipc-chip__text',
      '[data-testid="genres"] a .ipc-chip__text',

      // Hero section alternative formats
      '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',
      '.GenresAndPlot__GenreChip .ipc-chip__text',

      // Storyline section (your provided structure - keep as fallback)
      'li[data-testid="storyline-genres"] .ipc-metadata-list-item__list-content-item',
      'li[data-testid="storyline-genres"] a[href*="genres="]',

      // General genre link selectors (broader search)
      'a[href*="/search/title/?genres="] span',
      'a[href*="genres="] span',
      'a[href*="genres="]',

      // Schema.org microdata
      '[itemprop="genre"]',
      'span[itemprop="genre"]',

      // Fallback selectors
      '.see-more.inline.canwrap a[href*="genres="]',
      '.titlePageSprite.star-box-giga-star + div a[href*="genres="]',

      // Very broad fallback - any link with genre in URL
      'a[href*="explore=genres"]'
    ];

    console.log(`🔍 Starting genre extraction...`);

    for (let i = 0; i < genreSelectors.length; i++) {
      const selector = genreSelectors[i];
      const elements = $(selector);
      console.log(`🔍 Selector ${i + 1}/${genreSelectors.length}: "${selector}" found ${elements.length} elements`);

      if (elements.length > 0) {
        elements.each((_, element) => {
          const genre = $(element).text().trim();
          console.log(`📝 Found genre text: "${genre}"`);

          // Clean up genre text and validate
          if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {
            // Skip common non-genre text
            const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];
            if (!skipTexts.includes(genre)) {
              genres.push(genre);
              console.log(`✅ Added genre: "${genre}"`);
            }
          }
        });

        if (genres.length > 0) {
          console.log(`✅ Successfully extracted ${genres.length} genres: [${genres.join(', ')}]`);
          break; // Use the first selector that finds results
        }
      }
    }

    if (genres.length === 0) {
      console.log('⚠️ No genres found with any selector');
      // Debug: Let's see what's actually in the storyline section
      const storylineSection = $('li[data-testid="storyline-genres"]');
      if (storylineSection.length > 0) {
        console.log('📋 Storyline section HTML:', storylineSection.html());
      } else {
        console.log('❌ No storyline-genres section found');
      }
    }

    return genres;
  }

  private extractCast($: cheerio.CheerioAPI): string[] {
    const cast: string[] = [];

    // Try multiple selectors for cast
    const castSelectors = [
      'section[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
      '.cast_list .primary_photo + td a',
      '.titleCast .primary_photo + td a',
      'div[data-testid="title-cast-item"] a[href*="/name/"]'
    ];

    for (const selector of castSelectors) {
      const elements = $(selector);
      if (elements.length > 0) {
        elements.each((_, element) => {
          const actorName = $(element).text().trim();
          if (actorName && !cast.includes(actorName) && cast.length < 10) { // Limit to top 10 cast members
            cast.push(actorName);
          }
        });
        break; // Use the first selector that finds results
      }
    }

    return cast;
  }

  private extractDirector($: cheerio.CheerioAPI): string | undefined {
    // Try multiple selectors for director
    const directorSelectors = [
      'li[data-testid="title-pc-principal-credit"]:contains("Director") .ipc-metadata-list-item__list-content-item',
      'li[data-testid="title-pc-principal-credit"]:contains("Directors") .ipc-metadata-list-item__list-content-item',
      'a[href*="/name/"][href*="ref_=tt_ov_dr"]',
      '.credit_summary_item:contains("Director") a'
    ];

    for (const selector of directorSelectors) {
      const element = $(selector).first();
      const director = element.text().trim();
      if (director) {
        return director;
      }
    }

    return undefined;
  }

  private extractCreator($: cheerio.CheerioAPI): string | undefined {
    // Try multiple selectors for creator (for TV series)
    const creatorSelectors = [
      'li[data-testid="title-pc-principal-credit"]:contains("Creator") .ipc-metadata-list-item__list-content-item',
      'li[data-testid="title-pc-principal-credit"]:contains("Creators") .ipc-metadata-list-item__list-content-item',
      '.credit_summary_item:contains("Creator") a',
      '.credit_summary_item:contains("Created by") a'
    ];

    for (const selector of creatorSelectors) {
      const element = $(selector).first();
      const creator = element.text().trim();
      if (creator) {
        return creator;
      }
    }

    return undefined;
  }

  private extractLanguage($: cheerio.CheerioAPI): string | undefined {
    // Try multiple selectors for language
    const languageSelectors = [
      'li[data-testid="title-details-languages"] .ipc-metadata-list-item__list-content-item',
      'div[data-testid="title-details-section"] li:contains("Language") .ipc-metadata-list-item__list-content-item',
      'a[href*="primary_language="]',
      '.txt-block:contains("Language") a'
    ];

    for (const selector of languageSelectors) {
      const element = $(selector).first();
      const language = element.text().trim();
      if (language) {
        return language;
      }
    }

    return undefined;
  }

  private extractCountry($: cheerio.CheerioAPI): string | undefined {
    // Try multiple selectors for country
    const countrySelectors = [
      'li[data-testid="title-details-origin"] .ipc-metadata-list-item__list-content-item',
      'div[data-testid="title-details-section"] li:contains("Country") .ipc-metadata-list-item__list-content-item',
      'a[href*="country_of_origin="]',
      '.txt-block:contains("Country") a'
    ];

    for (const selector of countrySelectors) {
      const element = $(selector).first();
      const country = element.text().trim();
      if (country) {
        return country;
      }
    }

    return undefined;
  }

  async scrapeMovie(imdbId: string): Promise<ScrapedMovieData> {
    const $ = await this.fetchPage(imdbId);
    const basicInfo = this.extractBasicInfo($);

    if (basicInfo.type !== 'movie') {
      throw new Error('IMDb ID does not correspond to a movie');
    }

    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
    const { popularity, delta: popularityDelta } = this.extractPopularity($);
    const { url: trailerUrl, runtime: trailerRuntime, likes: trailerLikes } = this.extractTrailerInfo($);

    return {
      title: basicInfo.title,
      year: basicInfo.year,
      rating: this.extractRating($),
      runtime: this.extractRuntime($),
      imdbRating,
      imdbVotes,
      popularity,
      popularityDelta,
      posterUrl: this.extractPosterUrl($),
      backdropUrl: this.extractBackdropUrl($),
      trailerUrl,
      trailerRuntime,
      trailerLikes,
      description: this.extractDescription($),
      genres: this.extractGenres($),
      director: this.extractDirector($),
      cast: this.extractCast($),
      language: this.extractLanguage($),
      country: this.extractCountry($),
    };
  }

  async scrapeSeries(imdbId: string): Promise<ScrapedSeriesData> {
    const $ = await this.fetchPage(imdbId);
    const basicInfo = this.extractBasicInfo($);

    if (basicInfo.type !== 'series') {
      throw new Error('IMDb ID does not correspond to a TV series');
    }

    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
    const { popularity, delta: popularityDelta } = this.extractPopularity($);
    const { url: trailerUrl } = this.extractTrailerInfo($);

    return {
      title: basicInfo.title,
      startYear: basicInfo.year,
      endYear: this.extractEndYear($),
      rating: this.extractRating($),
      imdbRating,
      imdbVotes,
      popularity,
      popularityDelta,
      posterUrl: this.extractPosterUrl($),
      backdropUrl: this.extractBackdropUrl($),
      trailerUrl,
      description: this.extractDescription($),
      genres: this.extractGenres($),
      creator: this.extractCreator($),
      cast: this.extractCast($),
      language: this.extractLanguage($),
      country: this.extractCountry($),
      totalSeasons: this.extractTotalSeasons($),
      status: this.extractSeriesStatus($),
    };
  }

  private extractEndYear($: CheerioAPI): number | undefined {
    // Try to extract end year from series info
    const yearElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < yearElements.length; i++) {
      const text = $(yearElements[i]).text().trim();
      const yearMatch = text.match(/(\d{4})–(\d{4})/);
      if (yearMatch) {
        return parseInt(yearMatch[2]);
      }
    }
    return undefined;
  }

  private extractTotalSeasons($: CheerioAPI): number | undefined {
    // Try to extract total seasons from series info
    const seasonElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < seasonElements.length; i++) {
      const text = $(seasonElements[i]).text().trim();
      const seasonMatch = text.match(/(\d+)\s+seasons?/i);
      if (seasonMatch) {
        return parseInt(seasonMatch[1]);
      }
    }
    return 1; // Default to 1 season
  }

  private extractSeriesStatus($: CheerioAPI): string {
    // Try to determine if series is ongoing or ended
    const yearElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < yearElements.length; i++) {
      const text = $(yearElements[i]).text().trim();
      if (text.includes('–') && !text.match(/\d{4}–\d{4}/)) {
        return 'ongoing';
      } else if (text.match(/\d{4}–\d{4}/)) {
        return 'ended';
      }
    }
    return 'ongoing'; // Default to ongoing
  }
}

export default IMDbScraper;
