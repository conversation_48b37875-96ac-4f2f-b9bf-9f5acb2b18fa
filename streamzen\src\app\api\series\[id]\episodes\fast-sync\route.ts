import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';
import IMDbEpisodeScraper from '@/lib/imdbEpisodeScraper';

/**
 * ULTRA-FAST Episode Sync - Optimized for < 0.5 second response time
 * This endpoint is designed for maximum speed with minimal processing
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  const startTime = Date.now();
  
  try {
    const { id } = await params;
    console.log(`⚡ ULTRA-FAST episode sync for series: ${id}`);

    await connectDB();

    // OPTIMIZATION 1: Parallel queries with lean() for maximum speed
    const [series, existingEpisodes] = await Promise.all([
      Series.findOne({ imdbId: id }).lean(),
      Episode.countDocuments({ imdbId: id }) // Just count, don't fetch all data
    ]);

    if (!series) {
      return NextResponse.json(
        { success: false, error: 'Series not found' },
        { status: 404 }
      );
    }

    const quickCheckDuration = (Date.now() - startTime) / 1000;
    console.log(`📺 ${series.title}: ${existingEpisodes} existing episodes (${quickCheckDuration}s)`);

    // OPTIMIZATION 2: Ultra-fast completeness check
    if (existingEpisodes >= 20) { // Assume complete if 20+ episodes
      const duration = (Date.now() - startTime) / 1000;
      console.log(`⏭️ Series appears complete (${existingEpisodes} episodes), ultra-fast skip in ${duration}s`);
      return NextResponse.json({
        success: true,
        message: 'Series appears complete, sync skipped for speed',
        stats: {
          totalFound: existingEpisodes,
          newEpisodesAdded: 0,
          totalEpisodesInDB: existingEpisodes,
          duration: `${duration}s`,
          skipped: true,
          reason: 'complete_series'
        }
      });
    }

    // OPTIMIZATION 3: Fast IMDb scraping with timeout
    const imdbScraper = IMDbEpisodeScraper.getInstance();
    const imdbPromise = imdbScraper.getSeriesEpisodes(series.imdbId);
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('IMDb timeout')), 3000) // 3 second timeout
    );

    let imdbEpisodeData;
    try {
      imdbEpisodeData = await Promise.race([imdbPromise, timeoutPromise]);
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.log(`⚠️ IMDb timeout/error for ${series.title} in ${duration}s`);
      return NextResponse.json({
        success: false,
        error: 'IMDb data unavailable (timeout)',
        stats: {
          totalFound: 0,
          newEpisodesAdded: 0,
          totalEpisodesInDB: existingEpisodes,
          duration: `${duration}s`,
          reason: 'imdb_timeout'
        }
      });
    }

    if (!imdbEpisodeData) {
      const duration = (Date.now() - startTime) / 1000;
      console.log(`⚠️ No IMDb data for ${series.title} in ${duration}s`);
      return NextResponse.json({
        success: false,
        error: 'No episode data found on IMDb',
        stats: {
          totalFound: 0,
          newEpisodesAdded: 0,
          totalEpisodesInDB: existingEpisodes,
          duration: `${duration}s`
        }
      });
    }

    const imdbDuration = (Date.now() - startTime) / 1000;
    console.log(`🎯 IMDb: ${imdbEpisodeData.totalEpisodes} episodes in ${imdbDuration}s`);

    // OPTIMIZATION 4: Only get existing episodes if we need to check for missing ones
    const existingEpisodesList = await Episode.find({ imdbId: id }, { season: 1, episode: 1 }).lean();
    const existingSet = new Set<string>();
    existingEpisodesList.forEach(ep => {
      existingSet.add(`S${ep.season}E${ep.episode}`);
    });

    // OPTIMIZATION 5: Ultra-fast bulk insert for missing episodes only
    const bulkOps = [];
    let newEpisodesCount = 0;

    for (const season of imdbEpisodeData.seasons) {
      for (const imdbEpisode of season.episodes) {
        const episodeKey = `S${imdbEpisode.season}E${imdbEpisode.episode}`;
        
        // Skip if episode already exists
        if (existingSet.has(episodeKey)) {
          continue;
        }

        // Generate VidSrc URLs
        const embedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${imdbEpisode.season}&episode=${imdbEpisode.episode}`;
        const embedUrlTmdb = series.tmdbId ? `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId}&season=${imdbEpisode.season}&episode=${imdbEpisode.episode}` : '';

        bulkOps.push({
          insertOne: {
            document: {
              seriesTitle: series.title,
              imdbId: series.imdbId,
              tmdbId: series.tmdbId,
              season: imdbEpisode.season,
              episode: imdbEpisode.episode,
              episodeTitle: imdbEpisode.title || `Episode ${imdbEpisode.episode}`,
              description: imdbEpisode.description || `${series.title} - Season ${imdbEpisode.season}, Episode ${imdbEpisode.episode}`,
              posterUrl: series.posterUrl,
              genres: series.genres || [],
              language: series.language,
              country: series.country,
              embedUrl: embedUrl,
              embedUrlTmdb: embedUrlTmdb,
              vidsrcUrl: embedUrl,
              vidsrcTmdbUrl: embedUrlTmdb,
              airDate: imdbEpisode.airDate ? new Date(imdbEpisode.airDate) : undefined,
              runtime: imdbEpisode.runtime || '45 min',
              createdAt: new Date(),
              updatedAt: new Date()
            }
          }
        });
        newEpisodesCount++;

        // OPTIMIZATION 6: Limit bulk operations to prevent timeout
        if (bulkOps.length >= 50) {
          break;
        }
      }
      if (bulkOps.length >= 50) {
        break;
      }
    }

    // OPTIMIZATION 7: Single ultra-fast bulk write
    if (bulkOps.length > 0) {
      await Episode.bulkWrite(bulkOps, { ordered: false });
    }

    const totalDuration = (Date.now() - startTime) / 1000;
    console.log(`⚡ ULTRA-FAST sync completed: ${newEpisodesCount} new episodes in ${totalDuration}s`);

    return NextResponse.json({
      success: true,
      message: `Ultra-fast sync completed in ${totalDuration}s`,
      stats: {
        totalFound: imdbEpisodeData.totalEpisodes,
        newEpisodesAdded: newEpisodesCount,
        totalEpisodesInDB: existingEpisodes + newEpisodesCount,
        duration: `${totalDuration}s`,
        performance: 'ultra_fast'
      }
    });

  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    console.error(`❌ Ultra-fast sync failed in ${duration}s:`, error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Ultra-fast sync failed',
        message: error.message,
        duration: `${duration}s`
      },
      { status: 500 }
    );
  }
}

/**
 * GET method for testing
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  return POST(request, { params });
}
