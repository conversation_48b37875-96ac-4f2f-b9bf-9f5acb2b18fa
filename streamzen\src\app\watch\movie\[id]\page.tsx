import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { apiClient } from '@/lib/api';
import VideoPlayer from '@/components/VideoPlayer';
import ContentInfo from '@/components/ContentInfo';
import VidSrcAPI from '@/lib/vidsrc';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface MovieWatchPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getMovie(id: string) {
  try {
    const movie = await apiClient.getMovie(id);
    return movie;
  } catch (error) {
    // Only log error if it's not a 404 (which is expected for non-existent content)
    if (!error.message?.includes('404')) {
      console.error('Error fetching movie:', error);
    }
    return null;
  }
}

export async function generateMetadata({ params }: MovieWatchPageProps): Promise<Metadata> {
  const { id } = await params;
  const movie = await getMovie(id);

  if (!movie) {
    return {
      title: 'Movie Not Found | freeMoviesWatchNow',
      description: 'The requested movie could not be found.',
    };
  }

  return SEOGenerator.generateMovieMetadata(movie);
}

export default async function MovieWatchPage({ params }: MovieWatchPageProps) {
  const { id } = await params;
  const movie = await getMovie(id);

  if (!movie) {
    notFound();
  }

  // Generate all streaming sources
  const vidsrc = VidSrcAPI.getInstance();
  const streamingSources = vidsrc.generateAllMovieEmbedUrls(movie.imdbId, movie.tmdbId);

  const contentInfo = {
    title: movie.title,
    year: movie.year,
    rating: movie.rating,
    runtime: movie.runtime,
    imdbRating: movie.imdbRating,
    description: movie.description,
    genres: movie.genres,
    director: movie.director,
    cast: movie.cast,
    language: movie.language,
    country: movie.country,
    posterUrl: movie.posterUrl,
    type: 'movie' as const
  };

  // Generate structured data
  const movieSchema = SchemaGenerator.generateMovieSchema(movie);
  const breadcrumbSchema = SchemaGenerator.generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Movies', url: '/movies' },
    { name: movie.title, url: `/watch/movie/${movie.imdbId}` }
  ]);

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            movieSchema,
            breadcrumbSchema
          ])
        }}
      />

      <VideoPlayer
        streamingSources={streamingSources}
        title={movie.title}
        type="movie"
      />

      <div className="max-w-[2560px] mx-auto px-8 lg:px-24 py-12">
        <ContentInfo content={contentInfo} />

        {/* SEO Blog-Style Content */}
        <div className="mt-16 max-w-4xl mx-auto">
          <article className="prose prose-invert prose-lg max-w-none">
            <h2 className="text-3xl font-bold text-white mb-6">
              About {movie.title} ({movie.year})
            </h2>

            <div className="text-gray-300 leading-relaxed space-y-4">
              <p>
                <strong>{movie.title}</strong> is a {movie.genres?.join(', ').toLowerCase()} movie released in {movie.year}.
                {movie.description && ` ${movie.description}`}
              </p>

              {movie.director && (
                <p>
                  Directed by <strong>{movie.director}</strong>, this film showcases exceptional storytelling and cinematography.
                </p>
              )}

              {movie.cast && movie.cast.length > 0 && (
                <p>
                  The movie features an outstanding cast including <strong>{movie.cast.slice(0, 5).join(', ')}</strong>
                  {movie.cast.length > 5 && ' and many more talented actors'}.
                </p>
              )}

              {movie.imdbRating && (
                <p>
                  With an IMDb rating of <strong>{movie.imdbRating}/10</strong>, {movie.title} has received
                  {movie.imdbRating >= 8 ? ' critical acclaim' : movie.imdbRating >= 7 ? ' positive reviews' : ' mixed reviews'}
                  from audiences and critics alike.
                </p>
              )}

              <p>
                Experience <strong>{movie.title}</strong> ({movie.year}) in stunning HD quality on freeMoviesWatchNow.
                {movie.director && ` Directed by ${movie.director},`} this {movie.genres?.[0]?.toLowerCase()}
                {movie.runtime && ` runs for ${movie.runtime}`} and delivers an unforgettable cinematic experience.
                Our advanced streaming technology ensures seamless playback across all devices.
              </p>

              {movie.cast && movie.cast.length > 0 && (
                <p>
                  Featuring outstanding performances by <strong>{movie.cast.slice(0, 3).join(', ')}</strong>
                  {movie.cast.length > 3 && ` and ${movie.cast.length - 3} other talented actors`},
                  this film showcases exceptional storytelling and production quality.
                </p>
              )}

              <p>
                Stream <strong>{movie.title}</strong> instantly with our premium video player featuring multiple quality options,
                subtitle support, and adaptive streaming technology. Join millions of viewers who trust freeMoviesWatchNow
                for their entertainment needs.
              </p>

              {movie.language && movie.language !== 'English' && (
                <p>
                  Originally produced in <strong>{movie.language}</strong>, this film offers subtitles and dubbing
                  options for international audiences. Experience authentic {movie.language} cinema with
                  high-quality translations and cultural context.
                </p>
              )}

              {movie.genres && movie.genres.length > 1 && (
                <p>
                  This multi-genre masterpiece combines elements of <strong>{movie.genres.join(', ')}</strong>,
                  creating a unique viewing experience that appeals to diverse audiences. Each genre element
                  is expertly woven into the narrative structure.
                </p>
              )}

              <div className="mt-8 p-6 bg-gray-900/50 rounded-xl border border-gray-700/50">
                <h3 className="text-xl font-bold text-white mb-4">Why Watch {movie.title} on freeMoviesWatchNow?</h3>
                <ul className="space-y-2 text-gray-300">
                  <li>• <strong>Premium HD Quality:</strong> Crystal clear 1080p streaming with adaptive bitrate</li>
                  <li>• <strong>Multiple Sources:</strong> Backup streaming options for uninterrupted viewing</li>
                  <li>• <strong>No Registration:</strong> Start watching immediately without account creation</li>
                  <li>• <strong>Mobile Optimized:</strong> Perfect viewing experience on all devices</li>
                  <li>• <strong>Fast Loading:</strong> Advanced CDN technology for instant playback</li>
                  {movie.language !== 'English' && (
                    <li>• <strong>Subtitle Support:</strong> Multiple language options available</li>
                  )}
                </ul>
              </div>

              <div className="mt-8 p-6 bg-blue-900/20 rounded-xl border border-blue-700/30">
                <h3 className="text-xl font-bold text-white mb-4">Technical Specifications</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Quality:</span>
                    <p className="text-white font-medium">HD 1080p</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Audio:</span>
                    <p className="text-white font-medium">Stereo/5.1</p>
                  </div>
                  {movie.runtime && (
                    <div>
                      <span className="text-gray-400">Duration:</span>
                      <p className="text-white font-medium">{movie.runtime}</p>
                    </div>
                  )}
                  {movie.language && (
                    <div>
                      <span className="text-gray-400">Language:</span>
                      <p className="text-white font-medium">{movie.language}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-8 p-6 bg-gray-900/30 rounded-xl border border-gray-700/30">
                <h3 className="text-xl font-bold text-white mb-4">Frequently Asked Questions</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-white mb-2">Is {movie.title} available in HD quality?</h4>
                    <p className="text-gray-300 text-sm">
                      Yes, {movie.title} is available in full HD 1080p quality with crystal clear audio.
                      Our streaming technology automatically adjusts quality based on your connection.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2">Do I need to register to watch {movie.title}?</h4>
                    <p className="text-gray-300 text-sm">
                      No registration required! You can start watching {movie.title} immediately without
                      creating an account or providing personal information.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2">Is {movie.title} compatible with mobile devices?</h4>
                    <p className="text-gray-300 text-sm">
                      Absolutely! {movie.title} streams perfectly on smartphones, tablets, laptops, and
                      desktop computers with responsive video player controls.
                    </p>
                  </div>
                  {movie.language !== 'English' && (
                    <div>
                      <h4 className="font-semibold text-white mb-2">Are subtitles available for {movie.title}?</h4>
                      <p className="text-gray-300 text-sm">
                        Yes, {movie.title} includes subtitle options for better accessibility and
                        international viewing experience.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <p>
                freeMoviesWatchNow offers the latest movies and classic films in various genres. Whether you're looking for
                action-packed blockbusters, heartwarming dramas, spine-chilling horror, or laugh-out-loud comedies,
                our extensive library has something for everyone.
              </p>
            </div>

            <div className="mt-8 p-6 bg-gray-900/50 rounded-lg border border-gray-800">
              <h3 className="text-xl font-semibold text-white mb-4">Why Watch on freeMoviesWatchNow?</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• HD Quality streaming with multiple sources</li>
                <li>• No registration required - watch instantly</li>
                <li>• Compatible with all devices</li>
                <li>• Fast loading and reliable servers</li>
                <li>• Updated daily with latest releases</li>
              </ul>
            </div>
          </article>
        </div>
      </div>
    </div>
  );
}
