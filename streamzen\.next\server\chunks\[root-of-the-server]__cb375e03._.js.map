{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Movie.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IMovie extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string; // MPAA rating (R, PG-13, etc.)\n  runtime?: string; // e.g., \"2h 22m\"\n  imdbRating?: number;\n  imdbVotes?: string; // e.g., \"3.1M\"\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst MovieSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: {\n    type: String,\n    required: true\n  },\n  year: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  rating: String,\n  runtime: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  trailerRuntime: String,\n  trailerLikes: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  director: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  }\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nMovieSchema.index({ year: -1, imdbRating: -1 });\nMovieSchema.index({ genres: 1, year: -1 });\n// Removed text index to avoid language override issues\nMovieSchema.index({ title: 1 });\nMovieSchema.index({ language: 1, country: 1 });\n\nexport default mongoose.models.Movie || mongoose.model<IMovie>('Movie', MovieSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAgCA,MAAM,cAAsB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACrC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;IACZ;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;IACR,SAAS;IACT,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,UAAU;IACV,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;AACF,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,YAAY,KAAK,CAAC;IAAE,MAAM,CAAC;IAAG,YAAY,CAAC;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,MAAM,CAAC;AAAE;AACxC,uDAAuD;AACvD,YAAY,KAAK,CAAC;IAAE,OAAO;AAAE;AAC7B,YAAY,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;uCAE7B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAS,SAAS", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Series.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\n// Episode interface for embedded episodes within series\nexport interface IEpisode {\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  description?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  posterUrl?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl: string;\n  vidsrcTmdbUrl?: string;\n  isLatestRelease?: boolean; // ✅ CRITICAL: VidSrc latest release flag\n  createdAt?: Date;\n  updatedAt?: Date;\n}\n\nexport interface ISeries extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string; // MPAA rating\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string; // 'ongoing', 'ended', 'cancelled'\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection\n  episodes: IEpisode[];\n  episodeCount?: number; // Cache for quick access\n  lastEpisodeUpdate?: Date; // Track when episodes were last synced\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst SeriesSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: {\n    type: String,\n    required: true\n  },\n  startYear: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  endYear: { \n    type: Number,\n    index: true \n  },\n  rating: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  creator: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  totalSeasons: Number,\n  status: { \n    type: String,\n    enum: ['ongoing', 'ended', 'cancelled'],\n    index: true \n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  // 🆕 EMBEDDED EPISODES ARRAY - Replaces separate Episode collection\n  episodes: [{\n    season: { type: Number, required: true },\n    episode: { type: Number, required: true },\n    episodeTitle: String,\n    description: String,\n    airDate: Date,\n    runtime: { type: String, default: '45 min' },\n    imdbRating: Number,\n    posterUrl: String,\n    embedUrl: { type: String, required: true },\n    embedUrlTmdb: String,\n    vidsrcUrl: { type: String, required: true },\n    vidsrcTmdbUrl: String,\n    isLatestRelease: { type: Boolean, default: false, index: true }, // ✅ CRITICAL: VidSrc latest release flag\n    createdAt: { type: Date, default: Date.now },\n    updatedAt: { type: Date, default: Date.now }\n  }],\n  episodeCount: { type: Number, default: 0 }, // Cache for quick access\n  lastEpisodeUpdate: Date // Track when episodes were last synced\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nSeriesSchema.index({ startYear: -1, imdbRating: -1 });\nSeriesSchema.index({ genres: 1, startYear: -1 });\nSeriesSchema.index({ status: 1, startYear: -1 });\n// Removed text index to avoid language override issues\nSeriesSchema.index({ title: 1 });\nSeriesSchema.index({ language: 1, country: 1 });\n// 🆕 EPISODE-SPECIFIC INDEXES for embedded episodes\nSeriesSchema.index({ 'episodes.season': 1, 'episodes.episode': 1 });\nSeriesSchema.index({ 'episodes.isLatestRelease': 1 }); // ✅ CRITICAL: For VidSrc latest episodes queries\nSeriesSchema.index({ lastEpisodeUpdate: -1 }); // For sync tracking\nSeriesSchema.index({ episodeCount: -1 }); // For quick episode count queries\n\nexport default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAsDA,MAAM,eAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;IACR,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,SAAS;IACT,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,cAAc;IACd,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAS;SAAY;QACvC,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,oEAAoE;IACpE,UAAU;QAAC;YACT,QAAQ;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACvC,SAAS;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACxC,cAAc;YACd,aAAa;YACb,SAAS;YACT,SAAS;gBAAE,MAAM;gBAAQ,SAAS;YAAS;YAC3C,YAAY;YACZ,WAAW;YACX,UAAU;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACzC,cAAc;YACd,WAAW;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YAC1C,eAAe;YACf,iBAAiB;gBAAE,MAAM;gBAAS,SAAS;gBAAO,OAAO;YAAK;YAC9D,WAAW;gBAAE,MAAM;gBAAM,SAAS,KAAK,GAAG;YAAC;YAC3C,WAAW;gBAAE,MAAM;gBAAM,SAAS,KAAK,GAAG;YAAC;QAC7C;KAAE;IACF,cAAc;QAAE,MAAM;QAAQ,SAAS;IAAE;IACzC,mBAAmB,KAAK,uCAAuC;AACjE,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,aAAa,KAAK,CAAC;IAAE,WAAW,CAAC;IAAG,YAAY,CAAC;AAAE;AACnD,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,uDAAuD;AACvD,aAAa,KAAK,CAAC;IAAE,OAAO;AAAE;AAC9B,aAAa,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;AAC7C,oDAAoD;AACpD,aAAa,KAAK,CAAC;IAAE,mBAAmB;IAAG,oBAAoB;AAAE;AACjE,aAAa,KAAK,CAAC;IAAE,4BAA4B;AAAE,IAAI,iDAAiD;AACxG,aAAa,KAAK,CAAC;IAAE,mBAAmB,CAAC;AAAE,IAAI,oBAAoB;AACnE,aAAa,KAAK,CAAC;IAAE,cAAc,CAAC;AAAE,IAAI,kCAAkC;uCAE7D,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Episode.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IEpisode extends Document {\n  imdbId: string; // Series IMDb ID\n  tmdbId?: string; // Series TMDB ID\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  genres?: string[]; // Genres inherited from series\n  language?: string; // Language inherited from series\n  country?: string; // Country inherited from series\n  posterUrl?: string; // Poster inherited from series\n  isLatestRelease?: boolean; // Track if episode is from VidSrc latest\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst EpisodeSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  seriesTitle: { \n    type: String, \n    required: true,\n    index: true \n  },\n  season: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episode: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episodeTitle: String,\n  airDate: { \n    type: Date,\n    index: true \n  },\n  runtime: String,\n  imdbRating: Number,\n  description: String,\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  },\n  genres: [{\n    type: String,\n    index: true\n  }],\n  language: {\n    type: String,\n    index: true\n  },\n  country: {\n    type: String,\n    index: true\n  },\n  posterUrl: String,\n  isLatestRelease: {\n    type: Boolean,\n    default: false,\n    index: true\n  }\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nEpisodeSchema.index({ imdbId: 1, season: 1, episode: 1 }, { unique: true });\nEpisodeSchema.index({ airDate: -1 });\nEpisodeSchema.index({ seriesTitle: 1, season: 1, episode: 1 });\nEpisodeSchema.index({ createdAt: -1 }); // For latest episodes\n\nexport default mongoose.models.Episode || mongoose.model<IEpisode>('Episode', EpisodeSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA2BA,MAAM,gBAAwB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACvC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,cAAc;IACd,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,SAAS;IACT,YAAY;IACZ,aAAa;IACb,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,iBAAiB;QACf,MAAM;QACN,SAAS;QACT,OAAO;IACT;AACF,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,cAAc,KAAK,CAAC;IAAE,QAAQ;IAAG,QAAQ;IAAG,SAAS;AAAE,GAAG;IAAE,QAAQ;AAAK;AACzE,cAAc,KAAK,CAAC;IAAE,SAAS,CAAC;AAAE;AAClC,cAAc,KAAK,CAAC;IAAE,aAAa;IAAG,QAAQ;IAAG,SAAS;AAAE;AAC5D,cAAc,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE,IAAI,sBAAsB;uCAE/C,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAW,WAAW", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/scraper.ts"], "sourcesContent": ["import axios from 'axios';\nimport * as cheerio from 'cheerio';\n\nexport interface ScrapedMovieData {\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  backdropUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n}\n\nexport interface ScrapedSeriesData {\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  backdropUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n}\n\nclass IMDbScraper {\n  private static instance: IMDbScraper;\n  private requestCount = 0;\n  private lastRequestTime = 0;\n  private readonly RATE_LIMIT = 30; // requests per minute\n  private readonly MIN_DELAY = 2000; // 2 seconds between requests\n\n  static getInstance(): IMDbScraper {\n    if (!IMDbScraper.instance) {\n      IMDbScraper.instance = new IMDbScraper();\n    }\n    return IMDbScraper.instance;\n  }\n\n  private async rateLimit(): Promise<void> {\n    const now = Date.now();\n    const timeSinceLastRequest = now - this.lastRequestTime;\n\n    if (timeSinceLastRequest < this.MIN_DELAY) {\n      const delay = this.MIN_DELAY - timeSinceLastRequest;\n      // Add random jitter to avoid detection patterns\n      const jitter = Math.random() * 1000; // 0-1000ms random delay\n      await new Promise(resolve => setTimeout(resolve, delay + jitter));\n    }\n\n    this.lastRequestTime = Date.now();\n    this.requestCount++;\n  }\n\n  private getRandomUserAgent(): string {\n    const userAgents = [\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',\n      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'\n    ];\n    return userAgents[Math.floor(Math.random() * userAgents.length)];\n  }\n\n  private async fetchPage(imdbId: string): Promise<cheerio.CheerioAPI> {\n    await this.rateLimit();\n\n    const url = `https://www.imdb.com/title/${imdbId}/`;\n    const headers = {\n      'User-Agent': this.getRandomUserAgent(),\n      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n      'Accept-Language': 'en-US,en;q=0.5',\n      'Accept-Encoding': 'gzip, deflate, br',\n      'Connection': 'keep-alive',\n      'Upgrade-Insecure-Requests': '1',\n      'Sec-Fetch-Dest': 'document',\n      'Sec-Fetch-Mode': 'navigate',\n      'Sec-Fetch-Site': 'none',\n      'Cache-Control': 'max-age=0',\n    };\n\n    try {\n      const response = await axios.get(url, { headers, timeout: 30000 });\n      return cheerio.load(response.data);\n    } catch (error) {\n      console.error(`Error fetching IMDb page for ${imdbId}:`, error);\n      throw new Error(`Failed to fetch IMDb page: ${error.message}`);\n    }\n  }\n\n  private extractBasicInfo($: cheerio.CheerioAPI): { title: string; year: number; type: 'movie' | 'series' } {\n    const titleElement = $('h1[data-testid=\"hero__pageTitle\"] span[data-testid=\"hero__primary-text\"]');\n    const title = titleElement.text().trim();\n    \n    if (!title) {\n      throw new Error('Could not extract title from IMDb page');\n    }\n\n    // Extract year from the release info\n    const yearElement = $('ul.ipc-inline-list a[href*=\"/releaseinfo/\"]');\n    const yearText = yearElement.text().trim();\n    const year = parseInt(yearText) || new Date().getFullYear();\n\n    // Determine if it's a movie or series\n    const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();\n    const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');\n    \n    return {\n      title,\n      year,\n      type: isMovie ? 'movie' : 'series'\n    };\n  }\n\n  private extractRating($: cheerio.CheerioAPI): string | undefined {\n    const ratingElement = $('ul.ipc-inline-list a[href*=\"/parentalguide/\"]');\n    return ratingElement.text().trim() || undefined;\n  }\n\n  private extractRuntime($: cheerio.CheerioAPI): string | undefined {\n    const runtimeElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < runtimeElements.length; i++) {\n      const text = $(runtimeElements[i]).text().trim();\n      if (text.includes('h') || text.includes('min')) {\n        return text;\n      }\n    }\n    return undefined;\n  }\n\n  private extractIMDbRating($: cheerio.CheerioAPI): { rating?: number; votes?: string } {\n    const ratingElement = $('div[data-testid=\"hero-rating-bar__aggregate-rating__score\"] span');\n    const rating = parseFloat(ratingElement.text().trim()) || undefined;\n    \n    const votesElement = $('div.sc-d541859f-3');\n    const votes = votesElement.text().trim() || undefined;\n    \n    return { rating, votes };\n  }\n\n  private extractPopularity($: cheerio.CheerioAPI): { popularity?: number; delta?: number } {\n    const popularityElement = $('div[data-testid=\"hero-rating-bar__popularity__score\"]');\n    const popularity = parseInt(popularityElement.text().trim()) || undefined;\n    \n    const deltaElement = $('div[data-testid=\"hero-rating-bar__popularity__delta\"]');\n    const deltaText = deltaElement.text().trim();\n    const delta = deltaText ? parseInt(deltaText.replace(/[^\\d-]/g, '')) : undefined;\n    \n    return { popularity, delta };\n  }\n\n  private extractPosterUrl($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for poster image\n    const selectors = [\n      'div[data-testid=\"hero-media__poster\"] img',\n      '.ipc-image[data-testid=\"hero-media__poster\"]',\n      '.poster img',\n      '.ipc-media img',\n      'img[class*=\"poster\"]',\n      'a[class*=\"ipc-lockup-overlay\"] img'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      const src = element.attr('src');\n      if (src && src.includes('media-amazon.com')) {\n        // Clean up the URL to get high quality image\n        return src.replace(/\\._.*?_\\./, '._V1_FMjpg_UX1000_.').replace(/\\._.*?\\./, '._V1_FMjpg_UX1000_.');\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractBackdropUrl($: cheerio.CheerioAPI): string | undefined {\n    // Try to extract backdrop/hero image\n    const selectors = [\n      '.hero-media__slate-overlay img',\n      '.slate img',\n      '.hero__background img',\n      'div[data-testid=\"hero-media\"] img'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      const src = element.attr('src');\n      if (src && src.includes('media-amazon.com')) {\n        return src.replace(/\\._.*?_\\./, '._V1_FMjpg_UX1920_.');\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractTrailerInfo($: cheerio.CheerioAPI): { url?: string; runtime?: string; likes?: string } {\n    const trailerElement = $('a[data-testid=\"video-player-slate-overlay\"]');\n    const url = trailerElement.attr('href') || undefined;\n    \n    const runtimeElement = $('span[data-testid=\"video-player-slate-runtime\"]');\n    const runtime = runtimeElement.text().trim() || undefined;\n    \n    const likesElement = $('span.ipc-reaction-summary__label');\n    const likes = likesElement.text().trim() || undefined;\n    \n    return { url, runtime, likes };\n  }\n\n  private extractDescription($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for description\n    const selectors = [\n      'div[data-testid=\"hero-media__slate\"] img',\n      'span[data-testid=\"plot-xl\"]',\n      'span[data-testid=\"plot-l\"]',\n      'span[data-testid=\"plot\"]'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      if (selector.includes('img')) {\n        const alt = element.attr('alt');\n        if (alt && alt.length > 20) return alt;\n      } else {\n        const text = element.text().trim();\n        if (text && text.length > 10) return text;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractGenres($: cheerio.CheerioAPI): string[] {\n    const genres: string[] = [];\n\n    // Updated genre selectors based on current IMDb structure\n    const genreSelectors = [\n      // Current IMDb hero section genres (most common)\n      '[data-testid=\"genres\"] .ipc-chip .ipc-chip__text',\n      '[data-testid=\"genres\"] .ipc-chip__text',\n      '[data-testid=\"genres\"] a .ipc-chip__text',\n\n      // Hero section alternative formats\n      '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',\n      '.GenresAndPlot__GenreChip .ipc-chip__text',\n\n      // Storyline section (your provided structure - keep as fallback)\n      'li[data-testid=\"storyline-genres\"] .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"storyline-genres\"] a[href*=\"genres=\"]',\n\n      // General genre link selectors (broader search)\n      'a[href*=\"/search/title/?genres=\"] span',\n      'a[href*=\"genres=\"] span',\n      'a[href*=\"genres=\"]',\n\n      // Schema.org microdata\n      '[itemprop=\"genre\"]',\n      'span[itemprop=\"genre\"]',\n\n      // Fallback selectors\n      '.see-more.inline.canwrap a[href*=\"genres=\"]',\n      '.titlePageSprite.star-box-giga-star + div a[href*=\"genres=\"]',\n\n      // Very broad fallback - any link with genre in URL\n      'a[href*=\"explore=genres\"]'\n    ];\n\n    console.log(`🔍 Starting genre extraction...`);\n\n    for (let i = 0; i < genreSelectors.length; i++) {\n      const selector = genreSelectors[i];\n      const elements = $(selector);\n      console.log(`🔍 Selector ${i + 1}/${genreSelectors.length}: \"${selector}\" found ${elements.length} elements`);\n\n      if (elements.length > 0) {\n        elements.each((_, element) => {\n          const genre = $(element).text().trim();\n          console.log(`📝 Found genre text: \"${genre}\"`);\n\n          // Clean up genre text and validate\n          if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {\n            // Skip common non-genre text\n            const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];\n            if (!skipTexts.includes(genre)) {\n              genres.push(genre);\n              console.log(`✅ Added genre: \"${genre}\"`);\n            }\n          }\n        });\n\n        if (genres.length > 0) {\n          console.log(`✅ Successfully extracted ${genres.length} genres: [${genres.join(', ')}]`);\n          break; // Use the first selector that finds results\n        }\n      }\n    }\n\n    if (genres.length === 0) {\n      console.log('⚠️ No genres found with any selector');\n      // Debug: Let's see what's actually in the storyline section\n      const storylineSection = $('li[data-testid=\"storyline-genres\"]');\n      if (storylineSection.length > 0) {\n        console.log('📋 Storyline section HTML:', storylineSection.html());\n      } else {\n        console.log('❌ No storyline-genres section found');\n      }\n    }\n\n    return genres;\n  }\n\n  private extractCast($: cheerio.CheerioAPI): string[] {\n    const cast: string[] = [];\n\n    // Try multiple selectors for cast\n    const castSelectors = [\n      'section[data-testid=\"title-cast\"] a[data-testid=\"title-cast-item__actor\"]',\n      '.cast_list .primary_photo + td a',\n      '.titleCast .primary_photo + td a',\n      'div[data-testid=\"title-cast-item\"] a[href*=\"/name/\"]'\n    ];\n\n    for (const selector of castSelectors) {\n      const elements = $(selector);\n      if (elements.length > 0) {\n        elements.each((_, element) => {\n          const actorName = $(element).text().trim();\n          if (actorName && !cast.includes(actorName) && cast.length < 10) { // Limit to top 10 cast members\n            cast.push(actorName);\n          }\n        });\n        break; // Use the first selector that finds results\n      }\n    }\n\n    return cast;\n  }\n\n  private extractDirector($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for director\n    const directorSelectors = [\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Director\") .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Directors\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"/name/\"][href*=\"ref_=tt_ov_dr\"]',\n      '.credit_summary_item:contains(\"Director\") a'\n    ];\n\n    for (const selector of directorSelectors) {\n      const element = $(selector).first();\n      const director = element.text().trim();\n      if (director) {\n        return director;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractCreator($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for creator (for TV series)\n    const creatorSelectors = [\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Creator\") .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Creators\") .ipc-metadata-list-item__list-content-item',\n      '.credit_summary_item:contains(\"Creator\") a',\n      '.credit_summary_item:contains(\"Created by\") a'\n    ];\n\n    for (const selector of creatorSelectors) {\n      const element = $(selector).first();\n      const creator = element.text().trim();\n      if (creator) {\n        return creator;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractLanguage($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for language\n    const languageSelectors = [\n      'li[data-testid=\"title-details-languages\"] .ipc-metadata-list-item__list-content-item',\n      'div[data-testid=\"title-details-section\"] li:contains(\"Language\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"primary_language=\"]',\n      '.txt-block:contains(\"Language\") a'\n    ];\n\n    for (const selector of languageSelectors) {\n      const element = $(selector).first();\n      const language = element.text().trim();\n      if (language) {\n        return language;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractCountry($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for country\n    const countrySelectors = [\n      'li[data-testid=\"title-details-origin\"] .ipc-metadata-list-item__list-content-item',\n      'div[data-testid=\"title-details-section\"] li:contains(\"Country\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"country_of_origin=\"]',\n      '.txt-block:contains(\"Country\") a'\n    ];\n\n    for (const selector of countrySelectors) {\n      const element = $(selector).first();\n      const country = element.text().trim();\n      if (country) {\n        return country;\n      }\n    }\n\n    return undefined;\n  }\n\n  async scrapeMovie(imdbId: string): Promise<ScrapedMovieData> {\n    const $ = await this.fetchPage(imdbId);\n    const basicInfo = this.extractBasicInfo($);\n\n    if (basicInfo.type !== 'movie') {\n      throw new Error('IMDb ID does not correspond to a movie');\n    }\n\n    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);\n    const { popularity, delta: popularityDelta } = this.extractPopularity($);\n    const { url: trailerUrl, runtime: trailerRuntime, likes: trailerLikes } = this.extractTrailerInfo($);\n\n    return {\n      title: basicInfo.title,\n      year: basicInfo.year,\n      rating: this.extractRating($),\n      runtime: this.extractRuntime($),\n      imdbRating,\n      imdbVotes,\n      popularity,\n      popularityDelta,\n      posterUrl: this.extractPosterUrl($),\n      backdropUrl: this.extractBackdropUrl($),\n      trailerUrl,\n      trailerRuntime,\n      trailerLikes,\n      description: this.extractDescription($),\n      genres: this.extractGenres($),\n      director: this.extractDirector($),\n      cast: this.extractCast($),\n      language: this.extractLanguage($),\n      country: this.extractCountry($),\n    };\n  }\n\n  async scrapeSeries(imdbId: string): Promise<ScrapedSeriesData> {\n    const $ = await this.fetchPage(imdbId);\n    const basicInfo = this.extractBasicInfo($);\n\n    if (basicInfo.type !== 'series') {\n      throw new Error('IMDb ID does not correspond to a TV series');\n    }\n\n    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);\n    const { popularity, delta: popularityDelta } = this.extractPopularity($);\n    const { url: trailerUrl } = this.extractTrailerInfo($);\n\n    return {\n      title: basicInfo.title,\n      startYear: basicInfo.year,\n      endYear: this.extractEndYear($),\n      rating: this.extractRating($),\n      imdbRating,\n      imdbVotes,\n      popularity,\n      popularityDelta,\n      posterUrl: this.extractPosterUrl($),\n      backdropUrl: this.extractBackdropUrl($),\n      trailerUrl,\n      description: this.extractDescription($),\n      genres: this.extractGenres($),\n      creator: this.extractCreator($),\n      cast: this.extractCast($),\n      language: this.extractLanguage($),\n      country: this.extractCountry($),\n      totalSeasons: this.extractTotalSeasons($),\n      status: this.extractSeriesStatus($),\n    };\n  }\n\n  private extractEndYear($: CheerioAPI): number | undefined {\n    // Try to extract end year from series info\n    const yearElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < yearElements.length; i++) {\n      const text = $(yearElements[i]).text().trim();\n      const yearMatch = text.match(/(\\d{4})–(\\d{4})/);\n      if (yearMatch) {\n        return parseInt(yearMatch[2]);\n      }\n    }\n    return undefined;\n  }\n\n  private extractTotalSeasons($: CheerioAPI): number | undefined {\n    // Try to extract total seasons from series info\n    const seasonElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < seasonElements.length; i++) {\n      const text = $(seasonElements[i]).text().trim();\n      const seasonMatch = text.match(/(\\d+)\\s+seasons?/i);\n      if (seasonMatch) {\n        return parseInt(seasonMatch[1]);\n      }\n    }\n    return 1; // Default to 1 season\n  }\n\n  private extractSeriesStatus($: CheerioAPI): string {\n    // Try to determine if series is ongoing or ended\n    const yearElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < yearElements.length; i++) {\n      const text = $(yearElements[i]).text().trim();\n      if (text.includes('–') && !text.match(/\\d{4}–\\d{4}/)) {\n        return 'ongoing';\n      } else if (text.match(/\\d{4}–\\d{4}/)) {\n        return 'ended';\n      }\n    }\n    return 'ongoing'; // Default to ongoing\n  }\n}\n\nexport default IMDbScraper;\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AA8CA,MAAM;IACJ,OAAe,SAAsB;IAC7B,eAAe,EAAE;IACjB,kBAAkB,EAAE;IACX,aAAa,GAAG;IAChB,YAAY,KAAK;IAElC,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,MAAc,YAA2B;QACvC,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,uBAAuB,MAAM,IAAI,CAAC,eAAe;QAEvD,IAAI,uBAAuB,IAAI,CAAC,SAAS,EAAE;YACzC,MAAM,QAAQ,IAAI,CAAC,SAAS,GAAG;YAC/B,gDAAgD;YAChD,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,wBAAwB;YAC7D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ;QAC3D;QAEA,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG;QAC/B,IAAI,CAAC,YAAY;IACnB;IAEQ,qBAA6B;QACnC,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA;SACD;QACD,OAAO,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;IAClE;IAEA,MAAc,UAAU,MAAc,EAA+B;QACnE,MAAM,IAAI,CAAC,SAAS;QAEpB,MAAM,MAAM,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;QACnD,MAAM,UAAU;YACd,cAAc,IAAI,CAAC,kBAAkB;YACrC,UAAU;YACV,mBAAmB;YACnB,mBAAmB;YACnB,cAAc;YACd,6BAA6B;YAC7B,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,iBAAiB;QACnB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBAAE;gBAAS,SAAS;YAAM;YAChE,OAAO,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EAAE;YACzD,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM,OAAO,EAAE;QAC/D;IACF;IAEQ,iBAAiB,CAAqB,EAA6D;QACzG,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI;QAEtC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,MAAM,cAAc,EAAE;QACtB,MAAM,WAAW,YAAY,IAAI,GAAG,IAAI;QACxC,MAAM,OAAO,SAAS,aAAa,IAAI,OAAO,WAAW;QAEzD,sCAAsC;QACtC,MAAM,iBAAiB,EAAE,yBAAyB,IAAI,GAAG,WAAW;QACpE,MAAM,UAAU,CAAC,eAAe,QAAQ,CAAC,gBAAgB,CAAC,eAAe,QAAQ,CAAC;QAElF,OAAO;YACL;YACA;YACA,MAAM,UAAU,UAAU;QAC5B;IACF;IAEQ,cAAc,CAAqB,EAAsB;QAC/D,MAAM,gBAAgB,EAAE;QACxB,OAAO,cAAc,IAAI,GAAG,IAAI,MAAM;IACxC;IAEQ,eAAe,CAAqB,EAAsB;QAChE,MAAM,kBAAkB,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,MAAM,OAAO,EAAE,eAAe,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC9C,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ;gBAC9C,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,CAAqB,EAAuC;QACpF,MAAM,gBAAgB,EAAE;QACxB,MAAM,SAAS,WAAW,cAAc,IAAI,GAAG,IAAI,OAAO;QAE1D,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI,MAAM;QAE5C,OAAO;YAAE;YAAQ;QAAM;IACzB;IAEQ,kBAAkB,CAAqB,EAA2C;QACxF,MAAM,oBAAoB,EAAE;QAC5B,MAAM,aAAa,SAAS,kBAAkB,IAAI,GAAG,IAAI,OAAO;QAEhE,MAAM,eAAe,EAAE;QACvB,MAAM,YAAY,aAAa,IAAI,GAAG,IAAI;QAC1C,MAAM,QAAQ,YAAY,SAAS,UAAU,OAAO,CAAC,WAAW,OAAO;QAEvE,OAAO;YAAE;YAAY;QAAM;IAC7B;IAEQ,iBAAiB,CAAqB,EAAsB;QAClE,0CAA0C;QAC1C,MAAM,YAAY;YAChB;YACA;YACA;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,MAAM,MAAM,QAAQ,IAAI,CAAC;YACzB,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB;gBAC3C,6CAA6C;gBAC7C,OAAO,IAAI,OAAO,CAAC,aAAa,uBAAuB,OAAO,CAAC,YAAY;YAC7E;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,CAAqB,EAAsB;QACpE,qCAAqC;QACrC,MAAM,YAAY;YAChB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,MAAM,MAAM,QAAQ,IAAI,CAAC;YACzB,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB;gBAC3C,OAAO,IAAI,OAAO,CAAC,aAAa;YAClC;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,CAAqB,EAAsD;QACpG,MAAM,iBAAiB,EAAE;QACzB,MAAM,MAAM,eAAe,IAAI,CAAC,WAAW;QAE3C,MAAM,iBAAiB,EAAE;QACzB,MAAM,UAAU,eAAe,IAAI,GAAG,IAAI,MAAM;QAEhD,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI,MAAM;QAE5C,OAAO;YAAE;YAAK;YAAS;QAAM;IAC/B;IAEQ,mBAAmB,CAAqB,EAAsB;QACpE,yCAAyC;QACzC,MAAM,YAAY;YAChB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,IAAI,SAAS,QAAQ,CAAC,QAAQ;gBAC5B,MAAM,MAAM,QAAQ,IAAI,CAAC;gBACzB,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,OAAO;YACrC,OAAO;gBACL,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI;gBAChC,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI,OAAO;YACvC;QACF;QAEA,OAAO;IACT;IAEQ,cAAc,CAAqB,EAAY;QACrD,MAAM,SAAmB,EAAE;QAE3B,0DAA0D;QAC1D,MAAM,iBAAiB;YACrB,iDAAiD;YACjD;YACA;YACA;YAEA,mCAAmC;YACnC;YACA;YAEA,iEAAiE;YACjE;YACA;YAEA,gDAAgD;YAChD;YACA;YACA;YAEA,uBAAuB;YACvB;YACA;YAEA,qBAAqB;YACrB;YACA;YAEA,mDAAmD;YACnD;SACD;QAED,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC;QAE7C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,WAAW,cAAc,CAAC,EAAE;YAClC,MAAM,WAAW,EAAE;YACnB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,eAAe,MAAM,CAAC,GAAG,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;YAE5G,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,MAAM,QAAQ,EAAE,SAAS,IAAI,GAAG,IAAI;oBACpC,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;oBAE7C,mCAAmC;oBACnC,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,QAAQ,CAAC,QAAQ;wBAC7E,6BAA6B;wBAC7B,MAAM,YAAY;4BAAC;4BAAU;4BAAS;4BAAW;4BAAQ;4BAAO;yBAAM;wBACtE,IAAI,CAAC,UAAU,QAAQ,CAAC,QAAQ;4BAC9B,OAAO,IAAI,CAAC;4BACZ,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;wBACzC;oBACF;gBACF;gBAEA,IAAI,OAAO,MAAM,GAAG,GAAG;oBACrB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;oBACtF,OAAO,4CAA4C;gBACrD;YACF;QACF;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,QAAQ,GAAG,CAAC;YACZ,4DAA4D;YAC5D,MAAM,mBAAmB,EAAE;YAC3B,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,QAAQ,GAAG,CAAC,8BAA8B,iBAAiB,IAAI;YACjE,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,OAAO;IACT;IAEQ,YAAY,CAAqB,EAAY;QACnD,MAAM,OAAiB,EAAE;QAEzB,kCAAkC;QAClC,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,cAAe;YACpC,MAAM,WAAW,EAAE;YACnB,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,MAAM,YAAY,EAAE,SAAS,IAAI,GAAG,IAAI;oBACxC,IAAI,aAAa,CAAC,KAAK,QAAQ,CAAC,cAAc,KAAK,MAAM,GAAG,IAAI;wBAC9D,KAAK,IAAI,CAAC;oBACZ;gBACF;gBACA,OAAO,4CAA4C;YACrD;QACF;QAEA,OAAO;IACT;IAEQ,gBAAgB,CAAqB,EAAsB;QACjE,sCAAsC;QACtC,MAAM,oBAAoB;YACxB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,kBAAmB;YACxC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,WAAW,QAAQ,IAAI,GAAG,IAAI;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,eAAe,CAAqB,EAAsB;QAChE,qDAAqD;QACrD,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI;YACnC,IAAI,SAAS;gBACX,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,gBAAgB,CAAqB,EAAsB;QACjE,sCAAsC;QACtC,MAAM,oBAAoB;YACxB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,kBAAmB;YACxC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,WAAW,QAAQ,IAAI,GAAG,IAAI;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,eAAe,CAAqB,EAAsB;QAChE,qCAAqC;QACrC,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI;YACnC,IAAI,SAAS;gBACX,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,YAAY,MAAc,EAA6B;QAC3D,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;QAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAExC,IAAI,UAAU,IAAI,KAAK,SAAS;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,QAAQ,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxE,MAAM,EAAE,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtE,MAAM,EAAE,KAAK,UAAU,EAAE,SAAS,cAAc,EAAE,OAAO,YAAY,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElG,OAAO;YACL,OAAO,UAAU,KAAK;YACtB,MAAM,UAAU,IAAI;YACpB,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B;YACA;YACA;YACA;YACA,WAAW,IAAI,CAAC,gBAAgB,CAAC;YACjC,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC;YACA;YACA;YACA,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,SAAS,IAAI,CAAC,cAAc,CAAC;QAC/B;IACF;IAEA,MAAM,aAAa,MAAc,EAA8B;QAC7D,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;QAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAExC,IAAI,UAAU,IAAI,KAAK,UAAU;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,QAAQ,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxE,MAAM,EAAE,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtE,MAAM,EAAE,KAAK,UAAU,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAEpD,OAAO;YACL,OAAO,UAAU,KAAK;YACtB,WAAW,UAAU,IAAI;YACzB,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B;YACA;YACA;YACA;YACA,WAAW,IAAI,CAAC,gBAAgB,CAAC;YACjC,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC;YACA,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,cAAc,IAAI,CAAC,mBAAmB,CAAC;YACvC,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACnC;IACF;IAEQ,eAAe,CAAa,EAAsB;QACxD,2CAA2C;QAC3C,MAAM,eAAe,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,OAAO,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC3C,MAAM,YAAY,KAAK,KAAK,CAAC;YAC7B,IAAI,WAAW;gBACb,OAAO,SAAS,SAAS,CAAC,EAAE;YAC9B;QACF;QACA,OAAO;IACT;IAEQ,oBAAoB,CAAa,EAAsB;QAC7D,gDAAgD;QAChD,MAAM,iBAAiB,EAAE;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC7C,MAAM,cAAc,KAAK,KAAK,CAAC;YAC/B,IAAI,aAAa;gBACf,OAAO,SAAS,WAAW,CAAC,EAAE;YAChC;QACF;QACA,OAAO,GAAG,sBAAsB;IAClC;IAEQ,oBAAoB,CAAa,EAAU;QACjD,iDAAiD;QACjD,MAAM,eAAe,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,OAAO,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC3C,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,gBAAgB;gBACpD,OAAO;YACT,OAAO,IAAI,KAAK,KAAK,CAAC,gBAAgB;gBACpC,OAAO;YACT;QACF;QACA,OAAO,WAAW,qBAAqB;IACzC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/vidsrcSync.ts"], "sourcesContent": ["import axios from 'axios';\nimport * as cheerio from 'cheerio';\nimport ContentService from './contentService';\nimport connectDB from './mongodb';\nimport Movie from '@/models/Movie';\nimport Series from '@/models/Series';\nimport Episode from '@/models/Episode';\nimport IMDbScraper from './scraper';\n\ninterface VidSrcMovie {\n  imdb_id: string;\n  tmdb_id?: string;\n  title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\ninterface VidSrcSeries {\n  imdb_id: string;\n  tmdb_id?: string;\n  title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n}\n\ninterface VidSrcEpisode {\n  imdb_id: string;\n  tmdb_id?: string;\n  show_title: string;\n  season: string;\n  episode: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\ninterface VidSrcResponse<T> {\n  result: T[];\n}\n\ninterface SyncStatus {\n  _id?: string;\n  lastSyncTime: Date;\n  nextSyncTime: Date;\n  isRunning: boolean;\n  lastSyncResults: {\n    movies: number;\n    series: number;\n    episodes: number;\n  };\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nclass VidSrcSyncService {\n  private static instance: VidSrcSyncService;\n  private syncInterval: NodeJS.Timeout | null = null;\n  private isInitialized = false;\n  private scraper: IMDbScraper;\n  private readonly PARALLEL_LIMIT = 10; // Process 10 items at a time\n  private readonly BATCH_DELAY = 2000; // 2 second delay between batches\n  private readonly ITEM_DELAY = 200; // 200ms delay between individual items\n\n  private constructor() {\n    this.scraper = IMDbScraper.getInstance();\n  }\n\n  static getInstance(): VidSrcSyncService {\n    if (!VidSrcSyncService.instance) {\n      VidSrcSyncService.instance = new VidSrcSyncService();\n    }\n    return VidSrcSyncService.instance;\n  }\n\n  public async initialize(): Promise<void> {\n    if (this.isInitialized) return;\n\n    await connectDB();\n    await this.checkAndRunSync();\n    this.startSyncScheduler();\n    this.isInitialized = true;\n    console.log('🔄 VidSrc Sync Service initialized');\n  }\n\n  private async processInParallel<T, R>(\n    items: T[],\n    processor: (item: T, index: number) => Promise<R>,\n    batchSize: number = this.PARALLEL_LIMIT\n  ): Promise<R[]> {\n    const results: R[] = [];\n\n    for (let i = 0; i < items.length; i += batchSize) {\n      const batch = items.slice(i, i + batchSize);\n      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(items.length / batchSize)} (${batch.length} items)`);\n\n      // Process batch in parallel with individual delays\n      const batchPromises = batch.map(async (item, batchIndex) => {\n        // Add staggered delay to avoid hitting rate limits\n        await new Promise(resolve => setTimeout(resolve, batchIndex * this.ITEM_DELAY));\n        return processor(item, i + batchIndex);\n      });\n\n      const batchResults = await Promise.allSettled(batchPromises);\n\n      // Collect successful results\n      batchResults.forEach((result, index) => {\n        if (result.status === 'fulfilled') {\n          results.push(result.value);\n        } else {\n          console.error(`❌ Error in batch item ${i + index}:`, result.reason);\n        }\n      });\n\n      // Delay between batches to avoid detection\n      if (i + batchSize < items.length) {\n        console.log(`⏳ Waiting ${this.BATCH_DELAY}ms before next batch...`);\n        await new Promise(resolve => setTimeout(resolve, this.BATCH_DELAY));\n      }\n    }\n\n    return results;\n  }\n\n  private async checkAndRunSync(): Promise<void> {\n    const lastSync = await this.getLastSyncStatus();\n    const now = new Date();\n\n    if (!lastSync || now >= lastSync.nextSyncTime) {\n      console.log('⏰ Sync time reached, starting automatic sync...');\n      await this.runFullSync();\n    } else {\n      const timeUntilNext = lastSync.nextSyncTime.getTime() - now.getTime();\n      console.log(`⏳ Next sync in ${Math.round(timeUntilNext / (1000 * 60 * 60))} hours`);\n    }\n  }\n\n  private startSyncScheduler(): void {\n    // Check every hour if it's time to sync\n    this.syncInterval = setInterval(async () => {\n      await this.checkAndRunSync();\n    }, 60 * 60 * 1000); // 1 hour\n  }\n\n  public async runFullSync(): Promise<{ movies: number; series: number; episodes: number }> {\n    console.log('🚀 Starting VidSrc full sync...');\n    \n    const syncStatus = await this.updateSyncStatus(true);\n    const results = { movies: 0, series: 0, episodes: 0 };\n\n    try {\n      // Sync movies (pages 1-15)\n      console.log('📽️ Syncing movies...');\n      results.movies = await this.syncMovies();\n\n      // Sync series (pages 1-15)\n      console.log('📺 Syncing series...');\n      results.series = await this.syncSeries();\n\n      // Sync episodes (pages 1-15)\n      console.log('🎬 Syncing episodes...');\n      results.episodes = await this.syncEpisodes();\n\n      // Update sync status\n      await this.updateSyncStatus(false, results);\n\n      console.log('✅ VidSrc sync completed:', results);\n      return results;\n\n    } catch (error) {\n      console.error('❌ VidSrc sync failed:', error);\n      await this.updateSyncStatus(false, results);\n      throw error;\n    }\n  }\n\n  private async syncMovies(): Promise<number> {\n    let totalMovies = 0;\n\n    for (let page = 1; page <= 15; page++) {\n      try {\n        const url = `https://vidsrc.xyz/movies/latest/page-${page}.json`;\n        console.log(`📥 Fetching movies page ${page}...`);\n\n        const response = await axios.get<VidSrcResponse<VidSrcMovie>>(url, {\n          timeout: 30000,\n          headers: {\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n          }\n        });\n\n        const movies = response.data.result;\n\n        // Filter out duplicates before processing\n        const existingMovieIds = await Movie.find(\n          { imdbId: { $in: movies.map(m => m.imdb_id) } },\n          { imdbId: 1 }\n        ).lean();\n        const existingIds = new Set(existingMovieIds.map(m => m.imdbId));\n        const newMovies = movies.filter(movie => !existingIds.has(movie.imdb_id));\n\n        console.log(`🎬 Found ${movies.length} movies, ${newMovies.length} new movies to process`);\n\n        if (newMovies.length === 0) {\n          console.log(`⏭️ No new movies on page ${page}, skipping...`);\n          continue;\n        }\n\n        // Process movies in parallel batches\n        const processedMovies = await this.processInParallel(\n          newMovies,\n          async (movieData: VidSrcMovie) => this.processMovie(movieData)\n        );\n\n        const successfulMovies = processedMovies.filter(result => result !== null);\n        totalMovies += successfulMovies.length;\n\n        console.log(`✅ Page ${page}: Added ${successfulMovies.length}/${newMovies.length} movies`);\n\n        // Add delay between pages to avoid rate limiting\n        await new Promise(resolve => setTimeout(resolve, 1000));\n      } catch (error) {\n        console.error(`❌ Error fetching movies page ${page}:`, error);\n      }\n    }\n\n    return totalMovies;\n  }\n\n  private async processMovie(movieData: VidSrcMovie): Promise<any> {\n    try {\n      // Double-check if movie already exists (race condition protection)\n      const existingMovie = await Movie.findOne({ imdbId: movieData.imdb_id });\n      if (existingMovie) {\n        console.log(`⏭️ Movie already exists: ${movieData.title} (${movieData.imdb_id})`);\n        return existingMovie;\n      }\n\n      console.log(`🔍 Scraping metadata for movie: ${movieData.title} (${movieData.imdb_id})`);\n\n      // Scrape complete metadata from IMDb\n      const scrapedData = await this.scraper.scrapeMovie(movieData.imdb_id);\n\n      // Prepare movie data\n      const movieDoc = {\n        imdbId: movieData.imdb_id,\n        tmdbId: movieData.tmdb_id,\n        title: scrapedData.title || movieData.title,\n        year: scrapedData.year || new Date().getFullYear(),\n        rating: scrapedData.rating,\n        runtime: scrapedData.runtime,\n        imdbRating: scrapedData.imdbRating,\n        imdbVotes: scrapedData.imdbVotes,\n        popularity: scrapedData.popularity,\n        popularityDelta: scrapedData.popularityDelta,\n        posterUrl: scrapedData.posterUrl,\n        trailerUrl: scrapedData.trailerUrl,\n        trailerRuntime: scrapedData.trailerRuntime,\n        trailerLikes: scrapedData.trailerLikes,\n        description: scrapedData.description,\n        genres: scrapedData.genres || [],\n        director: scrapedData.director,\n        cast: scrapedData.cast || [],\n        language: scrapedData.language,\n        country: scrapedData.country,\n        embedUrl: movieData.embed_url,\n        embedUrlTmdb: movieData.embed_url_tmdb,\n        vidsrcUrl: movieData.embed_url,\n        vidsrcTmdbUrl: movieData.embed_url_tmdb,\n        quality: movieData.quality\n      };\n\n      // Use findOneAndUpdate with upsert to handle duplicates gracefully\n      const movie = await Movie.findOneAndUpdate(\n        { imdbId: movieData.imdb_id },\n        movieDoc,\n        {\n          upsert: true,\n          new: true,\n          setDefaultsOnInsert: true\n        }\n      );\n\n      console.log(`✅ Added/Updated movie with metadata: ${scrapedData.title || movieData.title} (${movieData.imdb_id})`);\n      return movie;\n    } catch (error) {\n      console.error(`❌ Error processing movie ${movieData.imdb_id}:`, error);\n\n      // Check if it's a duplicate key error\n      if (error.code === 11000) {\n        console.log(`⏭️ Movie already exists (duplicate key): ${movieData.title} (${movieData.imdb_id})`);\n        // Try to find and return the existing movie\n        try {\n          const existingMovie = await Movie.findOne({ imdbId: movieData.imdb_id });\n          return existingMovie;\n        } catch (findError) {\n          console.error(`❌ Error finding existing movie: ${movieData.imdb_id}`, findError);\n          return null;\n        }\n      }\n\n      // Fallback: Create movie with basic data if scraping fails\n      try {\n        const fallbackDoc = {\n          imdbId: movieData.imdb_id,\n          tmdbId: movieData.tmdb_id,\n          title: movieData.title,\n          year: new Date().getFullYear(),\n          description: `Movie: ${movieData.title}`,\n          genres: ['Drama'],\n          imdbRating: 0,\n          runtime: '120 min',\n          language: 'English',\n          country: 'US',\n          director: 'Unknown',\n          cast: [],\n          embedUrl: movieData.embed_url,\n          embedUrlTmdb: movieData.embed_url_tmdb || '',\n          vidsrcUrl: movieData.embed_url,\n          vidsrcTmdbUrl: movieData.embed_url_tmdb,\n          quality: movieData.quality || 'HD'\n        };\n\n        // Use upsert for fallback as well\n        const movie = await Movie.findOneAndUpdate(\n          { imdbId: movieData.imdb_id },\n          fallbackDoc,\n          {\n            upsert: true,\n            new: true,\n            setDefaultsOnInsert: true\n          }\n        );\n\n        console.log(`⚠️ Added/Updated movie with fallback data: ${movieData.title} (${movieData.imdb_id})`);\n        return movie;\n      } catch (fallbackError) {\n        if (fallbackError.code === 11000) {\n          console.log(`⏭️ Movie already exists (fallback duplicate): ${movieData.title} (${movieData.imdb_id})`);\n          try {\n            const existingMovie = await Movie.findOne({ imdbId: movieData.imdb_id });\n            return existingMovie;\n          } catch (findError) {\n            console.error(`❌ Error finding existing movie in fallback: ${movieData.imdb_id}`, findError);\n            return null;\n          }\n        }\n        console.error(`❌ Failed to save movie even with fallback: ${movieData.imdb_id}`, fallbackError);\n        return null;\n      }\n    }\n  }\n\n  private async syncSeries(): Promise<number> {\n    let totalSeries = 0;\n\n    for (let page = 1; page <= 15; page++) {\n      try {\n        const url = `https://vidsrc.xyz/tvshows/latest/page-${page}.json`;\n        console.log(`📥 Fetching series page ${page}...`);\n\n        const response = await axios.get<VidSrcResponse<VidSrcSeries>>(url, {\n          timeout: 30000,\n          headers: {\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n          }\n        });\n\n        const seriesList = response.data.result;\n\n        // Filter out duplicates before processing\n        const existingSeriesIds = await Series.find(\n          { imdbId: { $in: seriesList.map(s => s.imdb_id) } },\n          { imdbId: 1 }\n        ).lean();\n        const existingIds = new Set(existingSeriesIds.map(s => s.imdbId));\n        const newSeries = seriesList.filter(series => !existingIds.has(series.imdb_id));\n\n        console.log(`📺 Found ${seriesList.length} series, ${newSeries.length} new series to process`);\n\n        if (newSeries.length === 0) {\n          console.log(`⏭️ No new series on page ${page}, skipping...`);\n          continue;\n        }\n\n        // Process series in parallel batches\n        const processedSeries = await this.processInParallel(\n          newSeries,\n          async (seriesData: VidSrcSeries) => this.processSeries(seriesData)\n        );\n\n        const successfulSeries = processedSeries.filter(result => result !== null);\n        totalSeries += successfulSeries.length;\n\n        console.log(`✅ Page ${page}: Added ${successfulSeries.length}/${newSeries.length} series`);\n\n      } catch (error) {\n        console.error(`❌ Error fetching series page ${page}:`, error);\n      }\n    }\n\n    return totalSeries;\n  }\n\n  private async processSeries(seriesData: VidSrcSeries): Promise<any> {\n    try {\n      // Double-check if series already exists (race condition protection)\n      const existingSeries = await Series.findOne({ imdbId: seriesData.imdb_id });\n      if (existingSeries) {\n        console.log(`⏭️ Series already exists: ${seriesData.title} (${seriesData.imdb_id})`);\n        return existingSeries;\n      }\n\n      console.log(`🔍 Scraping metadata for series: ${seriesData.title} (${seriesData.imdb_id})`);\n\n      // Scrape complete metadata from IMDb\n      const scrapedData = await this.scraper.scrapeSeries(seriesData.imdb_id);\n\n      // Prepare series data\n      const seriesDoc = {\n        imdbId: seriesData.imdb_id,\n        tmdbId: seriesData.tmdb_id,\n        title: scrapedData.title || seriesData.title,\n        startYear: scrapedData.startYear || new Date().getFullYear(),\n        endYear: scrapedData.endYear,\n        rating: scrapedData.rating,\n        imdbRating: scrapedData.imdbRating,\n        imdbVotes: scrapedData.imdbVotes,\n        popularity: scrapedData.popularity,\n        popularityDelta: scrapedData.popularityDelta,\n        posterUrl: scrapedData.posterUrl,\n        trailerUrl: scrapedData.trailerUrl,\n        description: scrapedData.description,\n        genres: scrapedData.genres || [],\n        creator: scrapedData.creator,\n        cast: scrapedData.cast || [],\n        language: scrapedData.language,\n        country: scrapedData.country,\n        totalSeasons: scrapedData.totalSeasons,\n        status: scrapedData.status,\n        embedUrl: seriesData.embed_url,\n        embedUrlTmdb: seriesData.embed_url_tmdb,\n        vidsrcUrl: seriesData.embed_url,\n        vidsrcTmdbUrl: seriesData.embed_url_tmdb\n      };\n\n      // Use findOneAndUpdate with upsert to handle duplicates gracefully\n      const series = await Series.findOneAndUpdate(\n        { imdbId: seriesData.imdb_id },\n        seriesDoc,\n        {\n          upsert: true,\n          new: true,\n          setDefaultsOnInsert: true\n        }\n      );\n\n      console.log(`✅ Added/Updated series with metadata: ${scrapedData.title || seriesData.title} (${seriesData.imdb_id})`);\n      return series;\n    } catch (error) {\n      console.error(`❌ Error processing series ${seriesData.imdb_id}:`, error);\n\n      // Check if it's a duplicate key error\n      if (error.code === 11000) {\n        console.log(`⏭️ Series already exists (duplicate key): ${seriesData.title} (${seriesData.imdb_id})`);\n        // Try to find and return the existing series\n        try {\n          const existingSeries = await Series.findOne({ imdbId: seriesData.imdb_id });\n          return existingSeries;\n        } catch (findError) {\n          console.error(`❌ Error finding existing series: ${seriesData.imdb_id}`, findError);\n          return null;\n        }\n      }\n\n      // Fallback: Create series with basic data if scraping fails\n      try {\n        const fallbackDoc = {\n          imdbId: seriesData.imdb_id,\n          tmdbId: seriesData.tmdb_id,\n          title: seriesData.title,\n          startYear: new Date().getFullYear(),\n          description: `TV Series: ${seriesData.title}`,\n          genres: ['Drama'],\n          imdbRating: 0,\n          status: 'ongoing',\n          totalSeasons: 1,\n          language: 'English',\n          country: 'US',\n          cast: [],\n          embedUrl: seriesData.embed_url,\n          embedUrlTmdb: seriesData.embed_url_tmdb || '',\n          vidsrcUrl: seriesData.embed_url,\n          vidsrcTmdbUrl: seriesData.embed_url_tmdb\n        };\n\n        // Use upsert for fallback as well\n        const series = await Series.findOneAndUpdate(\n          { imdbId: seriesData.imdb_id },\n          fallbackDoc,\n          {\n            upsert: true,\n            new: true,\n            setDefaultsOnInsert: true\n          }\n        );\n\n        console.log(`⚠️ Added/Updated series with fallback data: ${seriesData.title} (${seriesData.imdb_id})`);\n        return series;\n      } catch (fallbackError) {\n        if (fallbackError.code === 11000) {\n          console.log(`⏭️ Series already exists (fallback duplicate): ${seriesData.title} (${seriesData.imdb_id})`);\n          try {\n            const existingSeries = await Series.findOne({ imdbId: seriesData.imdb_id });\n            return existingSeries;\n          } catch (findError) {\n            console.error(`❌ Error finding existing series in fallback: ${seriesData.imdb_id}`, findError);\n            return null;\n          }\n        }\n        console.error(`❌ Failed to save series even with fallback: ${seriesData.imdb_id}`, fallbackError);\n        return null;\n      }\n    }\n  }\n\n  private async syncEpisodes(): Promise<number> {\n    let totalEpisodes = 0;\n\n    for (let page = 1; page <= 15; page++) {\n      try {\n        const url = `https://vidsrc.xyz/episodes/latest/page-${page}.json`;\n        console.log(`📥 Fetching episodes page ${page}...`);\n        \n        const response = await axios.get<VidSrcResponse<VidSrcEpisode>>(url, {\n          timeout: 30000,\n          headers: {\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n          }\n        });\n\n        const episodes = response.data.result;\n        \n        for (const episodeData of episodes) {\n          try {\n            // Check if episode already exists\n            const existingEpisode = await Episode.findOne({ \n              imdbId: episodeData.imdb_id,\n              season: parseInt(episodeData.season),\n              episode: parseInt(episodeData.episode)\n            });\n            if (existingEpisode) continue;\n\n            // Ensure series exists (auto-create if needed)\n            await this.ensureSeriesExistsForSync(episodeData.imdb_id, episodeData.show_title);\n\n            // Get series for poster URL and genres\n            const series = await Series.findOne({ imdbId: episodeData.imdb_id });\n\n            // Generate VidSrc embed URL using the pattern\n            const embedUrl = `https://vidsrc.me/embed/tv?imdb=${episodeData.imdb_id}&season=${episodeData.season}&episode=${episodeData.episode}`;\n\n            const episode = new Episode({\n              imdbId: episodeData.imdb_id,\n              tmdbId: episodeData.tmdb_id,\n              seriesTitle: episodeData.show_title,\n              season: parseInt(episodeData.season),\n              episode: parseInt(episodeData.episode),\n              episodeTitle: `Episode ${episodeData.episode}`,\n              description: `Episode ${episodeData.episode} of ${episodeData.show_title}`,\n              posterUrl: series?.posterUrl || '',\n              seriesPosterUrl: series?.posterUrl || '',\n              genres: series?.genres || [], // Populate genres from series\n              quality: episodeData.quality || 'HD',\n              embedUrl: embedUrl, // Required field\n              embedUrlTmdb: episodeData.embed_url_tmdb || '',\n              vidsrcUrl: episodeData.embed_url,\n              vidsrcTmdbUrl: episodeData.embed_url_tmdb,\n              runtime: '45 min',\n              airDate: new Date().toISOString(),\n              createdAt: new Date(),\n              updatedAt: new Date()\n            });\n\n            await episode.save();\n            totalEpisodes++;\n            console.log(`✅ Added episode: ${episodeData.show_title} S${episodeData.season}E${episodeData.episode}`);\n          } catch (error) {\n            console.error(`❌ Error processing episode ${episodeData.imdb_id} S${episodeData.season}E${episodeData.episode}:`, error);\n          }\n        }\n\n        // Add delay between pages\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n      } catch (error) {\n        console.error(`❌ Error fetching episodes page ${page}:`, error);\n      }\n    }\n\n    return totalEpisodes;\n  }\n\n  private async ensureSeriesExistsForSync(imdbId: string, title: string): Promise<void> {\n    const existingSeries = await Series.findOne({ imdbId });\n\n    if (!existingSeries) {\n      try {\n        console.log(`🔍 Auto-creating series with metadata: ${title} (${imdbId})`);\n\n        // Scrape complete metadata from IMDb\n        const scrapedData = await this.scraper.scrapeSeries(imdbId);\n\n        const series = new Series({\n          imdbId,\n          title: scrapedData.title || title,\n          startYear: scrapedData.startYear || new Date().getFullYear(),\n          endYear: scrapedData.endYear,\n          rating: scrapedData.rating,\n          imdbRating: scrapedData.imdbRating,\n          imdbVotes: scrapedData.imdbVotes,\n          popularity: scrapedData.popularity,\n          popularityDelta: scrapedData.popularityDelta,\n          posterUrl: scrapedData.posterUrl,\n          trailerUrl: scrapedData.trailerUrl,\n          description: scrapedData.description || `TV Series: ${title}`,\n          genres: scrapedData.genres || [],\n          creator: scrapedData.creator,\n          cast: scrapedData.cast || [],\n          language: scrapedData.language,\n          country: scrapedData.country,\n          totalSeasons: scrapedData.totalSeasons,\n          status: scrapedData.status,\n          embedUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,\n          embedUrlTmdb: '',\n          vidsrcUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,\n          vidsrcTmdbUrl: ''\n        });\n\n        await series.save();\n        console.log(`✅ Auto-created series with metadata: ${scrapedData.title || title} (${imdbId})`);\n      } catch (error) {\n        console.error(`❌ Error scraping series metadata for ${imdbId}:`, error);\n\n        // Fallback: Create series with basic data\n        const series = new Series({\n          imdbId,\n          title,\n          startYear: new Date().getFullYear(),\n          description: `TV Series: ${title}`,\n          genres: ['Drama'],\n          imdbRating: 0,\n          status: 'ongoing',\n          totalSeasons: 1,\n          language: 'English',\n          country: 'US',\n          cast: [],\n          embedUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,\n          embedUrlTmdb: '',\n          vidsrcUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,\n          vidsrcTmdbUrl: ''\n        });\n\n        await series.save();\n        console.log(`⚠️ Auto-created series with fallback data: ${title} (${imdbId})`);\n      }\n    }\n  }\n\n  private async getLastSyncStatus(): Promise<SyncStatus | null> {\n    await connectDB();\n    const { default: SyncStatusModel } = await import('@/models/SyncStatus');\n    return SyncStatusModel.findOne({ syncType: 'VIDSRC_FULL_SYNC' }).sort({ createdAt: -1 }).lean();\n  }\n\n  private async updateSyncStatus(isRunning: boolean, results?: { movies: number; series: number; episodes: number }): Promise<SyncStatus> {\n    await connectDB();\n    const { default: SyncStatusModel } = await import('@/models/SyncStatus');\n\n    const now = new Date();\n    const nextSync = new Date(now.getTime() + 12 * 60 * 60 * 1000); // 12 hours from now\n    const syncType = 'VIDSRC_FULL_SYNC';\n\n    const syncStatus = {\n      syncType,\n      lastSyncTime: isRunning ? null : now,\n      nextSyncTime: nextSync,\n      isRunning,\n      lastSyncResults: results || { movies: 0, series: 0, episodes: 0 },\n      updatedAt: now\n    };\n\n    const existing = await SyncStatusModel.findOne({ syncType });\n\n    if (existing) {\n      Object.assign(existing, syncStatus);\n      return existing.save();\n    } else {\n      return SyncStatusModel.create({\n        ...syncStatus,\n        createdAt: now\n      });\n    }\n  }\n\n  public async getSyncStatus(): Promise<SyncStatus | null> {\n    return this.getLastSyncStatus();\n  }\n\n  public async forcSync(): Promise<{ movies: number; series: number; episodes: number }> {\n    console.log('🔄 Force sync requested...');\n    return this.runFullSync();\n  }\n\n  destroy(): void {\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval);\n      this.syncInterval = null;\n    }\n    this.isInitialized = false;\n    console.log('🛑 VidSrc Sync Service destroyed');\n  }\n}\n\nexport default VidSrcSyncService;\n"], "names": [], "mappings": ";;;AAAA;AAGA;AACA;AACA;AACA;AACA;;;;;;;AAgDA,MAAM;IACJ,OAAe,SAA4B;IACnC,eAAsC,KAAK;IAC3C,gBAAgB,MAAM;IACtB,QAAqB;IACZ,iBAAiB,GAAG;IACpB,cAAc,KAAK;IACnB,aAAa,IAAI;IAElC,aAAsB;QACpB,IAAI,CAAC,OAAO,GAAG,uHAAA,CAAA,UAAW,CAAC,WAAW;IACxC;IAEA,OAAO,cAAiC;QACtC,IAAI,CAAC,kBAAkB,QAAQ,EAAE;YAC/B,kBAAkB,QAAQ,GAAG,IAAI;QACnC;QACA,OAAO,kBAAkB,QAAQ;IACnC;IAEA,MAAa,aAA4B;QACvC,IAAI,IAAI,CAAC,aAAa,EAAE;QAExB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,IAAI,CAAC,eAAe;QAC1B,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;IAEA,MAAc,kBACZ,KAAU,EACV,SAAiD,EACjD,YAAoB,IAAI,CAAC,cAAc,EACzB;QACd,MAAM,UAAe,EAAE;QAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,UAAW;YAChD,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAI;YACjC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,KAAK,KAAK,CAAC,IAAI,aAAa,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,WAAW,EAAE,EAAE,MAAM,MAAM,CAAC,OAAO,CAAC;YAEjI,mDAAmD;YACnD,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO,MAAM;gBAC3C,mDAAmD;gBACnD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,aAAa,IAAI,CAAC,UAAU;gBAC7E,OAAO,UAAU,MAAM,IAAI;YAC7B;YAEA,MAAM,eAAe,MAAM,QAAQ,UAAU,CAAC;YAE9C,6BAA6B;YAC7B,aAAa,OAAO,CAAC,CAAC,QAAQ;gBAC5B,IAAI,OAAO,MAAM,KAAK,aAAa;oBACjC,QAAQ,IAAI,CAAC,OAAO,KAAK;gBAC3B,OAAO;oBACL,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM;gBACpE;YACF;YAEA,2CAA2C;YAC3C,IAAI,IAAI,YAAY,MAAM,MAAM,EAAE;gBAChC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC;gBAClE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,IAAI,CAAC,WAAW;YACnE;QACF;QAEA,OAAO;IACT;IAEA,MAAc,kBAAiC;QAC7C,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB;QAC7C,MAAM,MAAM,IAAI;QAEhB,IAAI,CAAC,YAAY,OAAO,SAAS,YAAY,EAAE;YAC7C,QAAQ,GAAG,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW;QACxB,OAAO;YACL,MAAM,gBAAgB,SAAS,YAAY,CAAC,OAAO,KAAK,IAAI,OAAO;YACnE,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,KAAK,CAAC,gBAAgB,CAAC,OAAO,KAAK,EAAE,GAAG,MAAM,CAAC;QACpF;IACF;IAEQ,qBAA2B;QACjC,wCAAwC;QACxC,IAAI,CAAC,YAAY,GAAG,YAAY;YAC9B,MAAM,IAAI,CAAC,eAAe;QAC5B,GAAG,KAAK,KAAK,OAAO,SAAS;IAC/B;IAEA,MAAa,cAA6E;QACxF,QAAQ,GAAG,CAAC;QAEZ,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB,CAAC;QAC/C,MAAM,UAAU;YAAE,QAAQ;YAAG,QAAQ;YAAG,UAAU;QAAE;QAEpD,IAAI;YACF,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YACZ,QAAQ,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU;YAEtC,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YACZ,QAAQ,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU;YAEtC,6BAA6B;YAC7B,QAAQ,GAAG,CAAC;YACZ,QAAQ,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY;YAE1C,qBAAqB;YACrB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAEnC,QAAQ,GAAG,CAAC,4BAA4B;YACxC,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO;YACnC,MAAM;QACR;IACF;IAEA,MAAc,aAA8B;QAC1C,IAAI,cAAc;QAElB,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;YACrC,IAAI;gBACF,MAAM,MAAM,CAAC,sCAAsC,EAAE,KAAK,KAAK,CAAC;gBAChE,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,GAAG,CAAC;gBAEhD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAA8B,KAAK;oBACjE,SAAS;oBACT,SAAS;wBACP,cAAc;oBAChB;gBACF;gBAEA,MAAM,SAAS,SAAS,IAAI,CAAC,MAAM;gBAEnC,0CAA0C;gBAC1C,MAAM,mBAAmB,MAAM,wHAAA,CAAA,UAAK,CAAC,IAAI,CACvC;oBAAE,QAAQ;wBAAE,KAAK,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;oBAAE;gBAAE,GAC9C;oBAAE,QAAQ;gBAAE,GACZ,IAAI;gBACN,MAAM,cAAc,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC9D,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEvE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,UAAU,MAAM,CAAC,sBAAsB,CAAC;gBAEzF,IAAI,UAAU,MAAM,KAAK,GAAG;oBAC1B,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,aAAa,CAAC;oBAC3D;gBACF;gBAEA,qCAAqC;gBACrC,MAAM,kBAAkB,MAAM,IAAI,CAAC,iBAAiB,CAClD,WACA,OAAO,YAA2B,IAAI,CAAC,YAAY,CAAC;gBAGtD,MAAM,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,SAAU,WAAW;gBACrE,eAAe,iBAAiB,MAAM;gBAEtC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE,iBAAiB,MAAM,CAAC,CAAC,EAAE,UAAU,MAAM,CAAC,OAAO,CAAC;gBAEzF,iDAAiD;gBACjD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC,EAAE;YACzD;QACF;QAEA,OAAO;IACT;IAEA,MAAc,aAAa,SAAsB,EAAgB;QAC/D,IAAI;YACF,mEAAmE;YACnE,MAAM,gBAAgB,MAAM,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAAE,QAAQ,UAAU,OAAO;YAAC;YACtE,IAAI,eAAe;gBACjB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,UAAU,KAAK,CAAC,EAAE,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC;gBAChF,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,UAAU,KAAK,CAAC,EAAE,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC;YAEvF,qCAAqC;YACrC,MAAM,cAAc,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,OAAO;YAEpE,qBAAqB;YACrB,MAAM,WAAW;gBACf,QAAQ,UAAU,OAAO;gBACzB,QAAQ,UAAU,OAAO;gBACzB,OAAO,YAAY,KAAK,IAAI,UAAU,KAAK;gBAC3C,MAAM,YAAY,IAAI,IAAI,IAAI,OAAO,WAAW;gBAChD,QAAQ,YAAY,MAAM;gBAC1B,SAAS,YAAY,OAAO;gBAC5B,YAAY,YAAY,UAAU;gBAClC,WAAW,YAAY,SAAS;gBAChC,YAAY,YAAY,UAAU;gBAClC,iBAAiB,YAAY,eAAe;gBAC5C,WAAW,YAAY,SAAS;gBAChC,YAAY,YAAY,UAAU;gBAClC,gBAAgB,YAAY,cAAc;gBAC1C,cAAc,YAAY,YAAY;gBACtC,aAAa,YAAY,WAAW;gBACpC,QAAQ,YAAY,MAAM,IAAI,EAAE;gBAChC,UAAU,YAAY,QAAQ;gBAC9B,MAAM,YAAY,IAAI,IAAI,EAAE;gBAC5B,UAAU,YAAY,QAAQ;gBAC9B,SAAS,YAAY,OAAO;gBAC5B,UAAU,UAAU,SAAS;gBAC7B,cAAc,UAAU,cAAc;gBACtC,WAAW,UAAU,SAAS;gBAC9B,eAAe,UAAU,cAAc;gBACvC,SAAS,UAAU,OAAO;YAC5B;YAEA,mEAAmE;YACnE,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,gBAAgB,CACxC;gBAAE,QAAQ,UAAU,OAAO;YAAC,GAC5B,UACA;gBACE,QAAQ;gBACR,KAAK;gBACL,qBAAqB;YACvB;YAGF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,YAAY,KAAK,IAAI,UAAU,KAAK,CAAC,EAAE,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC;YACjH,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC,EAAE;YAEhE,sCAAsC;YACtC,IAAI,MAAM,IAAI,KAAK,OAAO;gBACxB,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,UAAU,KAAK,CAAC,EAAE,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC;gBAChG,4CAA4C;gBAC5C,IAAI;oBACF,MAAM,gBAAgB,MAAM,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;wBAAE,QAAQ,UAAU,OAAO;oBAAC;oBACtE,OAAO;gBACT,EAAE,OAAO,WAAW;oBAClB,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,UAAU,OAAO,EAAE,EAAE;oBACtE,OAAO;gBACT;YACF;YAEA,2DAA2D;YAC3D,IAAI;gBACF,MAAM,cAAc;oBAClB,QAAQ,UAAU,OAAO;oBACzB,QAAQ,UAAU,OAAO;oBACzB,OAAO,UAAU,KAAK;oBACtB,MAAM,IAAI,OAAO,WAAW;oBAC5B,aAAa,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;oBACxC,QAAQ;wBAAC;qBAAQ;oBACjB,YAAY;oBACZ,SAAS;oBACT,UAAU;oBACV,SAAS;oBACT,UAAU;oBACV,MAAM,EAAE;oBACR,UAAU,UAAU,SAAS;oBAC7B,cAAc,UAAU,cAAc,IAAI;oBAC1C,WAAW,UAAU,SAAS;oBAC9B,eAAe,UAAU,cAAc;oBACvC,SAAS,UAAU,OAAO,IAAI;gBAChC;gBAEA,kCAAkC;gBAClC,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,gBAAgB,CACxC;oBAAE,QAAQ,UAAU,OAAO;gBAAC,GAC5B,aACA;oBACE,QAAQ;oBACR,KAAK;oBACL,qBAAqB;gBACvB;gBAGF,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,UAAU,KAAK,CAAC,EAAE,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC;gBAClG,OAAO;YACT,EAAE,OAAO,eAAe;gBACtB,IAAI,cAAc,IAAI,KAAK,OAAO;oBAChC,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,UAAU,KAAK,CAAC,EAAE,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC;oBACrG,IAAI;wBACF,MAAM,gBAAgB,MAAM,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;4BAAE,QAAQ,UAAU,OAAO;wBAAC;wBACtE,OAAO;oBACT,EAAE,OAAO,WAAW;wBAClB,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,UAAU,OAAO,EAAE,EAAE;wBAClF,OAAO;oBACT;gBACF;gBACA,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU,OAAO,EAAE,EAAE;gBACjF,OAAO;YACT;QACF;IACF;IAEA,MAAc,aAA8B;QAC1C,IAAI,cAAc;QAElB,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;YACrC,IAAI;gBACF,MAAM,MAAM,CAAC,uCAAuC,EAAE,KAAK,KAAK,CAAC;gBACjE,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,GAAG,CAAC;gBAEhD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAA+B,KAAK;oBAClE,SAAS;oBACT,SAAS;wBACP,cAAc;oBAChB;gBACF;gBAEA,MAAM,aAAa,SAAS,IAAI,CAAC,MAAM;gBAEvC,0CAA0C;gBAC1C,MAAM,oBAAoB,MAAM,yHAAA,CAAA,UAAM,CAAC,IAAI,CACzC;oBAAE,QAAQ;wBAAE,KAAK,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;oBAAE;gBAAE,GAClD;oBAAE,QAAQ;gBAAE,GACZ,IAAI;gBACN,MAAM,cAAc,IAAI,IAAI,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC/D,MAAM,YAAY,WAAW,MAAM,CAAC,CAAA,SAAU,CAAC,YAAY,GAAG,CAAC,OAAO,OAAO;gBAE7E,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,MAAM,CAAC,SAAS,EAAE,UAAU,MAAM,CAAC,sBAAsB,CAAC;gBAE7F,IAAI,UAAU,MAAM,KAAK,GAAG;oBAC1B,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,aAAa,CAAC;oBAC3D;gBACF;gBAEA,qCAAqC;gBACrC,MAAM,kBAAkB,MAAM,IAAI,CAAC,iBAAiB,CAClD,WACA,OAAO,aAA6B,IAAI,CAAC,aAAa,CAAC;gBAGzD,MAAM,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,SAAU,WAAW;gBACrE,eAAe,iBAAiB,MAAM;gBAEtC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE,iBAAiB,MAAM,CAAC,CAAC,EAAE,UAAU,MAAM,CAAC,OAAO,CAAC;YAE3F,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC,EAAE;YACzD;QACF;QAEA,OAAO;IACT;IAEA,MAAc,cAAc,UAAwB,EAAgB;QAClE,IAAI;YACF,oEAAoE;YACpE,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;gBAAE,QAAQ,WAAW,OAAO;YAAC;YACzE,IAAI,gBAAgB;gBAClB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,WAAW,KAAK,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC;gBACnF,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,WAAW,KAAK,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC;YAE1F,qCAAqC;YACrC,MAAM,cAAc,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,OAAO;YAEtE,sBAAsB;YACtB,MAAM,YAAY;gBAChB,QAAQ,WAAW,OAAO;gBAC1B,QAAQ,WAAW,OAAO;gBAC1B,OAAO,YAAY,KAAK,IAAI,WAAW,KAAK;gBAC5C,WAAW,YAAY,SAAS,IAAI,IAAI,OAAO,WAAW;gBAC1D,SAAS,YAAY,OAAO;gBAC5B,QAAQ,YAAY,MAAM;gBAC1B,YAAY,YAAY,UAAU;gBAClC,WAAW,YAAY,SAAS;gBAChC,YAAY,YAAY,UAAU;gBAClC,iBAAiB,YAAY,eAAe;gBAC5C,WAAW,YAAY,SAAS;gBAChC,YAAY,YAAY,UAAU;gBAClC,aAAa,YAAY,WAAW;gBACpC,QAAQ,YAAY,MAAM,IAAI,EAAE;gBAChC,SAAS,YAAY,OAAO;gBAC5B,MAAM,YAAY,IAAI,IAAI,EAAE;gBAC5B,UAAU,YAAY,QAAQ;gBAC9B,SAAS,YAAY,OAAO;gBAC5B,cAAc,YAAY,YAAY;gBACtC,QAAQ,YAAY,MAAM;gBAC1B,UAAU,WAAW,SAAS;gBAC9B,cAAc,WAAW,cAAc;gBACvC,WAAW,WAAW,SAAS;gBAC/B,eAAe,WAAW,cAAc;YAC1C;YAEA,mEAAmE;YACnE,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,gBAAgB,CAC1C;gBAAE,QAAQ,WAAW,OAAO;YAAC,GAC7B,WACA;gBACE,QAAQ;gBACR,KAAK;gBACL,qBAAqB;YACvB;YAGF,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,YAAY,KAAK,IAAI,WAAW,KAAK,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC;YACpH,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC,EAAE;YAElE,sCAAsC;YACtC,IAAI,MAAM,IAAI,KAAK,OAAO;gBACxB,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,WAAW,KAAK,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC;gBACnG,6CAA6C;gBAC7C,IAAI;oBACF,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;wBAAE,QAAQ,WAAW,OAAO;oBAAC;oBACzE,OAAO;gBACT,EAAE,OAAO,WAAW;oBAClB,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,WAAW,OAAO,EAAE,EAAE;oBACxE,OAAO;gBACT;YACF;YAEA,4DAA4D;YAC5D,IAAI;gBACF,MAAM,cAAc;oBAClB,QAAQ,WAAW,OAAO;oBAC1B,QAAQ,WAAW,OAAO;oBAC1B,OAAO,WAAW,KAAK;oBACvB,WAAW,IAAI,OAAO,WAAW;oBACjC,aAAa,CAAC,WAAW,EAAE,WAAW,KAAK,EAAE;oBAC7C,QAAQ;wBAAC;qBAAQ;oBACjB,YAAY;oBACZ,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,SAAS;oBACT,MAAM,EAAE;oBACR,UAAU,WAAW,SAAS;oBAC9B,cAAc,WAAW,cAAc,IAAI;oBAC3C,WAAW,WAAW,SAAS;oBAC/B,eAAe,WAAW,cAAc;gBAC1C;gBAEA,kCAAkC;gBAClC,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,gBAAgB,CAC1C;oBAAE,QAAQ,WAAW,OAAO;gBAAC,GAC7B,aACA;oBACE,QAAQ;oBACR,KAAK;oBACL,qBAAqB;gBACvB;gBAGF,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,WAAW,KAAK,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC;gBACrG,OAAO;YACT,EAAE,OAAO,eAAe;gBACtB,IAAI,cAAc,IAAI,KAAK,OAAO;oBAChC,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,WAAW,KAAK,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC;oBACxG,IAAI;wBACF,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;4BAAE,QAAQ,WAAW,OAAO;wBAAC;wBACzE,OAAO;oBACT,EAAE,OAAO,WAAW;wBAClB,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,WAAW,OAAO,EAAE,EAAE;wBACpF,OAAO;oBACT;gBACF;gBACA,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,WAAW,OAAO,EAAE,EAAE;gBACnF,OAAO;YACT;QACF;IACF;IAEA,MAAc,eAAgC;QAC5C,IAAI,gBAAgB;QAEpB,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;YACrC,IAAI;gBACF,MAAM,MAAM,CAAC,wCAAwC,EAAE,KAAK,KAAK,CAAC;gBAClE,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK,GAAG,CAAC;gBAElD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAgC,KAAK;oBACnE,SAAS;oBACT,SAAS;wBACP,cAAc;oBAChB;gBACF;gBAEA,MAAM,WAAW,SAAS,IAAI,CAAC,MAAM;gBAErC,KAAK,MAAM,eAAe,SAAU;oBAClC,IAAI;wBACF,kCAAkC;wBAClC,MAAM,kBAAkB,MAAM,0HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;4BAC5C,QAAQ,YAAY,OAAO;4BAC3B,QAAQ,SAAS,YAAY,MAAM;4BACnC,SAAS,SAAS,YAAY,OAAO;wBACvC;wBACA,IAAI,iBAAiB;wBAErB,+CAA+C;wBAC/C,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,OAAO,EAAE,YAAY,UAAU;wBAEhF,uCAAuC;wBACvC,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;4BAAE,QAAQ,YAAY,OAAO;wBAAC;wBAElE,8CAA8C;wBAC9C,MAAM,WAAW,CAAC,gCAAgC,EAAE,YAAY,OAAO,CAAC,QAAQ,EAAE,YAAY,MAAM,CAAC,SAAS,EAAE,YAAY,OAAO,EAAE;wBAErI,MAAM,UAAU,IAAI,0HAAA,CAAA,UAAO,CAAC;4BAC1B,QAAQ,YAAY,OAAO;4BAC3B,QAAQ,YAAY,OAAO;4BAC3B,aAAa,YAAY,UAAU;4BACnC,QAAQ,SAAS,YAAY,MAAM;4BACnC,SAAS,SAAS,YAAY,OAAO;4BACrC,cAAc,CAAC,QAAQ,EAAE,YAAY,OAAO,EAAE;4BAC9C,aAAa,CAAC,QAAQ,EAAE,YAAY,OAAO,CAAC,IAAI,EAAE,YAAY,UAAU,EAAE;4BAC1E,WAAW,QAAQ,aAAa;4BAChC,iBAAiB,QAAQ,aAAa;4BACtC,QAAQ,QAAQ,UAAU,EAAE;4BAC5B,SAAS,YAAY,OAAO,IAAI;4BAChC,UAAU;4BACV,cAAc,YAAY,cAAc,IAAI;4BAC5C,WAAW,YAAY,SAAS;4BAChC,eAAe,YAAY,cAAc;4BACzC,SAAS;4BACT,SAAS,IAAI,OAAO,WAAW;4BAC/B,WAAW,IAAI;4BACf,WAAW,IAAI;wBACjB;wBAEA,MAAM,QAAQ,IAAI;wBAClB;wBACA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,YAAY,UAAU,CAAC,EAAE,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,EAAE;oBACxG,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,YAAY,OAAO,CAAC,EAAE,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,CAAC,CAAC,EAAE;oBACpH;gBACF;gBAEA,0BAA0B;gBAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC,EAAE;YAC3D;QACF;QAEA,OAAO;IACT;IAEA,MAAc,0BAA0B,MAAc,EAAE,KAAa,EAAiB;QACpF,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;YAAE;QAAO;QAErD,IAAI,CAAC,gBAAgB;YACnB,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;gBAEzE,qCAAqC;gBACrC,MAAM,cAAc,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;gBAEpD,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;oBACxB;oBACA,OAAO,YAAY,KAAK,IAAI;oBAC5B,WAAW,YAAY,SAAS,IAAI,IAAI,OAAO,WAAW;oBAC1D,SAAS,YAAY,OAAO;oBAC5B,QAAQ,YAAY,MAAM;oBAC1B,YAAY,YAAY,UAAU;oBAClC,WAAW,YAAY,SAAS;oBAChC,YAAY,YAAY,UAAU;oBAClC,iBAAiB,YAAY,eAAe;oBAC5C,WAAW,YAAY,SAAS;oBAChC,YAAY,YAAY,UAAU;oBAClC,aAAa,YAAY,WAAW,IAAI,CAAC,WAAW,EAAE,OAAO;oBAC7D,QAAQ,YAAY,MAAM,IAAI,EAAE;oBAChC,SAAS,YAAY,OAAO;oBAC5B,MAAM,YAAY,IAAI,IAAI,EAAE;oBAC5B,UAAU,YAAY,QAAQ;oBAC9B,SAAS,YAAY,OAAO;oBAC5B,cAAc,YAAY,YAAY;oBACtC,QAAQ,YAAY,MAAM;oBAC1B,UAAU,CAAC,gCAAgC,EAAE,QAAQ;oBACrD,cAAc;oBACd,WAAW,CAAC,gCAAgC,EAAE,QAAQ;oBACtD,eAAe;gBACjB;gBAEA,MAAM,OAAO,IAAI;gBACjB,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,YAAY,KAAK,IAAI,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;YAC9F,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC,EAAE;gBAEjE,0CAA0C;gBAC1C,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;oBACxB;oBACA;oBACA,WAAW,IAAI,OAAO,WAAW;oBACjC,aAAa,CAAC,WAAW,EAAE,OAAO;oBAClC,QAAQ;wBAAC;qBAAQ;oBACjB,YAAY;oBACZ,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,SAAS;oBACT,MAAM,EAAE;oBACR,UAAU,CAAC,gCAAgC,EAAE,QAAQ;oBACrD,cAAc;oBACd,WAAW,CAAC,gCAAgC,EAAE,QAAQ;oBACtD,eAAe;gBACjB;gBAEA,MAAM,OAAO,IAAI;gBACjB,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;YAC/E;QACF;IACF;IAEA,MAAc,oBAAgD;QAC5D,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,EAAE,SAAS,eAAe,EAAE,GAAG;QACrC,OAAO,gBAAgB,OAAO,CAAC;YAAE,UAAU;QAAmB,GAAG,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GAAG,IAAI;IAC/F;IAEA,MAAc,iBAAiB,SAAkB,EAAE,OAA8D,EAAuB;QACtI,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,EAAE,SAAS,eAAe,EAAE,GAAG;QAErC,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,OAAO,oBAAoB;QACpF,MAAM,WAAW;QAEjB,MAAM,aAAa;YACjB;YACA,cAAc,YAAY,OAAO;YACjC,cAAc;YACd;YACA,iBAAiB,WAAW;gBAAE,QAAQ;gBAAG,QAAQ;gBAAG,UAAU;YAAE;YAChE,WAAW;QACb;QAEA,MAAM,WAAW,MAAM,gBAAgB,OAAO,CAAC;YAAE;QAAS;QAE1D,IAAI,UAAU;YACZ,OAAO,MAAM,CAAC,UAAU;YACxB,OAAO,SAAS,IAAI;QACtB,OAAO;YACL,OAAO,gBAAgB,MAAM,CAAC;gBAC5B,GAAG,UAAU;gBACb,WAAW;YACb;QACF;IACF;IAEA,MAAa,gBAA4C;QACvD,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA,MAAa,WAA0E;QACrF,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,UAAgB;QACd,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,cAAc,IAAI,CAAC,YAAY;YAC/B,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/sync/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport VidSrcSyncService from '@/lib/vidsrcSync';\n\nconst syncService = VidSrcSyncService.getInstance();\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const action = searchParams.get('action');\n\n    if (action === 'status') {\n      const status = await syncService.getSyncStatus();\n      return NextResponse.json({\n        success: true,\n        data: status\n      });\n    }\n\n    if (action === 'force') {\n      const results = await syncService.forcSync();\n      return NextResponse.json({\n        success: true,\n        message: 'Force sync completed',\n        data: results\n      });\n    }\n\n    // Default: return sync status\n    const status = await syncService.getSyncStatus();\n    return NextResponse.json({\n      success: true,\n      data: status\n    });\n\n  } catch (error) {\n    console.error('Error in sync API:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to process sync request',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action } = body;\n\n    if (action === 'force') {\n      const results = await syncService.forcSync();\n      return NextResponse.json({\n        success: true,\n        message: `Force sync completed: ${results.movies} movies, ${results.series} series, ${results.episodes} episodes`,\n        data: results\n      });\n    }\n\n    // Fallback to old sync method if no action specified\n    const results = await syncService.forcSync();\n    return NextResponse.json({\n      success: true,\n      message: `Successfully synced content: ${results.movies} movies, ${results.series} series, ${results.episodes} episodes`,\n      counts: results\n    });\n\n  } catch (error) {\n    console.error('Error in sync POST API:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to process sync request',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,cAAc,0HAAA,CAAA,UAAiB,CAAC,WAAW;AAE1C,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,WAAW,UAAU;YACvB,MAAM,SAAS,MAAM,YAAY,aAAa;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;QAEA,IAAI,WAAW,SAAS;YACtB,MAAM,UAAU,MAAM,YAAY,QAAQ;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF;QAEA,8BAA8B;QAC9B,MAAM,SAAS,MAAM,YAAY,aAAa;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,IAAI,WAAW,SAAS;YACtB,MAAM,UAAU,MAAM,YAAY,QAAQ;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS,CAAC,sBAAsB,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,QAAQ,CAAC,SAAS,CAAC;gBACjH,MAAM;YACR;QACF;QAEA,qDAAqD;QACrD,MAAM,UAAU,MAAM,YAAY,QAAQ;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS,CAAC,6BAA6B,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,QAAQ,CAAC,SAAS,CAAC;YACxH,QAAQ;QACV;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}