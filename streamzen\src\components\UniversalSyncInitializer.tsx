'use client';

import { useEffect, useState } from 'react';

interface SyncStatus {
  isRunning: boolean;
  lastSyncTime: string | null;
  nextSyncTime: string | null;
  syncType: string;
}

interface InitStatus {
  initialized: boolean;
  error: string | null;
  status: SyncStatus | null;
  utcTime: string | null;
}

export default function UniversalSyncInitializer() {
  const [initStatus, setInitStatus] = useState<InitStatus>({
    initialized: false,
    error: null,
    status: null,
    utcTime: null
  });

  useEffect(() => {
    const initializeUniversalSync = async () => {
      try {
        console.log('🌍 Initializing Universal Sync Service...');
        
        const response = await fetch('/api/admin/init-universal-sync', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const result = await response.json();

        if (result.success) {
          console.log('✅ Universal Sync Service initialized:', result.message);
          console.log('🕐 UTC Time:', result.utcTime);
          console.log('📅 Next Sync:', result.status?.nextSyncTime);
          
          setInitStatus({
            initialized: true,
            error: null,
            status: result.status,
            utcTime: result.utcTime
          });
        } else {
          console.error('❌ Failed to initialize Universal Sync Service:', result.message);
          setInitStatus({
            initialized: false,
            error: result.message || 'Unknown error',
            status: null,
            utcTime: result.utcTime
          });
        }
      } catch (error) {
        console.error('❌ Error initializing Universal Sync Service:', error);
        setInitStatus({
          initialized: false,
          error: error.message || 'Network error',
          status: null,
          utcTime: new Date().toISOString()
        });
      }
    };

    // Initialize on component mount
    initializeUniversalSync();

    // Set up periodic status checks every 5 minutes
    const statusInterval = setInterval(async () => {
      try {
        const response = await fetch('/api/sync/universal?action=status');
        const result = await response.json();
        
        if (result.success && result.data) {
          setInitStatus(prev => ({
            ...prev,
            status: {
              isRunning: result.data.isRunning,
              lastSyncTime: result.data.lastSyncTime,
              nextSyncTime: result.data.nextSyncTime,
              syncType: result.data.syncType
            },
            utcTime: result.utcTime
          }));
        }
      } catch (error) {
        console.error('❌ Error checking sync status:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(statusInterval);
    };
  }, []);

  // Format time for display
  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'Never';
    return new Date(timeString).toLocaleString('en-US', {
      timeZone: 'UTC',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    });
  };

  // Calculate time until next sync
  const getTimeUntilNextSync = () => {
    if (!initStatus.status?.nextSyncTime) return 'Unknown';
    
    const now = new Date();
    const nextSync = new Date(initStatus.status.nextSyncTime);
    const diff = nextSync.getTime() - now.getTime();
    
    if (diff <= 0) return 'Due now';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  // This component doesn't render anything visible, but shows status in console
  // You can uncomment the JSX below if you want a visible status indicator

  return null;

  /* Uncomment this section if you want a visible status indicator
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-black/80 backdrop-blur-sm rounded-lg border border-gray-700 p-4 text-white text-xs max-w-sm">
        <div className="flex items-center space-x-2 mb-2">
          <div className={`w-2 h-2 rounded-full ${
            initStatus.initialized 
              ? (initStatus.status?.isRunning ? 'bg-yellow-400 animate-pulse' : 'bg-green-400')
              : 'bg-red-400'
          }`} />
          <span className="font-semibold">Universal Sync</span>
        </div>
        
        {initStatus.error ? (
          <div className="text-red-400">Error: {initStatus.error}</div>
        ) : (
          <div className="space-y-1">
            <div>Status: {initStatus.status?.isRunning ? 'Running' : 'Scheduled'}</div>
            <div>Last: {formatTime(initStatus.status?.lastSyncTime)}</div>
            <div>Next: {getTimeUntilNextSync()}</div>
            <div>UTC: {formatTime(initStatus.utcTime)}</div>
          </div>
        )}
      </div>
    </div>
  );
  */
}
