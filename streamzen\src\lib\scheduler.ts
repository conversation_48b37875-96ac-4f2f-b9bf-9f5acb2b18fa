// Simple scheduler for VidSrc sync
let syncInterval: NodeJS.Timeout | null = null;

export class VidSrcScheduler {
  private static instance: VidSrcScheduler;
  private isRunning = false;

  static getInstance(): VidSrcScheduler {
    if (!VidSrcScheduler.instance) {
      VidSrcScheduler.instance = new VidSrcScheduler();
    }
    return VidSrcScheduler.instance;
  }

  async startDailySync() {
    if (this.isRunning) {
      console.log('📅 VidSrc sync scheduler already running');
      return;
    }

    console.log('📅 Starting VidSrc daily sync scheduler');
    this.isRunning = true;

    // Run immediately on startup
    await this.runSync();

    // Then run every 24 hours (86400000 ms)
    syncInterval = setInterval(async () => {
      await this.runSync();
    }, 24 * 60 * 60 * 1000);
  }

  stopSync() {
    if (syncInterval) {
      clearInterval(syncInterval);
      syncInterval = null;
    }
    this.isRunning = false;
    console.log('📅 VidSrc sync scheduler stopped');
  }

  private async runSync() {
    try {
      console.log('🚀 Starting scheduled VidSrc episodes sync...');
      
      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/sync/vidsrc-episodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Scheduled sync completed:', result.stats);
      } else {
        console.error('❌ Scheduled sync failed:', response.status);
      }
    } catch (error) {
      console.error('❌ Scheduled sync error:', error);
    }
  }
}

// Auto-start scheduler in production
if (process.env.NODE_ENV === 'production') {
  VidSrcScheduler.getInstance().startDailySync();
}
