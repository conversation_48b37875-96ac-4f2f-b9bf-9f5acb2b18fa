import { NextRequest, NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imdbIds, contentType = 'auto' } = body;
    
    if (!imdbIds || !Array.isArray(imdbIds) || imdbIds.length === 0) {
      return NextResponse.json(
        { error: 'imdbIds array is required and cannot be empty' },
        { status: 400 }
      );
    }

    // Validate IMDb IDs format
    const invalidIds = imdbIds.filter(id => !id.startsWith('tt') || id.length < 9);
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid IMDb IDs: ${invalidIds.join(', ')}` },
        { status: 400 }
      );
    }

    // Get client IP for tracking
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
    
    const requestRecord = await contentService.createBulkRequest(imdbIds, ip, contentType);
    
    return NextResponse.json({
      requestId: requestRecord._id,
      status: requestRecord.status,
      totalCount: requestRecord.totalCount,
      message: 'Request submitted successfully. Processing will begin shortly.'
    });
  } catch (error) {
    console.error('Error creating bulk request:', error);
    return NextResponse.json(
      { error: 'Failed to create request' },
      { status: 500 }
    );
  }
}
