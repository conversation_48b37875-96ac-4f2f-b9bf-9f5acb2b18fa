const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

async function checkExistingSeries() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI;
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Count existing series
    const seriesCount = await mongoose.connection.db.collection('series').countDocuments();
    console.log(`📺 Existing series in database: ${seriesCount}`);

    // If there are series, show some sample data
    if (seriesCount > 0) {
      const sampleSeries = await mongoose.connection.db.collection('series')
        .find({})
        .limit(3)
        .toArray();
      
      console.log('\n📋 Sample existing series:');
      sampleSeries.forEach((series, index) => {
        console.log(`${index + 1}. ${series.title} (${series.imdbId}) - ${series.startYear}`);
      });
    }

    console.log(`\n🎯 Expected behavior:`);
    console.log(`- Series scraper will check each IMDb ID against database`);
    console.log(`- Existing series will be skipped (no re-scraping)`);
    console.log(`- Only new series will be scraped and saved`);
    console.log(`- This saves time and prevents duplicates`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

checkExistingSeries();
