const mongoose = require('mongoose');

async function checkGenres() {
  try {
    await mongoose.connect('mongodb+srv://saimmanchester12121212:<EMAIL>/streamzen?retryWrites=true&w=majority&appName=Cluster0');
    console.log('🔗 Connected to MongoDB');

    const Movie = mongoose.model('Movie', new mongoose.Schema({}, { strict: false }));
    const movies = await Movie.find({}).limit(10).select('title genres language country');
    
    console.log('📊 Recent movies with metadata:');
    movies.forEach((movie, index) => {
      console.log(`${index + 1}. ${movie.title}:`);
      console.log(`   Genres: [${movie.genres?.join(', ') || 'none'}]`);
      console.log(`   Language: ${movie.language || 'none'}`);
      console.log(`   Country: ${movie.country || 'none'}`);
      console.log('');
    });

    const totalMovies = await Movie.countDocuments();
    console.log(`📈 Total movies in database: ${totalMovies}`);

    // Check genre distribution
    const genreStats = await Movie.aggregate([
      { $unwind: '$genres' },
      { $group: { _id: '$genres', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    console.log('🎭 Top genres:');
    genreStats.forEach(stat => {
      console.log(`   ${stat._id}: ${stat.count} movies`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

checkGenres();
