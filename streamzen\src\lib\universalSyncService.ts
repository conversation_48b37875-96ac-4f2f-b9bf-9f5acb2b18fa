import connectDB from './mongodb';
import SyncStatus from '@/models/SyncStatus';

interface UniversalSyncResult {
  success: boolean;
  timestamp: string;
  utcTime: string;
  nextSyncTime: string;
  results: {
    movies: {
      success: boolean;
      count: number;
      duration: string;
      error?: string;
    };
    series: {
      success: boolean;
      count: number;
      duration: string;
      error?: string;
    };
    episodes: {
      success: boolean;
      count: number;
      duration: string;
      error?: string;
    };
    seriesEpisodes: {
      success: boolean;
      seriesProcessed: number;
      episodesAdded: number;
      duration: string;
      error?: string;
    };
  };
  totalDuration: string;
}

class UniversalSyncService {
  private static instance: UniversalSyncService;
  private syncInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private readonly SYNC_TYPE = 'UNIVERSAL_SYNC';
  private readonly SYNC_INTERVAL_HOURS = 3;

  private constructor() {}

  public static getInstance(): UniversalSyncService {
    if (!UniversalSyncService.instance) {
      UniversalSyncService.instance = new UniversalSyncService();
    }
    return UniversalSyncService.instance;
  }

  /**
   * Initialize the universal sync scheduler
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🔄 Universal Sync Service already initialized');
      return;
    }

    try {
      await connectDB();
      console.log('🚀 Initializing Universal Sync Service...');

      // Check if we need to create initial sync status
      await this.ensureSyncStatus();

      // Start the scheduler
      this.startUniversalScheduler();

      // Check if we need to sync immediately
      await this.checkAndRunSync();

      this.isInitialized = true;
      console.log('✅ Universal Sync Service initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize Universal Sync Service:', error);
      throw error;
    }
  }

  /**
   * Ensure sync status exists in database
   */
  private async ensureSyncStatus(): Promise<void> {
    try {
      await connectDB();
      const { default: SyncStatus } = await import('@/models/SyncStatus');

      const existing = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });

      if (!existing) {
        const now = new Date();
        const nextSync = this.calculateNextSyncTime(now);

        console.log(`📅 Creating initial Universal Sync status...`);
        console.log(`📅 Next sync scheduled for: ${nextSync.toISOString()}`);

        const newSyncStatus = await SyncStatus.create({
          syncType: this.SYNC_TYPE,
          lastSyncTime: new Date(0), // Never synced (epoch time)
          nextSyncTime: nextSync,
          isRunning: false,
          lastResult: null
        });

        console.log(`✅ Created initial sync status with ID: ${newSyncStatus._id}`);
      } else {
        // Check if existing record needs fixing
        let needsUpdate = false;
        const updateData: any = {};

        if (!existing.nextSyncTime) {
          const now = new Date();
          updateData.nextSyncTime = this.calculateNextSyncTime(now);
          needsUpdate = true;
          console.log(`🔧 Fixing missing nextSyncTime`);
        }

        if (!existing.lastSyncTime) {
          updateData.lastSyncTime = new Date(0);
          needsUpdate = true;
          console.log(`🔧 Fixing missing lastSyncTime`);
        }

        if (typeof existing.isRunning !== 'boolean') {
          updateData.isRunning = false;
          needsUpdate = true;
          console.log(`🔧 Fixing isRunning field`);
        }

        if (needsUpdate) {
          await SyncStatus.updateOne({ syncType: this.SYNC_TYPE }, updateData);
          console.log(`🔧 Fixed Universal Sync status record`);
        }

        const nextSyncTime = updateData.nextSyncTime || existing.nextSyncTime;
        console.log(`📋 Universal Sync status ready. Next sync: ${nextSyncTime?.toISOString() || 'Unknown'}`);
      }
    } catch (error) {
      console.error('❌ Error ensuring sync status:', error);
      throw error;
    }
  }

  /**
   * Calculate next sync time based on UTC 3-hour intervals
   */
  private calculateNextSyncTime(fromTime: Date): Date {
    const utcTime = new Date(fromTime.getTime());
    
    // Get current UTC hour
    const currentHour = utcTime.getUTCHours();
    
    // Find next 3-hour interval (0, 3, 6, 9, 12, 15, 18, 21)
    const nextSyncHour = Math.ceil((currentHour + 1) / this.SYNC_INTERVAL_HOURS) * this.SYNC_INTERVAL_HOURS;
    
    // Set to next sync time
    const nextSync = new Date(utcTime);
    nextSync.setUTCHours(nextSyncHour % 24, 0, 0, 0);
    
    // If we've passed today's last sync, move to tomorrow
    if (nextSyncHour >= 24) {
      nextSync.setUTCDate(nextSync.getUTCDate() + 1);
    }
    
    return nextSync;
  }

  /**
   * Check current UTC time and determine if sync should run
   */
  private async checkAndRunSync(): Promise<void> {
    try {
      await connectDB();
      const { default: SyncStatus } = await import('@/models/SyncStatus');

      const syncStatus = await SyncStatus.findOne({ syncType: this.SYNC_TYPE });
      if (!syncStatus) {
        console.log('⚠️ No sync status found, ensuring sync status...');
        await this.ensureSyncStatus();
        return;
      }

      // Check if nextSyncTime is missing or invalid
      if (!syncStatus.nextSyncTime) {
        console.log('⚠️ nextSyncTime is missing, fixing sync status...');
        const now = new Date();
        const nextSync = this.calculateNextSyncTime(now);

        await SyncStatus.updateOne(
          { syncType: this.SYNC_TYPE },
          { nextSyncTime: nextSync }
        );

        console.log(`📅 Fixed nextSyncTime: ${nextSync.toISOString()}`);
        return;
      }

      const now = new Date();
      const utcNow = new Date(now.getTime());

      console.log(`🕐 UTC Time Check: ${utcNow.toISOString()}`);
      console.log(`📅 Next Scheduled Sync: ${syncStatus.nextSyncTime.toISOString()}`);

      // Check if it's time to sync and not already running
      if (utcNow >= syncStatus.nextSyncTime && !syncStatus.isRunning) {
        console.log('⏰ UTC Time reached for universal sync!');
        console.log('🚀 Starting universal sync process...');
        await this.runUniversalSync();
      } else if (syncStatus.isRunning) {
        console.log('🔄 Sync already running, skipping...');
        console.log(`⚠️ Sync has been running since: ${syncStatus.updatedAt?.toISOString() || 'Unknown'}`);
        console.log(`💡 If this is stuck, use: GET /api/sync/universal?action=reset`);
      } else {
        const timeUntilNext = syncStatus.nextSyncTime.getTime() - utcNow.getTime();
        const hoursUntilNext = Math.round(timeUntilNext / (1000 * 60 * 60 * 100)) / 10;
        console.log(`⏳ Next universal sync in ${hoursUntilNext} hours`);
        console.log(`📊 Sync Status: isRunning=${syncStatus.isRunning}, lastSync=${syncStatus.lastSyncTime?.toISOString() || 'Never'}`);
      }

    } catch (error) {
      console.error('❌ Error in universal sync check:', error);
    }
  }

  /**
   * Start the universal scheduler that checks every 30 minutes
   */
  private startUniversalScheduler(): void {
    // Check every 30 minutes if it's time to sync
    this.syncInterval = setInterval(async () => {
      await this.checkAndRunSync();
    }, 30 * 60 * 1000); // 30 minutes

    console.log('🔄 Universal sync scheduler started (checks every 30 minutes)');
  }

  /**
   * Run the complete universal sync process
   */
  public async runUniversalSync(): Promise<UniversalSyncResult> {
    const startTime = Date.now();
    const utcTime = new Date();
    
    console.log('🌍 STARTING UNIVERSAL SYNC PROCESS');
    console.log(`🕐 UTC Time: ${utcTime.toISOString()}`);
    
    // Mark sync as running
    await this.updateSyncStatus(true);
    
    const results: UniversalSyncResult = {
      success: false,
      timestamp: new Date().toISOString(),
      utcTime: utcTime.toISOString(),
      nextSyncTime: '',
      results: {
        movies: { success: false, count: 0, duration: '0s' },
        series: { success: false, count: 0, duration: '0s' },
        episodes: { success: false, count: 0, duration: '0s' },
        seriesEpisodes: { success: false, seriesProcessed: 0, episodesAdded: 0, duration: '0s' }
      },
      totalDuration: '0s'
    };

    try {
      // STEP 1: Sync Movies
      console.log('🎬 STEP 1: Syncing Movies...');
      const step1Start = Date.now();
      results.results.movies = await this.syncMovies();
      const step1Duration = (Date.now() - step1Start) / 1000;
      console.log(`✅ STEP 1 COMPLETED: Movies sync finished in ${step1Duration}s`);

      // STEP 2: Sync Series
      console.log('📺 STEP 2: Syncing Series...');
      const step2Start = Date.now();
      results.results.series = await this.syncSeries();
      const step2Duration = (Date.now() - step2Start) / 1000;
      console.log(`✅ STEP 2 COMPLETED: Series sync finished in ${step2Duration}s`);

      // STEP 3: Sync Episodes
      console.log('🎭 STEP 3: Syncing Episodes...');
      const step3Start = Date.now();
      results.results.episodes = await this.syncEpisodes();
      const step3Duration = (Date.now() - step3Start) / 1000;
      console.log(`✅ STEP 3 COMPLETED: Episodes sync finished in ${step3Duration}s`);

      // STEP 4: Series Episodes Sync REMOVED
      // Episodes are now synced individually when users visit series pages
      console.log('📚 STEP 4: Series Episodes Sync - SKIPPED (handled on series page visits)');
      results.results.seriesEpisodes = {
        success: true,
        seriesProcessed: 0,
        episodesAdded: 0,
        duration: '0s'
      };

      // Calculate next sync time
      const nextSync = this.calculateNextSyncTime(utcTime);
      results.nextSyncTime = nextSync.toISOString();
      
      // Mark as successful if at least one sync succeeded
      results.success = results.results.movies.success ||
                       results.results.series.success ||
                       results.results.episodes.success ||
                       results.results.seriesEpisodes.success;
      
      const totalDuration = (Date.now() - startTime) / 1000;
      const totalMinutes = Math.round(totalDuration / 60 * 100) / 100;
      const endTime = new Date().toISOString();
      results.totalDuration = `${totalDuration}s`;

      // Update sync status
      await this.updateSyncStatus(false, results, nextSync);

      console.log('🎉 UNIVERSAL SYNC COMPLETED SUCCESSFULLY!');
      console.log('═══════════════════════════════════════════════════════════');
      console.log(`⏰ Start Time: ${utcTime.toISOString()}`);
      console.log(`⏰ End Time: ${endTime}`);
      console.log(`⏱️ TOTAL DURATION: ${totalDuration}s (${totalMinutes} minutes)`);
      console.log('═══════════════════════════════════════════════════════════');
      console.log('📊 STEP-BY-STEP BREAKDOWN:');
      console.log(`   🎬 Step 1 - Movies: ${results.results.movies.duration} (${results.results.movies.count} items)`);
      console.log(`   📺 Step 2 - Series: ${results.results.series.duration} (${results.results.series.count} items)`);
      console.log(`   🎭 Step 3 - Episodes: ${results.results.episodes.duration} (${results.results.episodes.count} items)`);
      console.log(`   📚 Step 4 - Series Episodes: SKIPPED (handled on-demand when users visit series pages)`);
      console.log('═══════════════════════════════════════════════════════════');
      console.log(`📅 Next Sync Scheduled: ${results.nextSyncTime}`);
      console.log('═══════════════════════════════════════════════════════════');
      
      return results;
      
    } catch (error) {
      const totalDuration = (Date.now() - startTime) / 1000;
      const totalMinutes = Math.round(totalDuration / 60 * 100) / 100;
      const endTime = new Date().toISOString();
      results.totalDuration = `${totalDuration}s`;

      console.error('❌ UNIVERSAL SYNC FAILED!');
      console.error('═══════════════════════════════════════════════════════════');
      console.error(`⏰ Start Time: ${utcTime.toISOString()}`);
      console.error(`⏰ End Time: ${endTime}`);
      console.error(`⏱️ DURATION BEFORE FAILURE: ${totalDuration}s (${totalMinutes} minutes)`);
      console.error(`❌ Error: ${error.message}`);
      console.error('═══════════════════════════════════════════════════════════');

      // Update sync status with error
      await this.updateSyncStatus(false, results);

      throw error;
    }
  }

  /**
   * Sync movies using the full VidSrc sync
   */
  private async syncMovies(): Promise<{ success: boolean; count: number; duration: string; error?: string }> {
    const startTime = Date.now();
    
    try {
      console.log('📥 Calling VidSrc movies sync...');
      
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const response = await fetch(`${baseUrl}/api/sync?action=force`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const result = await response.json();
      const duration = (Date.now() - startTime) / 1000;
      
      if (response.ok && result.success) {
        console.log(`✅ Movies sync completed: ${result.data?.movies || 0} movies`);
        return {
          success: true,
          count: result.data?.movies || 0,
          duration: `${duration}s`
        };
      } else {
        throw new Error(result.message || 'Movies sync failed');
      }
      
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.error('❌ Movies sync error:', error);
      return {
        success: false,
        count: 0,
        duration: `${duration}s`,
        error: error.message
      };
    }
  }

  /**
   * Sync series using the full VidSrc sync
   */
  private async syncSeries(): Promise<{ success: boolean; count: number; duration: string; error?: string }> {
    const startTime = Date.now();
    
    try {
      console.log('📥 Calling VidSrc series sync...');
      
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const response = await fetch(`${baseUrl}/api/sync?action=force`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const result = await response.json();
      const duration = (Date.now() - startTime) / 1000;
      
      if (response.ok && result.success) {
        console.log(`✅ Series sync completed: ${result.data?.series || 0} series`);
        return {
          success: true,
          count: result.data?.series || 0,
          duration: `${duration}s`
        };
      } else {
        throw new Error(result.message || 'Series sync failed');
      }
      
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.error('❌ Series sync error:', error);
      return {
        success: false,
        count: 0,
        duration: `${duration}s`,
        error: error.message
      };
    }
  }

  /**
   * Sync episodes using the dedicated episodes sync
   */
  private async syncEpisodes(): Promise<{ success: boolean; count: number; duration: string; error?: string }> {
    const startTime = Date.now();
    
    try {
      console.log('📥 Calling VidSrc episodes sync...');
      
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const response = await fetch(`${baseUrl}/api/sync/vidsrc-episodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const result = await response.json();
      const duration = (Date.now() - startTime) / 1000;
      
      if (response.ok && result.success) {
        console.log(`✅ Episodes sync completed: ${result.stats?.totalEpisodes || 0} episodes`);
        return {
          success: true,
          count: result.stats?.totalEpisodes || 0,
          duration: `${duration}s`
        };
      } else {
        throw new Error(result.message || 'Episodes sync failed');
      }
      
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.error('❌ Episodes sync error:', error);
      return {
        success: false,
        count: 0,
        duration: `${duration}s`,
        error: error.message
      };
    }
  }

  /**
   * Sync ALL episodes for ALL series in database using IMDb data (Comprehensive)
   * This replaces the individual episode syncing that happened on watch page visits
   */
  private async syncAllSeriesEpisodes(): Promise<{ success: boolean; seriesProcessed: number; episodesAdded: number; duration: string; error?: string }> {
    const startTime = Date.now();

    try {
      console.log('📚 COMPREHENSIVE SERIES EPISODES SYNC - IMDb Based');
      console.log('🎯 Goal: Fetch ALL episodes for ALL series from IMDb and save to database');
      console.log('📋 This replaces individual episode syncing from watch pages');

      await connectDB();
      const { default: Series } = await import('@/models/Series');
      const { default: Episode } = await import('@/models/Episode');
      const { default: IMDbEpisodeScraper } = await import('@/lib/imdbEpisodeScraper');

      // Get all series from database (no type filter - Series collection only contains series)
      const allSeries = await Series.find({}, { imdbId: 1, title: 1, tmdbId: 1, _id: 1 }).lean();
      console.log(`📊 Found ${allSeries.length} series in database to process for comprehensive episode sync`);

      if (allSeries.length === 0) {
        const duration = (Date.now() - startTime) / 1000;
        return {
          success: true,
          seriesProcessed: 0,
          episodesAdded: 0,
          duration: `${duration}s`
        };
      }

      let seriesProcessed = 0;
      let totalEpisodesAdded = 0;
      const batchSize = 10; // Process 10 series at a time to avoid overwhelming IMDb
      const imdbScraper = IMDbEpisodeScraper.getInstance();

      console.log(`🚀 OPTIMIZED PROCESSING: ${batchSize} series at a time with rate limiting for IMDb compatibility`);

      // Process series in large parallel batches
      for (let i = 0; i < allSeries.length; i += batchSize) {
        const batch = allSeries.slice(i, i + batchSize);
        const batchNumber = Math.floor(i/batchSize) + 1;
        const totalBatches = Math.ceil(allSeries.length/batchSize);

        console.log(`⚡ Processing PARALLEL batch ${batchNumber}/${totalBatches} (${batch.length} series simultaneously)`);
        const batchStartTime = Date.now();

        const batchPromises = batch.map(async (series, index) => {
          try {
            const seriesStartTime = Date.now();
            console.log(`📺 [${index + 1}/${batch.length}] Processing: ${series.title} (${series.imdbId})`);

            // First, get existing episodes to check if series is already complete
            const existingEpisodes = await Episode.find({ imdbId: series.imdbId }).lean();

            // Create existing episodes map for fast lookup
            const existingEpisodeMap = new Set<string>();
            existingEpisodes.forEach(ep => {
              existingEpisodeMap.add(`S${ep.season}E${ep.episode}`);
            });

            console.log(`🔍 ${series.title}: Checking ${existingEpisodes.length} existing episodes...`);

            // Get IMDb episode data to check completeness
            const imdbEpisodeData = await imdbScraper.getSeriesEpisodes(series.imdbId);

            if (!imdbEpisodeData) {
              console.log(`⚠️ No IMDb data: ${series.title}`);
              return { success: false, episodesAdded: 0, seriesTitle: series.title };
            }

            // Check if series is already complete (optimization)
            const totalImdbEpisodes = imdbEpisodeData.totalEpisodes;
            const existingCount = existingEpisodes.length;

            if (existingCount >= totalImdbEpisodes && totalImdbEpisodes > 0) {
              console.log(`⏭️ ${series.title}: Already complete (${existingCount}/${totalImdbEpisodes} episodes) - SKIPPING`);
              return {
                success: true,
                episodesAdded: 0,
                seriesTitle: series.title,
                duration: (Date.now() - seriesStartTime) / 1000
              };
            }

            console.log(`🎯 ${series.title}: ${totalImdbEpisodes} total episodes (${existingCount} existing, ${totalImdbEpisodes - existingCount} missing)`);

            // Prepare episode operations for parallel processing (ONLY missing episodes)
            const episodeOperations = [];
            const newEpisodeKeys = [];

            // Process ONLY missing episodes from IMDb data (optimization)
            for (const season of imdbEpisodeData.seasons) {
              for (const episode of season.episodes) {
                const episodeKey = `S${episode.season}E${episode.episode}`;

                // OPTIMIZATION: Skip if episode already exists
                if (existingEpisodeMap.has(episodeKey)) {
                  continue; // Skip existing episodes - no processing needed
                }

                // Generate VidSrc embed URLs for missing episodes only
                const vidsrcEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${episode.season}&episode=${episode.episode}`;
                const vidsrcTmdbUrl = series.tmdbId ? `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId}&season=${episode.season}&episode=${episode.episode}` : '';

                // Create episode data with explicit field control (no seriesId)
                const episodeData = {
                  imdbId: series.imdbId,
                  tmdbId: series.tmdbId || undefined,
                  seriesTitle: series.title,
                  season: episode.season,
                  episode: episode.episode,
                  episodeTitle: episode.title || `Episode ${episode.episode}`,
                  description: episode.description || undefined,
                  airDate: episode.airDate ? new Date(episode.airDate) : undefined,
                  runtime: episode.runtime || '45 min',
                  embedUrl: vidsrcEmbedUrl,
                  embedUrlTmdb: vidsrcTmdbUrl || undefined,
                  vidsrcUrl: vidsrcEmbedUrl,
                  vidsrcTmdbUrl: vidsrcTmdbUrl || undefined
                };

                // Add to parallel operations (only for missing episodes)
                episodeOperations.push(
                  Episode.findOneAndUpdate(
                    {
                      imdbId: series.imdbId,
                      season: episode.season,
                      episode: episode.episode
                    },
                    episodeData,
                    {
                      upsert: true,
                      new: true
                    }
                  )
                );

                // Track new episodes
                newEpisodeKeys.push(episodeKey);
              }
            }

            // Execute episode operations in parallel (only for missing episodes)
            if (episodeOperations.length > 0) {
              console.log(`📝 Processing ${episodeOperations.length} missing episodes for ${series.title}...`);
              await Promise.allSettled(episodeOperations);
            } else {
              console.log(`⚡ No missing episodes to process for ${series.title}`);
            }

            const seriesDuration = (Date.now() - seriesStartTime) / 1000;
            console.log(`✅ ${series.title}: ${newEpisodeKeys.length} new episodes added in ${seriesDuration}s`);

            return {
              success: true,
              episodesAdded: newEpisodeKeys.length,
              seriesTitle: series.title,
              duration: seriesDuration
            };

          } catch (error) {
            console.error(`❌ Error processing series ${series.title}:`, error);
            return { success: false, episodesAdded: 0, seriesTitle: series.title };
          }
        });

        // Execute all series in parallel and wait for completion
        const batchResults = await Promise.allSettled(batchPromises);
        const batchDuration = (Date.now() - batchStartTime) / 1000;

        // Count successful series and episodes
        let batchSuccessful = 0;
        let batchEpisodesAdded = 0;

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled' && result.value.success) {
            batchSuccessful++;
            batchEpisodesAdded += result.value.episodesAdded;
            seriesProcessed++;
            totalEpisodesAdded += result.value.episodesAdded;
          } else {
            const seriesTitle = batch[index]?.title || 'Unknown';
            console.error(`❌ Failed: ${seriesTitle}`);
          }
        });

        console.log(`⚡ Batch ${batchNumber} completed: ${batchSuccessful}/${batch.length} series, ${batchEpisodesAdded} episodes in ${batchDuration}s`);

        // Respectful delay between batches to avoid overwhelming IMDb
        if (i + batchSize < allSeries.length) {
          console.log('⏳ Respectful pause before next batch to avoid rate limiting...');
          await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay between batches
        }
      }

      const duration = (Date.now() - startTime) / 1000;

      console.log(`🎉 COMPREHENSIVE EPISODE SYNC COMPLETED!`);
      console.log(`📊 Results: ${seriesProcessed}/${allSeries.length} series processed`);
      console.log(`📺 Episodes: ${totalEpisodesAdded} new episodes added to database`);
      console.log(`⏱️ Duration: ${duration}s`);

      return {
        success: true,
        seriesProcessed,
        episodesAdded: totalEpisodesAdded,
        duration: `${duration}s`
      };

    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.error('❌ Comprehensive series episodes sync error:', error);
      return {
        success: false,
        seriesProcessed: 0,
        episodesAdded: 0,
        duration: `${duration}s`,
        error: error.message
      };
    }
  }

  /**
   * Update sync status in database
   */
  private async updateSyncStatus(isRunning: boolean, results?: UniversalSyncResult, nextSyncTime?: Date): Promise<void> {
    try {
      await connectDB();
      const { default: SyncStatus } = await import('@/models/SyncStatus');

      const now = new Date();
      const updateData: any = {
        isRunning,
        updatedAt: now
      };

      if (!isRunning) {
        updateData.lastSyncTime = now;
        if (results) {
          updateData.lastResult = results;
        }
        if (nextSyncTime) {
          updateData.nextSyncTime = nextSyncTime;
        } else {
          updateData.nextSyncTime = this.calculateNextSyncTime(now);
        }
      }

      await SyncStatus.updateOne(
        { syncType: this.SYNC_TYPE },
        updateData,
        { upsert: true }
      );

      console.log(`📊 Updated Universal Sync status: isRunning=${isRunning}`);
    } catch (error) {
      console.error('❌ Error updating sync status:', error);
      throw error;
    }
  }

  /**
   * Get current sync status
   */
  public async getSyncStatus(): Promise<any> {
    try {
      await connectDB();
      const { default: SyncStatus } = await import('@/models/SyncStatus');
      return await SyncStatus.findOne({ syncType: this.SYNC_TYPE });
    } catch (error) {
      console.error('❌ Error getting sync status:', error);
      return null;
    }
  }

  /**
   * Force manual sync
   */
  public async forceSync(): Promise<UniversalSyncResult> {
    console.log('🔧 Manual universal sync requested...');
    return this.runUniversalSync();
  }

  /**
   * Destroy the service
   */
  public destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.isInitialized = false;
    console.log('🛑 Universal Sync Service destroyed');
  }
}

export default UniversalSyncService;
